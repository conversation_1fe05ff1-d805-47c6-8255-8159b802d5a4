# 🐳 Docker Setup Guide for Knot Core Refactored

This guide will help you set up the complete development and production environment using Docker with PostgreSQL, Redis, secure token management, and the application.

## 🆕 What's New - Secure Token Management System

The application now includes a comprehensive secure token management system with:

- **AES-256-GCM Encryption** for all OAuth tokens
- **Automatic token refresh** before expiration
- **Admin API endpoints** for token management
- **Multi-platform support** (Etsy, Shopify, Amazon)
- **Complete audit trail** and monitoring

## 📋 Prerequisites

- Docker Desktop installed and running
- Your Shopify and Etsy API credentials

## 🚀 Quick Start

### 1. Environment Configuration

First, update the `.env` file with your actual API credentials:

```bash
# Copy the example file (already done)
# cp .env.example .env

# Edit the .env file and replace these values:
ETSY_API_KEY=your-actual-etsy-api-key
ETSY_SECRET_KEY=your-actual-etsy-secret-key
ETSY_SHOP_ID=your-actual-etsy-shop-id
ETSY_ACCESS_TOKEN=your-actual-etsy-access-token
ETSY_REFRESH_TOKEN=your-actual-etsy-refresh-token

SHOPIFY_SHOP_DOMAIN=your-shop.myshopify.com
SHOPIFY_ACCESS_TOKEN=your-actual-shopify-access-token
```

### 2. Start the Development Environment

```bash
# Make the script executable (already done)
chmod +x scripts/start-dev.sh

# Start all services
./scripts/start-dev.sh
```

Or manually with Docker Compose:

```bash
# Build and start all services
docker-compose up --build -d

# View logs
docker-compose logs -f app
```

## 🌐 Service URLs

Once running, you can access:

- **Main Application**: <http://localhost:3001>
- **API Documentation**: <http://localhost:3001/api>
- **PgAdmin (Database UI)**: <http://localhost:8080>
  - Email: `<EMAIL>`
  - Password: `admin123`
- **Redis Commander**: <http://localhost:8081>

## 📊 Testing the Setup

### 1. Health Check

```bash
curl http://localhost:3001/api/health
```

### 2. Login as Admin (Required for Token Management)

```bash
# Login to get admin token
ADMIN_TOKEN=$(curl -X POST "http://localhost:3001/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "AdminPassword123!"
  }' | jq -r '.data.accessToken')

echo "Admin Token: $ADMIN_TOKEN"
```

### 3. Store Etsy Tokens Securely

```bash
# Store current Etsy tokens in secure encrypted storage
curl -X POST "http://localhost:3001/api/v1/platform/tokens/etsy/store-current" \
  -H "Authorization: Bearer $ADMIN_TOKEN" \
  -H "Content-Type: application/json"
```

### 4. Check Token Status

```bash
# Check Etsy token status
curl -X GET "http://localhost:3001/api/v1/platform/tokens/etsy/status?shopId=6507168" \
  -H "Authorization: Bearer $ADMIN_TOKEN"

# Get token statistics
curl -X GET "http://localhost:3001/api/v1/platform/tokens/statistics" \
  -H "Authorization: Bearer $ADMIN_TOKEN"
```

### 5. Sync Orders from Etsy

```bash
curl -X POST "http://localhost:3001/api/v1/platform/sync/orders/etsy" \
  -H "Content-Type: application/json"
```

### 6. Get Orders with Pagination

```bash
# Get all orders
curl http://localhost:3001/api/v1/orders

# Get orders with filters
curl "http://localhost:3001/api/v1/orders?platform=ETSY&page=1&limit=10"

# Get recent orders
curl http://localhost:3001/api/v1/orders/recent
```

### 7. Get Order Statistics

```bash
curl http://localhost:3001/api/v1/orders/stats
```

## 🔧 Available API Endpoints

### Authentication Endpoints

- `POST /api/v1/auth/login` - Login to get access token
- `POST /api/v1/auth/register` - Register new user
- `POST /api/v1/auth/logout` - Logout user

### Token Management Endpoints (Admin Only)

- `POST /api/v1/platform/tokens/:platform` - Store OAuth tokens for a platform
- `GET /api/v1/platform/tokens/:platform/status` - Get token status for a platform
- `POST /api/v1/platform/tokens/:platform/refresh` - Manually refresh tokens
- `DELETE /api/v1/platform/tokens/:platform` - Revoke tokens for a platform
- `GET /api/v1/platform/tokens/statistics` - Get token statistics
- `POST /api/v1/platform/tokens/etsy/store-current` - Store current Etsy tokens

### Platform Sync Endpoints

- `POST /api/v1/platform/sync/orders/:platform` - Sync orders for specific platform
- `GET /api/v1/platform/status` - Get platform status

### Order Endpoints

- `GET /api/v1/orders` - Get orders with pagination and filters
- `GET /api/v1/orders/stats` - Get order statistics
- `GET /api/v1/orders/recent` - Get recent orders

### Query Parameters for Orders

- `page` - Page number (default: 1)
- `limit` - Items per page (default: 20, max: 100)
- `platform` - Filter by platform (SHOPIFY, ETSY, AMAZON)
- `status` - Filter by order status
- `startDate` - Start date (ISO string)
- `endDate` - End date (ISO string)
- `customerEmail` - Filter by customer email
- `minAmount` - Minimum order amount
- `maxAmount` - Maximum order amount

## 🛠️ Development Commands

```bash
# View all service logs
docker-compose logs -f

# View app logs only
docker-compose logs -f app

# Restart the app service
docker-compose restart app

# Stop all services
docker-compose down

# Stop and remove volumes (clean slate)
docker-compose down -v

# Access the app container
docker-compose exec app sh

# Access the database
docker-compose exec postgres psql -U postgres -d knot_core
```

## 🐛 Troubleshooting

### Database Connection Issues

```bash
# Check if PostgreSQL is running
docker-compose ps postgres

# Check PostgreSQL logs
docker-compose logs postgres

# Restart PostgreSQL
docker-compose restart postgres
```

### Redis Connection Issues

```bash
# Check if Redis is running
docker-compose ps redis

# Test Redis connection
docker-compose exec redis redis-cli ping
```

### Application Issues

```bash
# Check app logs
docker-compose logs app

# Restart the app
docker-compose restart app

# Rebuild the app
docker-compose up --build app
```

### API Credential Issues

1. Verify your `.env` file has the correct credentials
2. Check that your Shopify/Etsy tokens haven't expired
3. Ensure your API permissions include order read access

## 🔐 Secure Token Management System

The application now includes a comprehensive secure token management system:

### Features

- **AES-256-GCM Encryption**: All OAuth tokens are encrypted at rest
- **Automatic Token Refresh**: Tokens are automatically refreshed 5 minutes before expiration
- **Token Lifecycle Management**: Complete tracking of token status, usage, and expiration
- **Admin API**: Full token management through secure API endpoints
- **Multi-Platform Support**: Supports Etsy, Shopify, and Amazon tokens
- **Audit Trail**: Complete history of token operations

### Security

- Tokens are encrypted using AES-256-GCM with random IVs
- Encryption key is stored in environment variables
- Only admin users can access token management endpoints
- Automatic cleanup of old tokens (90-day retention)

### Automatic Features

- **Auto-Migration**: Existing environment tokens are automatically migrated to secure storage
- **Auto-Refresh**: Tokens refresh automatically via cron job (every 5 minutes)
- **Health Monitoring**: Token status tracking and failure detection
- **Error Handling**: Comprehensive retry logic with exponential backoff

## 📝 Notes

- The database will automatically create tables on first run (DB_SYNCHRONIZE=true)
- OAuth tokens are now stored securely in the `oauth_tokens` table with encryption
- The app runs in development mode with hot reload
- All data persists in Docker volumes between restarts
- Token management requires admin authentication

## 🔄 Workflow for Testing with Secure Token Management

1. **Start the environment**: `./scripts/start-dev.sh`
2. **Login as admin**: Get admin token using the login endpoint
3. **Store tokens securely**: Use the token management API to store OAuth tokens
4. **Verify token status**: Check that tokens are stored and valid
5. **Sync orders**: `POST /api/v1/platform/sync/orders/etsy`
6. **View orders**: `GET /api/v1/orders`
7. **Test filters**: `GET /api/v1/orders?platform=ETSY&limit=5`

### Complete Example Workflow

```bash
# 1. Start environment
./scripts/start-dev.sh

# 2. Get admin token
ADMIN_TOKEN=$(curl -s -X POST "http://localhost:3001/api/v1/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "AdminPassword123!"}' \
  | jq -r '.data.accessToken')

# 3. Store Etsy tokens securely
curl -X POST "http://localhost:3001/api/v1/platform/tokens/etsy/store-current" \
  -H "Authorization: Bearer $ADMIN_TOKEN"

# 4. Check token status
curl -X GET "http://localhost:3001/api/v1/platform/tokens/etsy/status?shopId=6507168" \
  -H "Authorization: Bearer $ADMIN_TOKEN"

# 5. Sync orders from Etsy
curl -X POST "http://localhost:3001/api/v1/platform/sync/orders/etsy"

# 6. View synced orders
curl "http://localhost:3001/api/v1/orders?platform=ETSY&limit=10"
```

The system will securely manage OAuth tokens, automatically refresh them when needed, and provide encrypted storage with comprehensive audit trails.

## 🏭 Production Deployment

### Prerequisites for Production

1. **Server Requirements**:
   - Docker and Docker Compose installed
   - At least 2GB RAM and 20GB disk space
   - SSL certificates for HTTPS
   - Domain name configured

2. **Security Requirements**:
   - Generate a secure encryption key for token management
   - Use strong passwords for all services
   - Configure firewall rules
   - Set up SSL/TLS certificates

### Production Deployment Steps

1. **Prepare Environment**:

   ```bash
   # Copy production environment template
   cp .env.production .env.production.local

   # Generate secure encryption key
   node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"

   # Edit .env.production.local with your values
   nano .env.production.local
   ```

2. **Configure SSL Certificates**:

   ```bash
   # Create SSL directory
   mkdir -p docker/nginx/ssl

   # Copy your SSL certificates
   cp your-cert.pem docker/nginx/ssl/cert.pem
   cp your-key.pem docker/nginx/ssl/key.pem
   ```

3. **Deploy to Production**:

   ```bash
   # Run the production deployment script
   ./scripts/deploy-production.sh
   ```

### Production Environment Variables

Key variables to configure in `.env.production`:

```bash
# CRITICAL: Generate a new encryption key for production
ENCRYPTION_KEY=your-32-byte-hex-key-here

# Database (use managed database service in production)
DATABASE_HOST=your-production-db-host
DATABASE_PASSWORD=your-secure-db-password

# Redis (use managed Redis service in production)
REDIS_HOST=your-redis-host
REDIS_PASSWORD=your-secure-redis-password

# JWT Secret (generate a new one for production)
JWT_SECRET=your-super-secure-jwt-secret

# API Credentials (your actual production tokens)
SHOPIFY_ACCESS_TOKEN=your-production-shopify-token
ETSY_API_KEY=your-production-etsy-api-key
```

### Production Services

The production setup includes:

- **Application**: Optimized production build with health checks
- **PostgreSQL**: Production database with persistence
- **Redis**: Production cache with persistence
- **Nginx**: Reverse proxy with SSL termination and rate limiting
- **Prometheus**: Monitoring and metrics collection

### Production Commands

```bash
# Deploy to production
./scripts/deploy-production.sh

# View production logs
docker-compose -f docker-compose.prod.yml logs -f

# Scale the application (if needed)
docker-compose -f docker-compose.prod.yml up -d --scale app=3

# Update the application
docker-compose -f docker-compose.prod.yml pull
docker-compose -f docker-compose.prod.yml up -d

# Backup database
docker-compose -f docker-compose.prod.yml exec postgres pg_dump -U postgres knot_core_prod > backup.sql
```
