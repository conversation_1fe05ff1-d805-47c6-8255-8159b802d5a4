# Compiled output
/dist
/node_modules
*.tsbuildinfo

# Logs
logs
*.log
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store
Thumbs.db

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Docker
.dockerignore

# Database
*.sqlite
*.db

# Cache
.cache
.parcel-cache

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# Build artifacts
build/
dist/

# Package manager
package-lock.json
yarn.lock
pnpm-lock.yaml

# TypeScript
*.tsbuildinfo

# ESLint
.eslintcache

# Prettier
.prettierignore

# Husky
.husky/_

# Local development
.local/
local/

# Documentation
docs/build/

# Backup files
*.backup
*.bak

# API Keys and secrets (additional safety)
**/secrets/
**/*.key
**/*.pem
**/*.p12
**/*.pfx

# Production files
production.env
prod.env
