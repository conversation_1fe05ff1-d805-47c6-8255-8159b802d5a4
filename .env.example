# Application Configuration
NODE_ENV=development
PORT=3000
APP_NAME=knot-core-refactored
APP_VERSION=2.0.0

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_DATABASE=knot_core
DB_SYNCHRONIZE=false
DB_LOGGING=false

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=redis123
REDIS_DB=0

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRATION_TIME=3600
JWT_PRIVATE_KEY=your-private-key
JWT_PUBLIC_KEY=your-public-key

# Amazon API Configuration
AMAZON_ACCESS_KEY_ID=your-amazon-access-key
AMAZON_SECRET_ACCESS_KEY=your-amazon-secret-key
AMAZON_REGION=us-east-1
AMAZON_MARKETPLACE_ID=your-marketplace-id
AMAZON_SELLER_ID=your-seller-id

# Etsy API Configuration
ETSY_API_KEY=your-etsy-api-key
ETSY_SECRET_KEY=your-etsy-secret-key
ETSY_SHOP_ID=your-etsy-shop-id
ETSY_ACCESS_TOKEN=your-etsy-access-token
ETSY_REFRESH_TOKEN=your-etsy-refresh-token

# Shopify API Configuration
SHOPIFY_SHOP_DOMAIN=your-shop.myshopify.com
SHOPIFY_ACCESS_TOKEN=your-shopify-access-token
SHOPIFY_API_VERSION=2023-10

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# Rate Limiting
RATE_LIMIT_TTL=60
RATE_LIMIT_LIMIT=100

# Development Tools
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=admin123
PGADMIN_PORT=8080
REDIS_COMMANDER_PORT=8081

# Health Check Configuration
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_INTERVAL=30000

# Queue Configuration
QUEUE_REDIS_HOST=localhost
QUEUE_REDIS_PORT=6379
QUEUE_REDIS_PASSWORD=redis123
QUEUE_REDIS_DB=1

# Monitoring Configuration
METRICS_ENABLED=true
METRICS_PORT=9090
