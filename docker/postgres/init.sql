-- Initialize knot_core database
-- This script runs when the PostgreSQL container starts for the first time

-- Create database if it doesn't exist
SELECT 'CREATE DATABASE knot_core'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'knot_core')\gexec

-- Connect to the knot_core database
\c knot_core;

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create indexes for better performance
-- These will be created by TypeORM, but we can add custom ones here if needed

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE knot_core TO postgres;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO postgres;

-- Log completion
SELECT 'Database initialization completed' as status;
