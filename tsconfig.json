{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2022", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strict": false, "noImplicitAny": false, "strictNullChecks": false, "strictFunctionTypes": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": false, "noUncheckedIndexedAccess": false, "exactOptionalPropertyTypes": false, "noImplicitOverride": false, "forceConsistentCasingInFileNames": true, "paths": {"@/*": ["src/*"], "@domain/*": ["src/domain/*"], "@application/*": ["src/application/*"], "@infrastructure/*": ["src/infrastructure/*"], "@presentation/*": ["src/presentation/*"], "@shared/*": ["src/shared/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "test"]}