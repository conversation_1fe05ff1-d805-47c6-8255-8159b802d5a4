/* eslint-disable @typescript-eslint/naming-convention */
import { decode } from 'html-entities';
import { first, last, capitalize } from 'lodash';
import moment from 'moment-timezone';
import { IMoney, IShopReceipt, IShopReceiptTransaction } from 'etsy-ts/v3';

import { OrderItem } from './types';
import { timeZone } from './settings';
import { parseSku, extractCompleteOrderData } from './skuUtil';

export const transformEtsyReceipt = (receipt: IShopReceipt): OrderItem[] => {
  const transformed: OrderItem[] = [];

  const {
    receipt_id,
    created_timestamp,
    is_shipped: isShipped,
    buyer_email,
    message_from_buyer,
    transactions,
    name,
    subtotal,
    first_line,
    second_line,
    city,
    country_iso,
    state,
    zip,
  } = receipt as Required<IShopReceipt>;
  const orderValue = subtotal as Required<IMoney>;

  const isMixedOrder =
    transactions.some((t) => t.title?.toLowerCase().includes('engraved')) &&
    transactions.some((t) => !t.title?.toLowerCase().includes('engraved'));

  transactions.forEach((transaction, index) => {
    const itemId = `E${receipt_id}.${index}`;
    const orderId = `E${receipt_id}`;
    const createdTimeLocal = moment(created_timestamp * 1000)
      .tz(timeZone)
      .format('YYYY-MM-DD hh:mm');
    const message = decode(message_from_buyer);
    const {
      title: nullableTitle,
      quantity,
      sku,
      variations,
    } = transaction as Required<IShopReceiptTransaction>;
    if (!sku) return;

    const title = nullableTitle ?? '';

    // Get initial values from SKU parsing
    let { style, color, size, design } = parseSku(sku);

    // order items
    const orderItemCount = transactions.length;
    const orderItemIndex = index;

    // customer "short name" and target filename
    const shippingNameParts = name.trim().split(' ');
    const shippingShortName = `${capitalize(first(shippingNameParts))}${
      capitalize(last(shippingNameParts))[0]
    }`;
    const targetFileName = `${orderId}-${shippingShortName}${
      orderItemCount > 1 ? `-${orderItemIndex + 1}of${orderItemCount}` : ''
    }`;

    // address and value
    const chitchatsAddress: OrderItem['chitchatsAddress'] = {
      name,
      address_1: first_line,
      address_2: second_line ?? '',
      city,
      country_code: country_iso,
      province_code: state ?? '',
      postal_code: zip,
      phone: '', // no phone
    };

    // variations and engraving
    let engravingSide: string | undefined;
    let personalization: string | undefined;
    const customFields: OrderItem['customFields'] = [];

    variations.forEach(({ property_id, formatted_name, formatted_value }) => {
      const decodedName = decode(formatted_name);
      const decodedValue = decode(formatted_value);

      if (property_id === 54) {
        // 54 = personalization
        personalization = decodedValue;
        if (personalization === 'Not requested on this item.')
          personalization = undefined;
      }
      if (property_id === 513) {
        // 513 = engraving side
        engravingSide = decodedValue;
      }
      if (property_id === 514 && formatted_value === 'Yes') {
        // 513 = Inside Engraving?
        engravingSide = 'inside';
      }
      customFields.push({
        label: decodedName,
        value: decodedValue,
      });
    });

    // Use enhanced SKU extractor to fill in any missing fields
    const extractedData = extractCompleteOrderData({
      style,
      color,
      size,
      design,
      sku,
      title,
      source: 'etsy',
    });

    // Update fields with extracted data
    style = extractedData.style || style;
    color = extractedData.color || color;
    size = extractedData.size || size;
    design = extractedData.design || design;

    // Extract inlay type from SKU (Etsy typically doesn't have ink-color properties)
    // Priority: 1) ink-color property (if exists), 2) SKU extraction
    let inkColor;

    // Check if there's an ink-color property in the variations
    const inkColorProperty = variations.find(
      (v) =>
        v.formatted_name?.toLowerCase().includes('ink-color') ||
        v.formatted_name?.toLowerCase().includes('ink color')
    );

    if (inkColorProperty?.formatted_value) {
      inkColor = inkColorProperty.formatted_value.toLowerCase();
      console.log(`[Etsy] Set ink color from property: ${inkColor}`);
    } else if (extractedData.inlayType) {
      inkColor = extractedData.inlayType;
      console.log(
        `[Etsy] Set ink color from extracted inlay type: ${extractedData.inlayType}`
      );
    }

    let outside = {};
    let inside = {};
    if (design) {
      // Strip design prefixes (cs, gs, ss) for the outside object
      const cleanDesign = design.replace(/^(cs|gs|ss)/, '');
      outside = { design: cleanDesign };
    }
    if (engravingSide) {
      const engravingSideLowercase = engravingSide.toLowerCase();

      if (
        engravingSideLowercase.includes('inside') &&
        !engravingSideLowercase.includes('outside')
      ) {
        inside = { text: personalization };
      } else if (
        engravingSideLowercase.includes('outside') &&
        !engravingSideLowercase.includes('inside')
      ) {
        outside = { ...outside, text: personalization };
      } else if (
        engravingSideLowercase.includes('outside') &&
        engravingSideLowercase.includes('inside')
      ) {
        outside = { ...outside, text: '?' };
        inside = { text: '?' };
      }
    }

    const isEngraved =
      title.toLowerCase().includes('engraved') ||
      sku.toLowerCase().includes('trinity') ||
      (typeof outside === 'object' && Object.keys(outside).length > 0) ||
      (typeof inside === 'object' && Object.keys(inside).length > 0);

    transformed.push({
      // itemId
      itemId,
      // order
      createdTimeLocal,
      orderId,
      orderItemCount,
      orderItemIndex,
      orderItemNumberOfTotal:
        orderItemCount > 1 ? `${orderItemIndex + 1}/${orderItemCount}` : '',
      isShipped,
      message,
      isMixedOrder,
      isCancelled: false, // looks like API v3 just ignores cancelled orders?
      orderValue: orderValue.amount / orderValue.divisor,
      orderCurrency: orderValue.currency_code,
      // customer
      name,
      shippingShortName,
      email: buyer_email ?? '',
      // shipping
      chitchatsAddress,
      // item
      title,
      quantity,
      isEngraved,
      sku,
      style: style ?? '',
      color: color ?? '',
      size: size ?? '',
      design: design ?? '',
      inkColor,
      customFields,
      personalization,
      outside,
      inside,
      // file
      targetFileName,
      // source data
      source: 'etsy',
      etsyReceipt: receipt as Required<IShopReceipt>,
      etsyTransaction: transaction as Required<IShopReceiptTransaction>,
    });
  });

  return transformed;
};
