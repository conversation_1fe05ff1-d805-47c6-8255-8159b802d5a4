/* eslint-disable no-console */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/naming-convention */
import axios from 'axios';
import { Etsy } from 'etsy-ts/v3';
import moment from 'moment';
import { flatten } from 'lodash';

import { store } from './store';
import { selectGeneralConfig } from './selectors';
import { transformEtsyReceipt } from './etsyUtil';
import { OrderItem } from './types';
import { initAuthRefresh, refreshAuthLogic } from './etsyInitAuthRefresh';

let v3Client: Etsy;

export const v3InitClient = async () => {
  const { etsyApiKey, etsyRefreshToken } = selectGeneralConfig(
    store.getState()
  );

  // localStorage.setItem('etsyRefreshToken', etsyRefreshToken);
  // const refreshToken = localStorage.getItem('etsyRefreshToken') || '';

  await refreshAuthLogic(etsyApiKey, etsyRefreshToken);
  let accessToken = localStorage.getItem('etsyAccessToken');
  if (!accessToken) await refreshAuthLogic(etsyApiKey, etsyRefreshToken);
  // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
  accessToken = localStorage.getItem('etsyAccessToken')!;

  v3Client = new Etsy({ apiKey: etsyApiKey, accessToken });
  initAuthRefresh(v3Client, etsyApiKey, etsyRefreshToken);
};

export const getEtsyReceipts = async ({
  fromDate,
  toDate,
  onlyOpen,
  canceled,
  onProgress,
}: {
  fromDate: string;
  toDate: string;
  onlyOpen: boolean;
  canceled: boolean;
  onProgress?: (payload: OrderItem[]) => void;
}) => {
  const { etsyShopId } = selectGeneralConfig(store.getState());

  if (!v3Client) await v3InitClient();

  const params = {
    min_created: Math.trunc(Number(moment(fromDate).format('X'))),
    max_created: Math.trunc(Number(moment(toDate).endOf('day').format('X'))),
    was_shipped: undefined as boolean | undefined,
  };
  if (onlyOpen) params.was_shipped = false;

  const completePayload = [];

  // eslint-disable-next-line prefer-const
  let limit = 25;
  let page: number | null = 1;

  while (page !== null) {
    store.dispatch({ type: 'requests/increment' });
    // eslint-disable-next-line no-await-in-loop
    const result = await v3Client.ShopReceipt.getShopReceipts({
      shopId: etsyShopId,
      limit,
      offset: (page - 1) * limit,
      ...params,
    });
    store.dispatch({ type: 'requests/decrement' });

    const { data } = result;

    if (typeof data.count === 'undefined') throw new Error('no count');
    if (data.count <= page * limit) {
      page = null;
    } else {
      page += 1;
    }
    if (!data.results) throw new Error('no results');

    if (!data.results) throw new Error('no results');
    const payload = flatten(data.results.map(transformEtsyReceipt));
    completePayload.push(...payload);

    if (onProgress) {
      onProgress(payload);
    }

    store.dispatch({ type: 'items/get.progress', payload });
  }

  return completePayload;
};

export const getEtsyReceipt = async ({
  orderId,
  onProgress,
}: {
  orderId: string;
  onProgress?: (payload: OrderItem[]) => void;
}) => {
  const { etsyShopId } = selectGeneralConfig(store.getState());

  if (!v3Client) await v3InitClient();

  store.dispatch({ type: 'requests/increment' });
  const result = await v3Client.ShopReceipt.getShopReceipt(
    etsyShopId,
    Number(orderId.replace('E', ''))
  );

  store.dispatch({ type: 'requests/decrement' });

  const { data } = result;
  if (!data) throw new Error('no results');

  const payload = transformEtsyReceipt(data);

  if (onProgress) {
    onProgress(payload);
  }

  store.dispatch({ type: 'items/get.progress', payload });
  return payload;
};

export const requestAuthorizationCode = async () => {
  /*
  STEP 1: Request an authorization code
https://developer.etsy.com/documentation/essentials/authentication#step-1-request-an-authorization-code
  */

  const params = new URLSearchParams();
  params.append('response_type', 'code');
  params.append('client_id', '8lky9udgc981qh7h1vqipih6');
  params.append(
    'redirect_uri',
    process.env.ETSY_REDIRECT_URI ||
      'https://0ppyvbq3r4.execute-api.ca-central-1.amazonaws.com'
  );
  params.append(
    'scope',
    ['shops_r', 'shops_w', 'transactions_r', 'transactions_w'].join(' ')
  );
  params.append('state', 'STATE');
  params.append(
    'code_challenge',
    'DSWlW2Abh-cf8CeLL8-g3hQ2WQyYdKyiu83u_s7nRhI'
  );
  params.append('code_challenge_method', 'S256');

  console.log(
    `Go to: ${
      process.env.ETSY_OAUTH_BASE_URL || 'https://www.etsy.com/oauth/connect'
    }?${params.toString()}`
  );
};

export const requestAccessToken = async (code: string) => {
  /*
  STEP 3: Request an access token
https://developer.etsy.com/documentation/essentials/authentication#step-3-request-an-access-token
  */

  const formData = new FormData();
  formData.append('grant_type', 'authorization_code');
  formData.append('client_id', '8lky9udgc981qh7h1vqipih6');
  formData.append(
    'redirect_uri',
    process.env.ETSY_REDIRECT_URI ||
      'https://0ppyvbq3r4.execute-api.ca-central-1.amazonaws.com'
  );
  formData.append('code', code);
  formData.append(
    'code_verifier',
    'vvkdljkejllufrvbhgeiegrnvufrhvrffnkvcknjvfid'
  );

  const response = await axios({
    method: 'POST',
    url:
      process.env.ETSY_API_BASE_URL ||
      'https://api.etsy.com/v3/public/oauth/token',
    data: formData,
  });

  console.log('New refresh token:', response.data.refresh_token);
};
