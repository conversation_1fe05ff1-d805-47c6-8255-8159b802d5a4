import { find, uniqBy, uniq } from 'lodash';

const getItems = (state) => state.items;
const getItemsFileData = (state) => state.itemsFileData;
const getItemsCustomData = (state) => state.itemsCustomData;
const getItemsAiData = (state) => state.itemsAiData;
export const selectItemsLocalData = (state) => state.itemsLocalData;
export const selectIsItemsLocalDataLoaded = (state) =>
  state.isItemsLocalDataLoaded;
export const selectErrors = (state) => state.errors;

export const selectGeneralConfig = (state) => state.config.general;

// export const selectGeneralConfig = (state) => ({
//   ...state.config.general,
//   customApiKey: 'KEY_HERE',
// });

export const selectItems = (state) => {
  const items = getItems(state);
  const itemsFileData = getItemsFileData(state);
  const itemsCustomData = getItemsCustomData(state);
  const itemsAiData = getItemsAiData(state);
  const itemsLocalData = selectItemsLocalData(state);
  const { customApiKey } = selectGeneralConfig(state);

  const result = [];

  items.forEach((item) => {
    const { personalization, message, style, size, color } = item;
    const isReady = style && size && color && !personalization && !message;

    const itemCustomData = find(itemsCustomData, { itemId: item.itemId });

    if (customApiKey && !itemCustomData) return; // if using custom API, require custom data to show item

    result.push({
      ...item,
      isReady, // may be overridden by custom data below
      ...find(itemsFileData, { itemId: item.itemId }),
      ...find(itemsAiData, { itemId: item.itemId }),
      ...find(itemsLocalData, { itemId: item.itemId }),
      ...itemCustomData,
    });
  });

  return result;
};

export const selectRingOptions = (state) => {
  const blankOption = [
    {
      label: '',
      value: '',
    },
  ];

  const { ringSpecs, colors: configColors } = state.config;

  const styles = blankOption
    .concat(uniqBy(ringSpecs, 'style'))
    .map(({ style }) => ({
      value: style,
      label: style,
    }));
  const sizes = blankOption
    .concat(uniqBy(ringSpecs, 'size'))
    .map(({ size }) => ({
      value: size,
      label: size,
    }));
  const colors = blankOption.concat(
    configColors.map(({ code, name }) => ({ value: code, label: name }))
  );
  return {
    styles,
    sizes,
    colors,
  };
};

export const selectRingSpec = (state, { style, size }) => {
  console.log('selectRingSpec', { style, size });
  console.log('selectRingSpec', state.config.ringSpecs);
  return find(state.config.ringSpecs, { style, size });
};

export const selectDesignSpecByCode = (state, code) => {
  return find(state.config.designSpecs, { code });
};

export const selectDesignSpecs = (state) => state.config.designSpecs;

export const selectPostageTypes = (state) => state.config.postageTypes;

export const selectInlayDesigns = (state) => state.config.inlayDesigns;
export const selectInlayGoldDesigns = (state) => state.config.inlayGoldDesigns;

export const selectInlaySilverDesigns = (state) =>
  state.config.inlaySilverDesigns;

export const selectIsWorking = (state) => state.activeRequestsCount > 0;

export const selectSkuValidParams = (state) => {
  const {
    ringSpecs,
    colors: configColors,
    designSpecs: configDesigns,
  } = state.config;

  const colors = configColors.map(({ code }) => code);
  const designs = configDesigns.map(({ code }) => code);
  const styles = uniq(ringSpecs.map(({ style }) => style));
  const sizes = uniq(ringSpecs.map(({ size }) => size));

  return [
    ...colors.map((value) => ({ value, type: 'color' })),
    ...designs.map((value) => ({ value, type: 'design' })),
    ...styles.map((value) => ({ value, type: 'style' })),
    ...sizes.map((value) => ({ value, type: 'size' })),
  ];
};

// Debug helper to see all available designs
export const selectAllDesigns = (state) => {
  return state.config.designSpecs.map(({ code, name }) => ({ code, name }));
};
