import { find } from 'lodash';

import { store } from './store';
import { selectSkuValidParams } from './selectors';

export interface SkuParseResult {
  style?: string;
  color?: string;
  size?: string;
  design?: string;
  inlayType?: 'gold' | 'silver' | 'clear' | 'none';
}

/**
 * Extract inlay type from design prefix or SKU patterns
 */
const extractInlayType = (
  sku: string,
  design?: string
): 'gold' | 'silver' | 'clear' | 'none' | undefined => {
  const skuLower = sku.toLowerCase();

  // Check for new inlay patterns
  if (
    skuLower.includes('-gs') ||
    skuLower.includes('gs') ||
    design?.startsWith('gs')
  ) {
    return 'gold';
  }
  if (
    skuLower.includes('-ss') ||
    skuLower.includes('ss') ||
    design?.startsWith('ss')
  ) {
    return 'silver';
  }
  if (
    skuLower.includes('-cs') ||
    skuLower.includes('cs') ||
    design?.startsWith('cs') ||
    skuLower.includes('csclad')
  ) {
    return 'clear';
  }

  // Legacy patterns
  if (skuLower.includes('goldink')) {
    return 'gold';
  }
  if (skuLower.includes('silverink')) {
    return 'silver';
  }

  return undefined;
};

export interface OrderData {
  style?: string;
  color?: string;
  size?: string;
  design?: string;
  sku: string;
  title?: string;
  source: 'shopify' | 'etsy';
}

/**
 * Parse SKU to extract style, color, size, and design information
 *
 * Supports design codes with prefixes:
 * - csfloral -> floral (strips 'cs' prefix) - cs = clear silicone (no inlay)
 * - gsfloral -> floral (strips 'gs' prefix) - gs = gold silicone (gold inlay)
 * - ssfloral -> floral (strips 'ss' prefix) - ss = silver silicone (silver inlay)
 *
 * New inlay patterns:
 * - -gsFili-, -ssFili-, -csFili- (gold, silver, clear inlay with design)
 * - csClad (clear silicone with no color inlay)
 *
 * Style pattern updates:
 * - CF4 and CF-4mm -> C4
 *
 * Example SKU: G-CF6-S11-csfloral-GOLD
 * - style: CF6
 * - size: S11
 * - design: csfloral (stripped to 'floral' for lookup, but keeps original in result)
 * - color: GOLD
 */
export const parseSku = (sku: string): SkuParseResult => {
  const skuValidParams = selectSkuValidParams(store.getState());

  const parsed: SkuParseResult = {};

  if (!sku?.match(/^G-/)) return {};

  const parts = sku.split('-');

  parts.forEach((value) => {
    // Handle design codes with prefixes (cs, gs, ss)
    let processedValue = value;
    let foundParam = find(skuValidParams, { value: processedValue });

    // If not found and it might be a design with prefix, try stripping prefixes
    if (
      !foundParam &&
      (value.startsWith('cs') ||
        value.startsWith('gs') ||
        value.startsWith('ss'))
    ) {
      // Strip the prefix (cs, gs, ss) and try again
      processedValue = value.substring(2); // Remove first 2 characters
      foundParam = find(skuValidParams, { value: processedValue });

      // If found with stripped prefix, use the original value for the parsed result
      // but mark it as a design type
      if (foundParam && foundParam.type === 'design') {
        parsed.design = value; // Keep original value with prefix
        return;
      }
    }

    // If found with original value, use it
    if (foundParam) {
      const paramType = foundParam.type as keyof SkuParseResult;
      if (paramType !== 'inlayType') {
        parsed[paramType] = value;
      }
    }
  });

  // Extract inlay type from the SKU and design
  parsed.inlayType = extractInlayType(sku, parsed.design);

  return parsed;
};

/**
 * Enhanced SKU extractor that fills in missing order data fields
 * Uses SKU parsing as fallback when style, color, design, or size are missing
 */
export const extractCompleteOrderData = (
  orderData: OrderData
): SkuParseResult => {
  const { style, color, size: orderSize, design, sku, title } = orderData;

  // Start with existing data and format size if needed
  let formattedSize = orderSize;

  // Format existing size if it's just a number (e.g., "5" -> "S05")
  if (formattedSize && /^\d{1,2}(\.\d)?$/.test(formattedSize)) {
    const sizeNum = formattedSize.padStart(2, '0');
    formattedSize = `S${sizeNum}`;
  }

  const result: SkuParseResult = {
    style: style || undefined,
    color: color || undefined,
    size: formattedSize || undefined,
    design: design || undefined,
    inlayType: undefined,
  };

  // Parse SKU to get fallback values
  const skuParsed = parseSku(sku);

  // Fill in missing fields from SKU parsing
  if (!result.style && skuParsed.style) {
    result.style = skuParsed.style;
  }

  if (!result.color && skuParsed.color) {
    result.color = skuParsed.color;
  }

  if (!result.size && skuParsed.size) {
    result.size = skuParsed.size;
  }

  if (!result.design && skuParsed.design) {
    result.design = skuParsed.design;
  }

  // Always extract inlay type from SKU parsing
  if (skuParsed.inlayType) {
    result.inlayType = skuParsed.inlayType;
  }

  // Enhanced style extraction for descriptive SKU patterns
  if (!result.style && sku) {
    const skuLower = sku.toLowerCase();
    let inferredStyle: string | undefined;

    // Handle descriptive SKU patterns like "ComfortFit-6mm-GreenEnchanted-12" and "CF-4mm-StarPurple-11"
    if (skuLower.includes('comfortfit') && skuLower.includes('6mm')) {
      inferredStyle = 'CF6';
    } else if (skuLower.includes('comfortfit') && skuLower.includes('4mm')) {
      inferredStyle = 'CF4'; // New pattern: CF4 and CF-4mm -> C4
    } else if (
      (skuLower.startsWith('cf-') || skuLower.includes('-cf-')) &&
      skuLower.includes('4mm')
    ) {
      inferredStyle = 'CF4'; // New pattern: CF4 and CF-4mm -> C4
    } else if (
      (skuLower.startsWith('cf-') || skuLower.includes('-cf-')) &&
      skuLower.includes('6mm')
    ) {
      inferredStyle = 'CF6';
    } else if (skuLower.includes('domed') && skuLower.includes('6mm')) {
      inferredStyle = 'D6';
    } else if (skuLower.includes('stepped') && skuLower.includes('comfort')) {
      inferredStyle = 'SCF';
    }
    // Legacy patterns
    else if (skuLower.includes('d6-')) {
      inferredStyle = 'D6';
    } else if (skuLower.includes('scf-')) {
      inferredStyle = 'SCF';
    } else if (skuLower.includes('cf6-') || skuLower.includes('c6')) {
      inferredStyle = 'CF6';
    } else if (skuLower.includes('cf4-') || skuLower.includes('c4')) {
      inferredStyle = 'CF4'; // New pattern: CF4 -> C4
    }

    if (inferredStyle) {
      result.style = inferredStyle;
    }
  }

  // Enhanced size extraction from descriptive SKUs
  if (!result.size && sku) {
    // Look for size patterns in SKU like "ComfortFit-6mm-GreenEnchanted-12" or "ComfortFit-6mm-Black-R11"
    let sizeMatch = sku.match(/[-_]R(\d{1,2}(?:\.\d)?)(?:$|[-_])/i); // R11, R8, etc.
    if (!sizeMatch) {
      sizeMatch = sku.match(/[-_](\d{1,2}(?:\.\d)?)(?:$|[-_])/); // 12, 8, etc.
    }
    if (sizeMatch) {
      const sizeNum = sizeMatch[1].padStart(2, '0');
      result.size = `S${sizeNum}`;
    }
  }

  // Enhanced design extraction from descriptive SKUs
  if (!result.design && sku) {
    const skuValidParams = selectSkuValidParams(store.getState());
    const validDesigns = skuValidParams.filter(
      (param) => param.type === 'design'
    );
    const skuLower = sku.toLowerCase();

    // Look for design patterns in descriptive SKUs and validate against config
    const designPatterns = [
      'enchanted',
      'geometric',
      'floral',
      'celtic',
      'tribal',
      'vintage',
      'modern',
      'classic',
      'nature',
      'abstract',
      'minimalist',
      'ornate',
    ];

    designPatterns.forEach((pattern) => {
      if (skuLower.includes(pattern)) {
        // Find matching valid design from config
        const validDesign = validDesigns.find(
          (configDesign) =>
            configDesign.value.toLowerCase() === pattern.toLowerCase() ||
            configDesign.value.toLowerCase().includes(pattern.toLowerCase()) ||
            pattern.toLowerCase().includes(configDesign.value.toLowerCase())
        );

        if (validDesign) {
          result.design = validDesign.value;
        }
      }
    });
  }

  // Try to extract additional info from title if available
  if (title) {
    const titleLower = title.toLowerCase();

    // Extract size from title if missing (common patterns)
    if (!result.size) {
      const sizeMatch = title.match(/\b(size\s+)?(\d{1,2}(?:\.\d)?)\b/i);
      if (sizeMatch) {
        const sizeNum = sizeMatch[2].padStart(2, '0');
        result.size = `S${sizeNum}`;
      }
    }

    // Extract color from title if missing (validate against config)
    if (!result.color) {
      const skuValidParams = selectSkuValidParams(store.getState());
      const validColors = skuValidParams.filter(
        (param) => param.type === 'color'
      );

      // Find color in title that matches valid colors from config
      const colorMatch = validColors.find((configColor) =>
        titleLower.includes(configColor.value.toLowerCase())
      );

      if (colorMatch) {
        result.color = colorMatch.value;
      }
    }
  }

  return result;
};
