import { IShopReceipt, IShopReceiptTransaction } from 'etsy-ts/v3';

export type OrderItem = {
  // itemId
  itemId: string;
  // order
  createdTimeLocal: string;
  orderId: string;
  orderItemCount: number;
  orderItemIndex: number;
  orderItemNumberOfTotal: string;
  isShipped: boolean;
  message: string;
  isMixedOrder: boolean;
  isCancelled: boolean;
  orderValue: number;
  orderCurrency: string;
  // customer
  name: string;
  shippingShortName: string;
  email: string;
  // shipping
  chitchatsAddress?: {
    name: string;
    address_1: string;
    address_2: string;
    city: string;
    country_code: string;
    phone: string;
    province_code: string;
    postal_code: string;
  };
  // item
  title: string;
  quantity: number;
  isEngraved: boolean;
  sku: string;
  style: string;
  color: string;
  size: string;
  design: string;
  inkColor?: string;
  customFields: { label: string; value: any }[];
  personalization?: string;
  outside: Record<string, any>;
  inside: Record<string, any>;
  // file
  fileName?: string;
  targetFileName?: string;
  // source data
} & (
  | {
      source: 'etsy';
      etsyReceipt: Required<IShopReceipt>;
      etsyTransaction: Required<IShopReceiptTransaction>;
    }
  | {
      source: 'shopify';
      shopifyOrder: any;
      shopifyLineItem: any;
    }
);
