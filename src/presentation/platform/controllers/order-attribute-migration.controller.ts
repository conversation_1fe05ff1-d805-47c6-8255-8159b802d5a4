import { Controller, Post, Get, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '@presentation/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@presentation/auth/guards/roles.guard';
import { Roles } from '@presentation/auth/decorators/roles.decorator';
import { UserRole } from '@domain/user/user-role.enum';
import { OrderAttributeMigrationService } from '../services/order-attribute-migration.service';

@ApiTags('Order Attribute Migration')
@Controller('platform/migration')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class OrderAttributeMigrationController {
  constructor(private readonly migrationService: OrderAttributeMigrationService) {}

  @Post('attributes')
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Migrate existing order items to extract missing attributes' })
  @ApiResponse({
    status: 200,
    description: 'Migration completed successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            processed: { type: 'number' },
            updated: { type: 'number' },
            errors: { type: 'number' },
          },
        },
      },
    },
  })
  async migrateAttributes() {
    const result = await this.migrationService.migrateOrderItemAttributes();

    return {
      success: true,
      data: result,
      message: `Migration completed: ${result.processed} processed, ${result.updated} updated, ${result.errors} errors`,
    };
  }

  @Get('status')
  @Roles(UserRole.ADMIN, UserRole.USER)
  @ApiOperation({ summary: 'Get migration status' })
  @ApiResponse({
    status: 200,
    description: 'Migration status retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            totalItems: { type: 'number' },
            itemsWithAttributes: { type: 'number' },
            itemsWithoutAttributes: { type: 'number' },
            migrationNeeded: { type: 'boolean' },
          },
        },
      },
    },
  })
  async getMigrationStatus() {
    const status = await this.migrationService.getMigrationStatus();

    return {
      success: true,
      data: status,
    };
  }
}
