import { Injectable, Logger } from '@nestjs/common';
import { PlatformSource } from '@domain/product/platform-source.enum';
import { ProductStatus } from '@domain/product/product-status.enum';
import { OrderStatus } from '@domain/order/order-status.enum';
import { ProductRepository } from '@infrastructure/database/repositories/product.repository';
import { OrderRepository } from '@infrastructure/database/repositories/order.repository';
import { OrderItemRepository } from '@infrastructure/database/repositories/order-item.repository';
import {
  ShopifyApiService,
  ShopifyProduct,
  ShopifyOrder,
  ShopifyLineItem,
} from '@infrastructure/external/shopify/shopify-api.service';
// import { PlatformSyncService } from './platform-sync.service'; // temporarily disabled
import { ProductAttributeExtractor } from '@shared/utils/product-attribute-extractor.util';
import { SkuExtractionService } from '@shared/services/sku-extraction.service';
import { PlatformAttributeExtractionService } from '@shared/services/platform-attribute-extraction.service';

export interface ShopifySyncResult {
  platform: PlatformSource.SHOPIFY;
  success: boolean;
  productsProcessed: number;
  ordersProcessed: number;
  errors: string[];
  duration: number;
  timestamp: Date;
}

@Injectable()
export class ShopifySyncService {
  private readonly logger = new Logger(ShopifySyncService.name);

  constructor(
    private readonly shopifyApiService: ShopifyApiService,
    private readonly productRepository: ProductRepository,
    private readonly orderRepository: OrderRepository,
    private readonly orderItemRepository: OrderItemRepository,
    // private readonly platformSyncService: PlatformSyncService, // temporarily disabled
    private readonly skuExtractionService: SkuExtractionService,
    private readonly platformAttributeExtractionService: PlatformAttributeExtractionService,
  ) {}

  /**
   * Sync all Shopify data (products and orders)
   */
  async syncAll(): Promise<ShopifySyncResult> {
    const startTime = Date.now();
    const errors: string[] = [];
    let productsProcessed = 0;
    let ordersProcessed = 0;

    try {
      this.logger.log('Starting Shopify sync (products and orders)');

      // Sync products
      try {
        productsProcessed = await this.syncProducts();
      } catch (error) {
        this.logger.error('Failed to sync Shopify products:', error);
        errors.push(`Product sync failed: ${error.message}`);
      }

      // Sync orders
      try {
        ordersProcessed = await this.syncOrders();
      } catch (error) {
        this.logger.error('Failed to sync Shopify orders:', error);
        errors.push(`Order sync failed: ${error.message}`);
      }

      const result: ShopifySyncResult = {
        platform: PlatformSource.SHOPIFY,
        success: errors.length === 0,
        productsProcessed,
        ordersProcessed,
        errors,
        duration: Date.now() - startTime,
        timestamp: new Date(),
      };

      // Record sync result
      // await this.platformSyncService.recordSyncResult(result); // temporarily disabled

      return result;
    } catch (error) {
      this.logger.error('Shopify sync failed:', error);

      const result: ShopifySyncResult = {
        platform: PlatformSource.SHOPIFY,
        success: false,
        productsProcessed,
        ordersProcessed,
        errors: [...errors, error.message],
        duration: Date.now() - startTime,
        timestamp: new Date(),
      };

      // await this.platformSyncService.recordSyncResult(result); // temporarily disabled
      return result;
    }
  }

  /**
   * Sync Shopify products
   */
  async syncProducts(): Promise<number> {
    this.logger.log('Starting Shopify product sync');
    let processed = 0;

    try {
      let sinceId = 0;
      const limit = 50;
      let hasMore = true;

      while (hasMore) {
        // Get products from Shopify
        const products = await this.shopifyApiService.getProducts({
          limit,
          since_id: sinceId,
          status: 'active',
        });

        hasMore = products.length === limit;

        for (const product of products) {
          try {
            await this.processProduct(product);
            processed++;
            sinceId = Math.max(sinceId, product.id);
          } catch (error) {
            this.logger.error(`Failed to process product ${product.id}:`, error);
          }
        }

        // Add delay to respect rate limits
        if (hasMore) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      this.logger.log(`Processed ${processed} Shopify products`);
      return processed;
    } catch (error) {
      this.logger.error('Failed to sync Shopify products:', error);
      throw error;
    }
  }

  /**
   * Sync Shopify orders
   */
  async syncOrders(): Promise<number> {
    this.logger.log('Starting Shopify order sync');
    let processed = 0;

    try {
      // Get orders from last 7 days
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 7);
      let sinceId = 0;
      const limit = 50;
      let hasMore = true;

      while (hasMore) {
        // Get orders from Shopify
        const result = await this.shopifyApiService.getOrders({
          limit,
          since_id: sinceId,
          created_at_min: thirtyDaysAgo.toISOString(),
          status: 'any',
        });

        const orders = result.orders;
        hasMore = orders.length === limit;

        for (const order of orders) {
          try {
            await this.processOrder(order);
            processed++;
            sinceId = Math.max(sinceId, order.id);
          } catch (error) {
            this.logger.error(`Failed to process order ${order.id}:`, error);
          }
        }

        // Add delay to respect rate limits
        if (hasMore) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      this.logger.log(`Processed ${processed} Shopify orders`);
      return processed;
    } catch (error) {
      this.logger.error('Failed to sync Shopify orders:', error);
      throw error;
    }
  }

  /**
   * Process product from Shopify
   */
  private async processProduct(shopifyProduct: ShopifyProduct): Promise<void> {
    // Process each variant as a separate product
    for (const variant of shopifyProduct.variants) {
      const sku = variant.sku || `SHOPIFY-${variant.id}`;

      // Check if product exists
      let product = await this.productRepository.findBySku(sku);

      if (!product) {
        // Create new product
        product = this.productRepository.create({
          sku,
          externalProductId: shopifyProduct.id?.toString() || '',
          title: `${shopifyProduct.title}${variant.title !== 'Default Title' ? ` - ${variant.title}` : ''}`,
          status: this.mapShopifyProductStatus(shopifyProduct.status),
          source: PlatformSource.SHOPIFY,
          quantity: 0,
          amazonQuantity: 0,
          shopifyQuantity: variant.inventory_quantity || 0,
          etsyQuantity: 0,
          price: parseFloat(variant.price || '0'),
          currency: 'USD', // Shopify doesn't provide currency in variant
          inventoryItemId: variant.inventory_item_id?.toString() || '',
          inventoryManagement: variant.inventory_management,
          externalCreatedAt: new Date(shopifyProduct.created_at),
        });
      } else {
        // Update existing product
        product.updateShopifyInfo({
          inventoryItemId: variant.inventory_item_id?.toString() || '',
          inventoryManagement: variant.inventory_management,
          quantity: variant.inventory_quantity || 0,
        });

        product.price = parseFloat(variant.price || '0');
      }

      await this.productRepository.save(product);
    }
  }

  /**
   * Process order from Shopify
   */
  private async processOrder(shopifyOrder: ShopifyOrder): Promise<void> {
    // Check if order already exists
    const existingOrder = await this.orderRepository.findByExternalId(
      shopifyOrder.id?.toString() || '',
      PlatformSource.SHOPIFY,
    );

    if (existingOrder) {
      // Update existing order if needed
      this.updateExistingOrder(existingOrder, shopifyOrder);
      await this.orderRepository.save(existingOrder);
      return;
    }

    // Create new order
    const order = this.orderRepository.create({
      externalOrderId: shopifyOrder.id?.toString() || '',
      orderNumber: shopifyOrder.name || '',
      status: this.mapShopifyOrderStatus(
        shopifyOrder.financial_status,
        shopifyOrder.fulfillment_status,
      ),
      source: PlatformSource.SHOPIFY,
      externalCreatedAt: new Date(shopifyOrder.created_at),
      customerName: shopifyOrder.customer
        ? `${shopifyOrder.customer.first_name || ''} ${shopifyOrder.customer.last_name || ''}`.trim()
        : '',
      customerEmail: shopifyOrder.email || '',
      customerPhone: shopifyOrder.phone || '',
      shippingAddressLine1: shopifyOrder.shipping_address?.address1,
      shippingAddressLine2: shopifyOrder.shipping_address?.address2,
      shippingCity: shopifyOrder.shipping_address?.city,
      shippingState: shopifyOrder.shipping_address?.province,
      shippingPostalCode: shopifyOrder.shipping_address?.zip,
      shippingCountry: shopifyOrder.shipping_address?.country,
      subtotalPrice: parseFloat(shopifyOrder.subtotal_price || '0'),
      shippingPrice: 0, // Will be calculated from shipping lines
      taxAmount: parseFloat(shopifyOrder.total_tax || '0'),
      discountAmount: parseFloat(shopifyOrder.total_discounts || '0'),
      totalPrice: parseFloat(shopifyOrder.total_price || '0'),
      currency: shopifyOrder.currency || 'USD',
      customerNote: shopifyOrder.note || '',
      isProcessed: shopifyOrder.financial_status === 'paid',
      rawData: {
        platform: 'shopify',
        order: shopifyOrder,
        syncedAt: new Date().toISOString(),
        apiVersion: '2023-10',
      },
      metadata: {
        shopifyData: {
          orderNumber: shopifyOrder.order_number,
          financialStatus: shopifyOrder.financial_status,
          fulfillmentStatus: shopifyOrder.fulfillment_status,
          gateway: shopifyOrder.gateway,
          test: shopifyOrder.test,
          tags: shopifyOrder.tags,
        },
      },
    });

    // Calculate shipping price from shipping lines
    if (shopifyOrder.shipping_lines && shopifyOrder.shipping_lines.length > 0) {
      order.shippingPrice = shopifyOrder.shipping_lines.reduce(
        (total, line) => total + parseFloat(line.price || '0'),
        0,
      );
    }

    const savedOrder = await this.orderRepository.save(order);

    // Create order items
    for (const lineItem of shopifyOrder.line_items) {
      await this.createOrderItem(savedOrder.id, lineItem);
    }
  }

  /**
   * Create order item from Shopify line item
   */
  private async createOrderItem(orderId: string, lineItem: ShopifyLineItem): Promise<void> {
    const sku = lineItem.sku || `SHOPIFY-${lineItem.variant_id || 'unknown'}`;

    // Step 1: Extract attributes using basic extractor first
    const basicAttributes = ProductAttributeExtractor.extractAttributes({
      title: lineItem.title,
      description: lineItem.variant_title,
      sku: lineItem.sku,
      variantTitle: lineItem.variant_title,
      properties: lineItem.properties?.map(prop => ({
        name: prop.name,
        value: prop.value,
      })),
      metadata: {
        vendor: lineItem.vendor,
        productId: lineItem.product_id,
        variantId: lineItem.variant_id,
      },
    });

    // Step 2: Use enhanced SKU extraction to fill in missing fields
    const skuExtractedData = this.skuExtractionService.extractCompleteOrderData({
      style: undefined, // Force SKU extraction
      color: undefined, // Force SKU extraction
      size: undefined, // Force SKU extraction
      outside: undefined, // Force SKU extraction
      sku,
      title: lineItem.title,
      source: 'shopify',
    });

    // Convert SKU extracted data to the expected format
    const skuExtracted = {
      style: skuExtractedData.style,
      color: skuExtractedData.color,
      size: skuExtractedData.size
        ? parseInt(skuExtractedData.size.replace('S', ''), 10)
        : undefined,
      design: skuExtractedData.outside,
      isCustom: false,
      isEngrave: false,
      customization: undefined,
      engraving: undefined,
      inlayType: (skuExtractedData.inlayType as 'gold' | 'silver' | 'clear' | 'none') || 'none',
    };

    // Step 3: Use platform-specific extraction to fill in any remaining missing attributes
    const platformData = {
      platform: 'shopify',
      productId: lineItem.product_id,
      variantId: lineItem.variant_id,
      vendor: lineItem.vendor,
      properties: lineItem.properties,
      variantTitle: lineItem.variant_title,
      variantOptions: [],
    };

    const finalAttributes = this.platformAttributeExtractionService.extractPlatformAttributes(
      PlatformSource.SHOPIFY,
      skuExtracted,
      platformData,
      lineItem.title,
      sku,
    );

    const orderItem = this.orderItemRepository.create({
      orderId,
      sku,
      title: lineItem.title || '',
      style: finalAttributes.style,
      color: finalAttributes.color,
      design: finalAttributes.design,
      size: finalAttributes.size,
      quantity: lineItem.quantity || 0,
      unitPrice: parseFloat(lineItem.price || '0'),
      totalPrice: parseFloat(lineItem.price || '0') * (lineItem.quantity || 0),
      currency: 'USD', // Shopify doesn't provide currency in line item
      externalProductId: lineItem.product_id?.toString() || '',
      externalVariantId: lineItem.variant_id?.toString() || '',
      isCustom: finalAttributes.isCustom || (lineItem.properties && lineItem.properties.length > 0),
      isEngrave: finalAttributes.isEngrave,
      customization: finalAttributes.customization,
      engraving: finalAttributes.engraving,
      rawData: {
        platform: 'shopify',
        lineItem: lineItem,
        syncedAt: new Date().toISOString(),
      },
      metadata: {
        shopifyData: {
          lineItemId: lineItem.id,
          variantTitle: lineItem.variant_title,
          vendor: lineItem.vendor,
          fulfillableQuantity: lineItem.fulfillable_quantity,
          fulfillmentStatus: lineItem.fulfillment_status,
          giftCard: lineItem.gift_card,
          properties: lineItem.properties,
        },
        extractedAttributes: {
          basic: basicAttributes,
          skuExtracted: skuExtracted,
          final: finalAttributes,
        },
      },
    });

    await this.orderItemRepository.save(orderItem);
  }

  /**
   * Update existing order with Shopify data
   */
  private updateExistingOrder(existingOrder: any, shopifyOrder: ShopifyOrder): void {
    const newStatus = this.mapShopifyOrderStatus(
      shopifyOrder.financial_status,
      shopifyOrder.fulfillment_status,
    );

    if (existingOrder.status !== newStatus) {
      existingOrder.updateStatus(newStatus);
    }

    // Update metadata
    if (!existingOrder.metadata) {
      existingOrder.metadata = {};
    }
    existingOrder.metadata.shopifyData = {
      ...existingOrder.metadata.shopifyData,
      lastUpdated: new Date(shopifyOrder.updated_at),
      financialStatus: shopifyOrder.financial_status,
      fulfillmentStatus: shopifyOrder.fulfillment_status,
    };
  }

  /**
   * Map Shopify product status to our product status
   */
  private mapShopifyProductStatus(status: string): ProductStatus {
    switch (status.toLowerCase()) {
      case 'active':
        return ProductStatus.ACTIVE;
      case 'archived':
        return ProductStatus.ARCHIVED;
      case 'draft':
        return ProductStatus.DRAFT;
      default:
        return ProductStatus.INACTIVE;
    }
  }

  /**
   * Map Shopify order status to our order status
   */
  private mapShopifyOrderStatus(financialStatus?: string, fulfillmentStatus?: string): OrderStatus {
    if (financialStatus === 'pending' || financialStatus === 'authorized') {
      return OrderStatus.PENDING;
    }

    if (financialStatus === 'paid') {
      if (fulfillmentStatus === 'fulfilled') {
        return OrderStatus.SHIPPED;
      } else if (fulfillmentStatus === 'partial') {
        return OrderStatus.PROCESSING;
      } else {
        return OrderStatus.CONFIRMED;
      }
    }

    if (financialStatus === 'refunded' || financialStatus === 'partially_refunded') {
      return OrderStatus.REFUNDED;
    }

    if (financialStatus === 'voided') {
      return OrderStatus.CANCELLED;
    }

    return OrderStatus.PENDING;
  }

  /**
   * Update inventory for a specific SKU
   */
  async updateInventory(sku: string, quantity: number): Promise<boolean> {
    try {
      this.logger.log(`Updating Shopify inventory for SKU ${sku} to quantity ${quantity}`);

      // Find the product to get inventory item ID
      const product = await this.productRepository.findBySku(sku);
      if (!product || !product.inventoryItemId) {
        this.logger.warn(`Product or inventory item ID not found for SKU ${sku}`);
        return false;
      }

      const inventoryItemId = parseInt(product.inventoryItemId, 10);

      // Get locations to update inventory
      const locations = await this.shopifyApiService.getLocations();
      if (locations.length === 0) {
        this.logger.warn('No Shopify locations found');
        return false;
      }

      // Update inventory at the first location (primary location)
      const primaryLocation = locations[0];
      await this.shopifyApiService.updateInventoryLevel(
        inventoryItemId,
        primaryLocation.id,
        quantity,
      );

      // Update local product record
      product.updateQuantityForPlatform(PlatformSource.SHOPIFY, quantity);
      await this.productRepository.save(product);

      return true;
    } catch (error) {
      this.logger.error(`Failed to update Shopify inventory for SKU ${sku}:`, error);
      return false;
    }
  }

  /**
   * Health check for Shopify API
   */
  async healthCheck(): Promise<boolean> {
    try {
      return await this.shopifyApiService.healthCheck();
    } catch (error) {
      this.logger.error('Shopify health check failed:', error);
      return false;
    }
  }

  /**
   * Sync orders with date filters and duplicate prevention
   */
  async syncOrdersWithFilters(params: {
    startDate?: string;
    endDate?: string;
    limit: number;
    forceUpdate: boolean;
  }): Promise<{
    ordersProcessed: number;
    ordersCreated: number;
    ordersUpdated: number;
    ordersSkipped: number;
  }> {
    this.logger.log('Starting Shopify order sync with filters', params);

    let ordersProcessed = 0;
    let ordersCreated = 0;
    let ordersUpdated = 0;
    let ordersSkipped = 0;

    try {
      // Build Shopify API parameters
      const apiParams: any = {
        limit: 250, // Use maximum Shopify limit for efficiency
        status: 'any', // Get all order statuses
        financial_status: 'any',
        fulfillment_status: 'any',
        maxPages: Math.ceil(params.limit / 250), // Calculate max pages needed
      };

      // Add date filters if provided
      if (params.startDate) {
        apiParams.created_at_min = params.startDate;
      }
      if (params.endDate) {
        apiParams.created_at_max = params.endDate;
      }

      this.logger.log('Fetching orders from Shopify API with pagination', {
        ...apiParams,
        requestedLimit: params.limit,
      });

      // Fetch ALL orders from Shopify with automatic pagination
      const allOrders = await this.shopifyApiService.getAllOrders(apiParams);

      // Limit the results to the requested amount
      const orders = allOrders.slice(0, params.limit);

      this.logger.log(
        `Retrieved ${orders.length} orders from Shopify (${allOrders.length} total available)`,
      );

      // Process each order
      for (const shopifyOrder of orders) {
        try {
          ordersProcessed++;

          // Check if order already exists
          const existingOrder = await this.orderRepository.findByExternalId(
            shopifyOrder.id?.toString() || '',
            PlatformSource.SHOPIFY,
          );

          if (existingOrder) {
            if (params.forceUpdate) {
              // Update existing order
              const newStatus = this.mapShopifyOrderStatus(
                shopifyOrder.financial_status,
                shopifyOrder.fulfillment_status,
              );
              if (existingOrder.status !== newStatus) {
                existingOrder.updateStatus(newStatus);
              }

              // Update metadata
              if (!existingOrder.metadata) {
                existingOrder.metadata = {};
              }
              existingOrder.metadata.shopifyData = {
                ...existingOrder.metadata.shopifyData,
                lastUpdated: new Date(shopifyOrder.updated_at),
                financialStatus: shopifyOrder.financial_status,
                fulfillmentStatus: shopifyOrder.fulfillment_status,
              };

              await this.orderRepository.save(existingOrder);
              ordersUpdated++;
              this.logger.debug(`Updated existing Shopify order: ${shopifyOrder.id}`);
            } else {
              // Skip existing order
              ordersSkipped++;
              this.logger.debug(`Skipped existing Shopify order: ${shopifyOrder.id}`);
            }
          } else {
            // Create new order
            await this.processOrder(shopifyOrder);
            ordersCreated++;
            this.logger.debug(`Created new Shopify order: ${shopifyOrder.id}`);
          }
        } catch (error) {
          this.logger.error(`Failed to process Shopify order ${shopifyOrder.id}:`, error);
          // Continue processing other orders
        }
      }

      this.logger.log(
        `Shopify order sync completed: ${ordersProcessed} processed, ${ordersCreated} created, ${ordersUpdated} updated, ${ordersSkipped} skipped`,
      );

      return {
        ordersProcessed,
        ordersCreated,
        ordersUpdated,
        ordersSkipped,
      };
    } catch (error) {
      this.logger.error('Failed to sync Shopify orders:', error);
      throw error;
    }
  }
}
