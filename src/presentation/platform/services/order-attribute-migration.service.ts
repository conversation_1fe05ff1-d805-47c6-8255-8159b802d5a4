import { Injectable, Logger } from '@nestjs/common';
import { OrderItemRepository } from '@infrastructure/database/repositories/order-item.repository';
import { ProductAttributeExtractor } from '@shared/utils/product-attribute-extractor.util';
import { PlatformAttributeExtractionService } from '@shared/services/platform-attribute-extraction.service';
import { PlatformSource } from '@domain/product/platform-source.enum';

@Injectable()
export class OrderAttributeMigrationService {
  private readonly logger = new Logger(OrderAttributeMigrationService.name);

  constructor(
    private readonly orderItemRepository: OrderItemRepository,
    private readonly platformAttributeExtractionService: PlatformAttributeExtractionService,
  ) {}

  /**
   * Migrate existing order items to extract missing attributes
   */
  async migrateOrderItemAttributes(): Promise<{
    processed: number;
    updated: number;
    errors: number;
  }> {
    this.logger.log('Starting order item attribute migration');

    let processed = 0;
    let updated = 0;
    let errors = 0;

    try {
      // Find order items that don't have extracted attributes
      const orderItems = await this.orderItemRepository.find({
        where: [{ style: null }, { color: null }, { design: null }, { size: null }],
        take: 1000, // Process in batches
      });

      this.logger.log(`Found ${orderItems.length} order items to migrate`);

      for (const orderItem of orderItems) {
        try {
          processed++;

          // Check if item already has attributes
          if (orderItem.style || orderItem.color || orderItem.design || orderItem.size) {
            continue; // Skip if already has some attributes
          }

          // Extract attributes from raw data using platform-specific logic
          const attributes = await this.extractAttributesFromOrderItem(orderItem);

          // Update the order item with extracted attributes
          if (attributes.style || attributes.color || attributes.design || attributes.size) {
            orderItem.updateProductInfo({
              style: attributes.style,
              color: attributes.color,
              design: attributes.design,
              size: attributes.size,
            });

            // Update customization info
            if (attributes.isCustom || attributes.isEngrave) {
              orderItem.updateCustomization({
                customization: attributes.customization,
                engraving: attributes.engraving,
                isCustom: attributes.isCustom,
                isEngrave: attributes.isEngrave,
              });
            }

            // Add extracted attributes to metadata
            if (!orderItem.metadata) {
              orderItem.metadata = {};
            }
            orderItem.metadata.extractedAttributes = attributes;
            orderItem.metadata.attributeMigration = {
              migratedAt: new Date().toISOString(),
              version: '1.0',
            };

            await this.orderItemRepository.save(orderItem);
            updated++;

            this.logger.debug(`Updated order item ${orderItem.id} with attributes`);
          }
        } catch (error) {
          this.logger.error(`Failed to migrate order item ${orderItem.id}:`, error);
          errors++;
        }
      }

      this.logger.log(
        `Attribute migration completed: ${processed} processed, ${updated} updated, ${errors} errors`,
      );

      return { processed, updated, errors };
    } catch (error) {
      this.logger.error('Order item attribute migration failed:', error);
      throw error;
    }
  }

  /**
   * Extract attributes from an existing order item using its raw data
   */
  private async extractAttributesFromOrderItem(orderItem: any): Promise<{
    style?: string;
    color?: string;
    size?: number;
    design?: string;
    isCustom: boolean;
    isEngrave: boolean;
    customization?: string;
    engraving?: string;
  }> {
    const rawData = orderItem.rawData || {};
    const platform = rawData.platform;

    // Step 1: Extract basic attributes first
    const basicAttributes = this.extractBasicAttributes(orderItem, rawData, platform);

    // Step 2: Create SKU extracted data (for now, use basic attributes)
    const skuExtracted = {
      style: basicAttributes.style,
      color: basicAttributes.color,
      size: basicAttributes.size,
      design: basicAttributes.design,
      isCustom: basicAttributes.isCustom,
      isEngrave: basicAttributes.isEngrave,
      customization: basicAttributes.customization,
      engraving: basicAttributes.engraving,
      inlayType: undefined,
    };

    // Step 3: Use platform-specific extraction if available
    if (this.platformAttributeExtractionService.isPlatformSupported(platform as PlatformSource)) {
      const platformData = this.getPlatformDataFromRawData(rawData, platform);

      return this.platformAttributeExtractionService.extractPlatformAttributes(
        platform as PlatformSource,
        skuExtracted,
        platformData,
        orderItem.title,
        orderItem.sku,
      );
    }

    // Fallback to basic attributes
    return basicAttributes;
  }

  /**
   * Extract basic attributes from order item
   */
  private extractBasicAttributes(
    orderItem: any,
    rawData: any,
    platform: string,
  ): {
    style?: string;
    color?: string;
    size?: number;
    design?: string;
    isCustom: boolean;
    isEngrave: boolean;
    customization?: string;
    engraving?: string;
  } {
    // Extract based on platform
    switch (platform) {
      case 'shopify':
        return this.extractFromShopifyData(rawData);
      case 'etsy':
        return this.extractFromEtsyData(rawData);
      case 'amazon':
        return this.extractFromAmazonData(rawData);
      default:
        // Fallback to basic extraction
        return ProductAttributeExtractor.extractAttributes({
          title: orderItem.title,
          sku: orderItem.sku,
        });
    }
  }

  /**
   * Get platform data from raw data for platform-specific extraction
   */
  private getPlatformDataFromRawData(rawData: any, platform: string): any {
    switch (platform) {
      case 'shopify':
        return {
          platform: 'shopify',
          productId: rawData.lineItem?.product_id,
          variantId: rawData.lineItem?.variant_id,
          vendor: rawData.lineItem?.vendor,
          properties: rawData.lineItem?.properties,
          variantTitle: rawData.lineItem?.variant_title,
          variantOptions: [],
        };
      case 'etsy':
        return {
          platform: 'etsy',
          listingId: rawData.transaction?.listing_id,
          transactionId: rawData.transaction?.transaction_id,
          variations: rawData.transaction?.variations,
          productData: rawData.transaction?.product_data,
        };
      case 'amazon':
        return {
          platform: 'amazon',
          asin: rawData.amazonData?.asin,
          sellerSku: rawData.amazonData?.sellerSku,
          title: rawData.amazonData?.title,
          productInfo: rawData.amazonData?.productInfo,
          conditionId: rawData.amazonData?.conditionId,
          isGift: rawData.amazonData?.isGift,
        };
      default:
        return { platform };
    }
  }

  /**
   * Extract attributes from Shopify raw data
   */
  private extractFromShopifyData(rawData: any): any {
    const lineItem = rawData.lineItem;
    if (!lineItem) return {};

    return ProductAttributeExtractor.extractAttributes({
      title: lineItem.title,
      description: lineItem.variant_title,
      sku: lineItem.sku,
      variantTitle: lineItem.variant_title,
      properties: lineItem.properties?.map((prop: any) => ({
        name: prop.name,
        value: prop.value,
      })),
      metadata: {
        vendor: lineItem.vendor,
        productId: lineItem.product_id,
        variantId: lineItem.variant_id,
      },
    });
  }

  /**
   * Extract attributes from Etsy raw data
   */
  private extractFromEtsyData(rawData: any): any {
    const transaction = rawData.transaction;
    if (!transaction) return {};

    return ProductAttributeExtractor.extractAttributes({
      title: transaction.title,
      description: transaction.description,
      sku: transaction.sku,
      properties: transaction.variations?.map((v: any) => ({
        name: v.formatted_name || '',
        value: v.formatted_value || '',
      })),
      variations: transaction.variations?.map((v: any) => ({
        name: v.formatted_name || '',
        value: v.formatted_value || '',
      })),
      productData: transaction.product_data,
      metadata: {
        listingId: transaction.listing_id,
        transactionId: transaction.transaction_id,
      },
    });
  }

  /**
   * Extract attributes from Amazon raw data
   */
  private extractFromAmazonData(rawData: any): any {
    const amazonData = rawData.amazonData;
    if (!amazonData) return {};

    return ProductAttributeExtractor.extractAttributes({
      title: amazonData.title,
      description: amazonData.productInfo?.title || '',
      sku: amazonData.sellerSku,
      metadata: {
        asin: amazonData.asin,
        orderItemId: amazonData.orderItemId,
        conditionId: amazonData.conditionId,
        isGift: amazonData.isGift,
        productInfo: amazonData.productInfo,
      },
    });
  }

  /**
   * Get migration status
   */
  async getMigrationStatus(): Promise<{
    totalItems: number;
    itemsWithAttributes: number;
    itemsWithoutAttributes: number;
    migrationNeeded: boolean;
  }> {
    const totalItems = await this.orderItemRepository.count();

    const itemsWithAttributes = await this.orderItemRepository.count({
      where: [
        { style: { $ne: null } },
        { color: { $ne: null } },
        { design: { $ne: null } },
        { size: { $ne: null } },
      ],
    });

    const itemsWithoutAttributes = totalItems - itemsWithAttributes;

    return {
      totalItems,
      itemsWithAttributes,
      itemsWithoutAttributes,
      migrationNeeded: itemsWithoutAttributes > 0,
    };
  }
}
