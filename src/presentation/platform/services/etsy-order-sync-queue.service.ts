import { Injectable, Logger } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue, Job } from 'bull';
import { v4 as uuidv4 } from 'uuid';
import { EtsyOrderSyncJobData, EtsyOrderSyncJobResult } from '../processors/etsy-order-sync.processor';

export interface EtsyOrderSyncQueueOptions {
  startDate?: string;
  endDate?: string;
  limit?: number;
  forceUpdate?: boolean;
  batchSize?: number;
}

export interface EtsyOrderSyncStatus {
  jobId: string;
  status: 'waiting' | 'active' | 'completed' | 'failed' | 'delayed' | 'paused';
  progress: number;
  totalJobs: number;
  completedJobs: number;
  failedJobs: number;
  totalOrdersProcessed: number;
  totalOrdersCreated: number;
  totalOrdersUpdated: number;
  totalOrdersSkipped: number;
  errors: string[];
  startedAt?: Date;
  completedAt?: Date;
  estimatedTimeRemaining?: number;
}

@Injectable()
export class EtsyOrderSyncQueueService {
  private readonly logger = new Logger(EtsyOrderSyncQueueService.name);
  private readonly syncStatuses = new Map<string, EtsyOrderSyncStatus>();

  constructor(
    @InjectQueue('etsy-order-sync') private readonly etsyOrderSyncQueue: Queue,
  ) {}

  /**
   * Start a queue-based order sync for Etsy
   */
  async startOrderSync(options: EtsyOrderSyncQueueOptions): Promise<string> {
    const jobId = uuidv4();
    const batchSize = options.batchSize || 50;
    const limit = options.limit || 1000;

    this.logger.log(`Starting Etsy order sync - JobID: ${jobId}, Options:`, options);

    // Initialize sync status
    const syncStatus: EtsyOrderSyncStatus = {
      jobId,
      status: 'waiting',
      progress: 0,
      totalJobs: Math.ceil(limit / batchSize),
      completedJobs: 0,
      failedJobs: 0,
      totalOrdersProcessed: 0,
      totalOrdersCreated: 0,
      totalOrdersUpdated: 0,
      totalOrdersSkipped: 0,
      errors: [],
      startedAt: new Date(),
    };

    this.syncStatuses.set(jobId, syncStatus);

    try {
      // Create initial batch job
      const initialJobData: EtsyOrderSyncJobData = {
        startDate: options.startDate,
        endDate: options.endDate,
        offset: 0,
        limit: batchSize,
        totalExpected: limit,
        jobId,
        forceUpdate: options.forceUpdate || false,
      };

      // Add the first job to the queue
      const job = await this.etsyOrderSyncQueue.add('sync-orders-batch', initialJobData, {
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
        removeOnComplete: 10,
        removeOnFail: 5,
      });

      this.logger.log(`Added initial Etsy order sync job to queue - JobID: ${jobId}, QueueJobID: ${job.id}`);

      // Set up job completion handler to create next batch if needed
      this.setupJobCompletionHandler(jobId, options);

      return jobId;

    } catch (error) {
      this.logger.error(`Failed to start Etsy order sync - JobID: ${jobId}:`, error);
      syncStatus.status = 'failed';
      syncStatus.errors.push(`Failed to start sync: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get sync status by job ID
   */
  async getSyncStatus(jobId: string): Promise<EtsyOrderSyncStatus | null> {
    const status = this.syncStatuses.get(jobId);
    if (!status) {
      return null;
    }

    // Update status with current queue information
    const jobs = await this.etsyOrderSyncQueue.getJobs(['waiting', 'active', 'completed', 'failed']);
    const relatedJobs = jobs.filter(job => job.data.jobId === jobId);

    if (relatedJobs.length > 0) {
      const activeJob = relatedJobs.find(job => job.opts.attempts && job.attemptsMade < job.opts.attempts);
      if (activeJob) {
        status.progress = activeJob.progress() || 0;
      }
    }

    return { ...status };
  }

  /**
   * Cancel a running sync
   */
  async cancelSync(jobId: string): Promise<boolean> {
    try {
      const jobs = await this.etsyOrderSyncQueue.getJobs(['waiting', 'active', 'delayed']);
      const relatedJobs = jobs.filter(job => job.data.jobId === jobId);

      for (const job of relatedJobs) {
        await job.remove();
      }

      const status = this.syncStatuses.get(jobId);
      if (status) {
        status.status = 'failed';
        status.completedAt = new Date();
        status.errors.push('Sync cancelled by user');
      }

      this.logger.log(`Cancelled Etsy order sync - JobID: ${jobId}`);
      return true;

    } catch (error) {
      this.logger.error(`Failed to cancel Etsy order sync - JobID: ${jobId}:`, error);
      return false;
    }
  }

  /**
   * Get all active syncs
   */
  async getActiveSyncs(): Promise<EtsyOrderSyncStatus[]> {
    const activeSyncs: EtsyOrderSyncStatus[] = [];

    for (const [jobId, status] of this.syncStatuses.entries()) {
      if (status.status === 'waiting' || status.status === 'active') {
        activeSyncs.push(await this.getSyncStatus(jobId) || status);
      }
    }

    return activeSyncs;
  }

  /**
   * Clean up old sync statuses (older than 24 hours)
   */
  async cleanupOldSyncs(): Promise<void> {
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

    for (const [jobId, status] of this.syncStatuses.entries()) {
      if (status.completedAt && status.completedAt < oneDayAgo) {
        this.syncStatuses.delete(jobId);
      }
    }
  }

  /**
   * Set up job completion handler to manage pagination
   */
  private async setupJobCompletionHandler(jobId: string, options: EtsyOrderSyncQueueOptions): Promise<void> {
    const batchSize = options.batchSize || 50;
    const maxLimit = options.limit || 1000;

    // Listen for job completion events
    this.etsyOrderSyncQueue.on('completed', async (job: Job, result: EtsyOrderSyncJobResult) => {
      if (job.data.jobId !== jobId) {
        return;
      }

      const status = this.syncStatuses.get(jobId);
      if (!status) {
        return;
      }

      try {
        // Update status with results
        status.completedJobs++;
        status.totalOrdersProcessed += result.ordersProcessed;
        status.totalOrdersCreated += result.ordersCreated;
        status.totalOrdersUpdated += result.ordersUpdated;
        status.totalOrdersSkipped += result.ordersSkipped;
        status.errors.push(...result.errors);

        this.logger.log(`Etsy batch completed - JobID: ${jobId}, Batch results:`, result);

        // Check if we need to create another batch
        if (result.hasMore && result.nextOffset !== undefined) {
          const totalProcessedSoFar = status.totalOrdersProcessed;
          
          // Check if we've reached the limit
          if (totalProcessedSoFar < maxLimit) {
            const remainingLimit = maxLimit - totalProcessedSoFar;
            const nextBatchSize = Math.min(batchSize, remainingLimit);

            // Create next batch job
            const nextJobData: EtsyOrderSyncJobData = {
              startDate: options.startDate,
              endDate: options.endDate,
              offset: result.nextOffset,
              limit: nextBatchSize,
              totalExpected: maxLimit,
              jobId,
              forceUpdate: options.forceUpdate || false,
            };

            await this.etsyOrderSyncQueue.add('sync-orders-batch', nextJobData, {
              attempts: 3,
              backoff: {
                type: 'exponential',
                delay: 2000,
              },
              removeOnComplete: 10,
              removeOnFail: 5,
              delay: 1000, // Add delay between batches for rate limiting
            });

            status.totalJobs++;
            this.logger.log(`Added next Etsy batch - JobID: ${jobId}, Offset: ${result.nextOffset}`);
          } else {
            // Reached limit, mark as completed
            status.status = 'completed';
            status.completedAt = new Date();
            this.logger.log(`Etsy order sync completed (limit reached) - JobID: ${jobId}`);
          }
        } else {
          // No more data, mark as completed
          status.status = 'completed';
          status.completedAt = new Date();
          this.logger.log(`Etsy order sync completed (no more data) - JobID: ${jobId}`);
        }

        // Update progress
        if (status.totalJobs > 0) {
          status.progress = Math.round((status.completedJobs / status.totalJobs) * 100);
        }

      } catch (error) {
        this.logger.error(`Error handling Etsy batch completion - JobID: ${jobId}:`, error);
        status.status = 'failed';
        status.errors.push(`Batch completion error: ${error.message}`);
      }
    });

    // Listen for job failure events
    this.etsyOrderSyncQueue.on('failed', async (job: Job, error: Error) => {
      if (job.data.jobId !== jobId) {
        return;
      }

      const status = this.syncStatuses.get(jobId);
      if (!status) {
        return;
      }

      status.failedJobs++;
      status.errors.push(`Job failed: ${error.message}`);

      // If too many failures, mark the entire sync as failed
      if (status.failedJobs >= 3) {
        status.status = 'failed';
        status.completedAt = new Date();
        this.logger.error(`Etsy order sync failed (too many failures) - JobID: ${jobId}`);
      }
    });
  }
}
