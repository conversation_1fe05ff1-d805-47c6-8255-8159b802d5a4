import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { BullModule } from '@nestjs/bull';

import { DatabaseModule } from '@infrastructure/database/database.module';
import { SharedModule } from '@shared/shared.module';
import { AmazonApiService } from '@infrastructure/external/amazon/amazon-api.service';
import { EtsyApiService } from '@infrastructure/external/etsy/etsy-api.service';
import { ShopifyApiService } from '@infrastructure/external/shopify/shopify-api.service';

import { PlatformController } from './platform.controller';
import { PlatformService } from './platform.service';
import { DesignColorCacheController } from './controllers/design-color-cache.controller';
import { AmazonSyncService } from './services/amazon-sync.service';
import { EtsySyncService } from './services/etsy-sync.service';
import { ShopifySyncService } from './services/shopify-sync.service';
import { PlatformSyncService } from './services/platform-sync.service';
import { EtsyOrderSyncQueueService } from './services/etsy-order-sync-queue.service';
import { EtsyOrderSyncProcessor } from './processors/etsy-order-sync.processor';

@Module({
  imports: [
    ConfigModule,
    DatabaseModule,
    SharedModule,
    // Register queue for Etsy order sync
    BullModule.registerQueue({
      name: 'etsy-order-sync',
    }),
  ],
  controllers: [PlatformController, DesignColorCacheController],
  providers: [
    // API Services
    AmazonApiService,
    EtsyApiService,
    ShopifyApiService,

    // Sync Services
    AmazonSyncService,
    EtsySyncService,
    ShopifySyncService,
    PlatformSyncService,

    // Queue Services
    EtsyOrderSyncQueueService,

    // Queue Processors
    EtsyOrderSyncProcessor,

    // Main Service
    PlatformService,
  ],
  exports: [
    PlatformService,
    AmazonSyncService,
    EtsySyncService,
    ShopifySyncService,
    EtsyOrderSyncQueueService,
    PlatformSyncService,
  ],
})
export class PlatformModule {}
