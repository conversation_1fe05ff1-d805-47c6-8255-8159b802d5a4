import { Controller, Get, Post, Param, Delete } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import {
  HealthCheckService,
  HealthCheck,
  TypeOrmHealthIndicator,
  MemoryHealthIndicator,
  DiskHealthIndicator,
} from '@nestjs/terminus';

import { Public } from '@presentation/auth/decorators/public.decorator';
import { SeederService } from '@infrastructure/database/seeders/seeder.service';
import { UserRepository } from '@infrastructure/database/repositories/user.repository';
import { OrderRepository } from '@infrastructure/database/repositories/order.repository';
import { OrderItemRepository } from '@infrastructure/database/repositories/order-item.repository';
import { UserRole } from '@domain/user/user-role.enum';

@ApiTags('Health')
@Controller('health')
export class HealthController {
  constructor(
    private health: HealthCheckService,
    private db: TypeOrmHealthIndicator,
    private memory: MemoryHealthIndicator,
    private disk: DiskHealthIndicator,
    private seederService: SeederService,
    private userRepository: UserRepository,
    private orderRepository: OrderRepository,
    private orderItemRepository: OrderItemRepository,
  ) {}

  @Get()
  @Public()
  @ApiOperation({ summary: 'Health check' })
  @ApiResponse({ status: 200, description: 'Health check successful' })
  @HealthCheck()
  check() {
    return this.health.check([
      () => this.db.pingCheck('database'),
      () => this.memory.checkHeap('memory_heap', 150 * 1024 * 1024),
      () => this.memory.checkRSS('memory_rss', 150 * 1024 * 1024),
      () => this.disk.checkStorage('storage', { path: '/', thresholdPercent: 0.9 }),
    ]);
  }

  @Get('ready')
  @Public()
  @ApiOperation({ summary: 'Readiness check' })
  @ApiResponse({ status: 200, description: 'Service is ready' })
  @HealthCheck()
  readiness() {
    return this.health.check([() => this.db.pingCheck('database')]);
  }

  @Get('live')
  @Public()
  @ApiOperation({ summary: 'Liveness check' })
  @ApiResponse({ status: 200, description: 'Service is alive' })
  alive() {
    return { status: 'ok', timestamp: new Date().toISOString() };
  }

  @Post('seed')
  @Public()
  @ApiOperation({ summary: 'Seed database with sample data' })
  @ApiResponse({ status: 200, description: 'Database seeded successfully' })
  async seed() {
    await this.seederService.seedAll();
    return { status: 'success', message: 'Database seeded successfully' };
  }

  @Post('verify-user/:email')
  @Public()
  @ApiOperation({ summary: 'Manually verify user email for testing' })
  @ApiResponse({ status: 200, description: 'User verified successfully' })
  async verifyUser(@Param('email') email: string) {
    const user = await this.userRepository.findByEmail(email);
    if (!user) {
      return { status: 'error', message: 'User not found' };
    }

    user.verifyEmail();
    await this.userRepository.save(user);

    return { status: 'success', message: 'User verified successfully' };
  }

  @Post('promote-admin/:email')
  @Public()
  @ApiOperation({ summary: 'Promote a user to admin (dev only)' })
  @ApiResponse({ status: 200, description: 'User promoted to admin' })
  async promoteAdmin(@Param('email') email: string) {
    const user = await this.userRepository.findByEmail(email);
    if (!user) {
      return { status: 'error', message: 'User not found' };
    }

    user.changeRole(UserRole.ADMIN);
    user.verifyEmail();
    user.activate();
    await this.userRepository.save(user);

    return { status: 'success', message: 'User promoted to admin' };
  }
}
