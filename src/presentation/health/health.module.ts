import { Modu<PERSON> } from '@nestjs/common';
import { TerminusModule } from '@nestjs/terminus';
import { HttpModule } from '@nestjs/axios';

import { DatabaseModule } from '@infrastructure/database/database.module';
import { SeederService } from '@infrastructure/database/seeders/seeder.service';
import { OrderSeeder } from '@infrastructure/database/seeders/order.seeder';

import { HealthController } from './health.controller';
import { HealthService } from './health.service';

@Module({
  imports: [TerminusModule, HttpModule, DatabaseModule],
  controllers: [HealthController],
  providers: [HealthService, SeederService, OrderSeeder],
})
export class HealthModule {}
