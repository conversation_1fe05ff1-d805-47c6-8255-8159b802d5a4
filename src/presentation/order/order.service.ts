import { Injectable, Logger } from '@nestjs/common';
import { OrderRepository } from '@infrastructure/database/repositories/order.repository';
import { OrderItemRepository } from '@infrastructure/database/repositories/order-item.repository';
import { PlatformSource } from '@domain/product/platform-source.enum';
import { OrderStatus } from '@domain/order/order-status.enum';
import {
  Between,
  FindManyOptions,
  MoreThanOrEqual,
  LessThanOrEqual,
  Like,
  DataSource,
} from 'typeorm';
import { Order } from '@domain/order/order.entity';
import { ProductAttributeExtractor } from '@shared/utils/product-attribute-extractor.util';

export interface OrderFilters {
  platform?: PlatformSource;
  status?: OrderStatus;
  startDate?: Date;
  endDate?: Date;
  customerEmail?: string;
  customerName?: string;
  orderNumber?: string;
  externalOrderId?: string;
  search?: string;
  minAmount?: number;
  maxAmount?: number;
}

export interface PaginationParams {
  page: number;
  limit: number;
}

export interface OrderListResponse {
  orders: any[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  filters: OrderFilters;
}

@Injectable()
export class OrderService {
  private readonly logger = new Logger(OrderService.name);

  constructor(
    private readonly orderRepository: OrderRepository,
    private readonly orderItemRepository: OrderItemRepository,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Get orders with pagination and filters
   */
  async getOrdersWithPagination(
    pagination: PaginationParams,
    filters: OrderFilters = {},
  ): Promise<OrderListResponse> {
    this.logger.log(`Getting orders with pagination: ${JSON.stringify({ pagination, filters })}`);

    const orderRepo = this.dataSource.getRepository(Order);
    const queryBuilder = orderRepo
      .createQueryBuilder('order')
      .leftJoinAndSelect('order.items', 'items')
      .orderBy('order.externalCreatedAt', 'DESC')
      .skip((pagination.page - 1) * pagination.limit)
      .take(pagination.limit);

    // Apply filters
    if (filters.platform) {
      queryBuilder.andWhere('order.source = :platform', { platform: filters.platform });
    }

    if (filters.status) {
      queryBuilder.andWhere('order.status = :status', { status: filters.status });
    }

    if (filters.customerEmail) {
      queryBuilder.andWhere('order.customerEmail ILIKE :customerEmail', {
        customerEmail: `%${filters.customerEmail}%`,
      });
    }

    if (filters.customerName) {
      queryBuilder.andWhere('order.customerName ILIKE :customerName', {
        customerName: `%${filters.customerName}%`,
      });
    }

    if (filters.orderNumber) {
      queryBuilder.andWhere('order.orderNumber ILIKE :orderNumber', {
        orderNumber: `%${filters.orderNumber}%`,
      });
    }

    if (filters.externalOrderId) {
      queryBuilder.andWhere('order.externalOrderId ILIKE :externalOrderId', {
        externalOrderId: `%${filters.externalOrderId}%`,
      });
    }

    // General search across multiple fields
    if (filters.search) {
      queryBuilder.andWhere(
        '(order.orderNumber ILIKE :search OR order.customerName ILIKE :search OR order.customerEmail ILIKE :search OR order.externalOrderId ILIKE :search)',
        { search: `%${filters.search}%` },
      );
    }

    if (filters.startDate && filters.endDate) {
      queryBuilder.andWhere('order.externalCreatedAt BETWEEN :startDate AND :endDate', {
        startDate: filters.startDate,
        endDate: filters.endDate,
      });
    } else if (filters.startDate) {
      queryBuilder.andWhere('order.externalCreatedAt >= :startDate', {
        startDate: filters.startDate,
      });
    } else if (filters.endDate) {
      queryBuilder.andWhere('order.externalCreatedAt <= :endDate', {
        endDate: filters.endDate,
      });
    }

    if (filters.minAmount && filters.maxAmount) {
      queryBuilder.andWhere('order.totalPrice BETWEEN :minAmount AND :maxAmount', {
        minAmount: filters.minAmount,
        maxAmount: filters.maxAmount,
      });
    } else if (filters.minAmount) {
      queryBuilder.andWhere('order.totalPrice >= :minAmount', {
        minAmount: filters.minAmount,
      });
    } else if (filters.maxAmount) {
      queryBuilder.andWhere('order.totalPrice <= :maxAmount', {
        maxAmount: filters.maxAmount,
      });
    }

    // Execute queries
    const [orders, total] = await queryBuilder.getManyAndCount();

    // Calculate pagination metadata
    const totalPages = Math.ceil(total / pagination.limit);
    const hasNext = pagination.page < totalPages;
    const hasPrevious = pagination.page > 1;

    return {
      orders: orders.map(order => this.formatOrderForResponse(order)),
      pagination: {
        page: pagination.page,
        limit: pagination.limit,
        total,
        totalPages,
        hasNext,
        hasPrev: hasPrevious,
      },
      filters,
    };
  }

  /**
   * Get order statistics
   */
  async getOrderStatistics(): Promise<{
    totalOrders: number;
    totalRevenue: number;
    ordersByPlatform: Record<PlatformSource, number>;
    ordersByStatus: Record<OrderStatus, number>;
    recentOrders: number;
    averageOrderValue: number;
  }> {
    this.logger.log('Getting order statistics');

    // Get total orders and revenue
    const orderRepo = this.dataSource.getRepository(Order);
    const totalOrders = await orderRepo.count();
    const revenueResult = await orderRepo
      .createQueryBuilder('order')
      .select('SUM(order.totalPrice)', 'totalRevenue')
      .getRawOne();
    const totalRevenue = parseFloat(revenueResult?.totalRevenue || '0');

    // Get orders by platform
    const platformStats = await orderRepo
      .createQueryBuilder('order')
      .select('order.source', 'platform')
      .addSelect('COUNT(*)', 'count')
      .groupBy('order.source')
      .getRawMany();

    const ordersByPlatform: Record<PlatformSource, number> = {
      [PlatformSource.SHOPIFY]: 0,
      [PlatformSource.ETSY]: 0,
      [PlatformSource.AMAZON]: 0,
      [PlatformSource.ORDERS]: 0,
      [PlatformSource.MANUAL]: 0,
    };

    platformStats.forEach(stat => {
      if (stat.platform in ordersByPlatform) {
        ordersByPlatform[stat.platform as PlatformSource] = parseInt(stat.count);
      }
    });

    // Get orders by status
    const statusStats = await orderRepo
      .createQueryBuilder('order')
      .select('order.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .groupBy('order.status')
      .getRawMany();

    const ordersByStatus: Record<OrderStatus, number> = {
      [OrderStatus.PENDING]: 0,
      [OrderStatus.CONFIRMED]: 0,
      [OrderStatus.PROCESSING]: 0,
      [OrderStatus.SHIPPED]: 0,
      [OrderStatus.DELIVERED]: 0,
      [OrderStatus.CANCELLED]: 0,
      [OrderStatus.REFUNDED]: 0,
      [OrderStatus.RETURNED]: 0,
    };

    statusStats.forEach(stat => {
      if (stat.status in ordersByStatus) {
        ordersByStatus[stat.status as OrderStatus] = parseInt(stat.count);
      }
    });

    // Get recent orders (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    const recentOrders = await orderRepo.count({
      where: { externalCreatedAt: MoreThanOrEqual(sevenDaysAgo) },
    });

    // Calculate average order value
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

    return {
      totalOrders,
      totalRevenue,
      ordersByPlatform,
      ordersByStatus,
      recentOrders,
      averageOrderValue,
    };
  }

  /**
   * Get recent orders
   */
  async getRecentOrders(limit: number = 10): Promise<any[]> {
    this.logger.log(`Getting ${limit} recent orders`);

    const orderRepo = this.dataSource.getRepository(Order);
    const orders = await orderRepo.find({
      relations: ['items'],
      order: { externalCreatedAt: 'DESC' },
      take: limit,
    });

    return orders.map(order => this.formatOrderForResponse(order));
  }

  /**
   * Get order by ID
   */
  async getOrderById(id: string): Promise<any> {
    this.logger.log(`Getting order by ID: ${id}`);

    const orderRepo = this.dataSource.getRepository(Order);
    const order = await orderRepo.findOne({
      where: { id },
      relations: ['items'],
    });

    if (!order) {
      return null;
    }

    // Extract attributes for order items if not already extracted
    await this.ensureOrderItemAttributes(order);

    return this.formatOrderForResponse(order);
  }

  /**
   * Ensure order items have extracted attributes
   */
  private async ensureOrderItemAttributes(order: any): Promise<void> {
    if (!order.items || order.items.length === 0) {
      return;
    }

    for (const item of order.items) {
      // Check if item already has extracted attributes
      if (item.style || item.color || item.design || item.size) {
        continue; // Already has attributes
      }

      // Extract attributes from SKU and title
      const attributes = this.extractAttributesFromOrderItem(item);

      if (attributes.style || attributes.color || attributes.design || attributes.size) {
        // Update the item with extracted attributes (in memory only for response)
        item.style = attributes.style;
        item.color = attributes.color;
        item.design = attributes.design;
        item.size = attributes.size;
        item.isCustom = attributes.isCustom;
        item.isEngrave = attributes.isEngrave;
        item.customization = attributes.customization;
        item.engraving = attributes.engraving;
      }
    }
  }

  /**
   * Format order for API response
   */
  private formatOrderForResponse(order: any): any {
    return {
      id: order.id,
      externalOrderId: order.externalOrderId,
      orderNumber: order.orderNumber,
      status: order.status,
      source: order.source,
      customerName: order.customerName,
      customerEmail: order.customerEmail,
      customerPhone: order.customerPhone,
      shippingAddress: {
        line1: order.shippingAddressLine1,
        line2: order.shippingAddressLine2,
        city: order.shippingCity,
        state: order.shippingState,
        postalCode: order.shippingPostalCode,
        country: order.shippingCountry,
      },
      subtotalPrice: order.subtotalPrice,
      shippingPrice: order.shippingPrice,
      taxAmount: order.taxAmount,
      discountAmount: order.discountAmount,
      totalPrice: order.totalPrice,
      currency: order.currency,
      customerNote: order.customerNote,
      isProcessed: order.isProcessed,
      externalCreatedAt: order.externalCreatedAt,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
      orderItems:
        order.items?.map((item: any) => ({
          id: item.id,
          sku: item.sku,
          title: item.title,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          totalPrice: item.totalPrice,
          currency: item.currency,
          isCustom: item.isCustom,
          isEngrave: item.isEngrave,
          customText: item.customText,
          engraveText: item.engraveText,
          // Extracted product attributes
          productAttributes: {
            style: item.style,
            color: item.color,
            design: item.design,
            size: item.size,
            customization: item.customization,
            engraving: item.engraving,
          },
          // Platform-specific data
          platformData: this.extractPlatformSpecificData(item, order.source),
        })) || [],
      metadata: order.metadata,
    };
  }

  /**
   * Extract attributes for order items that don't have them
   */
  async extractOrderItemAttributes(orderId: string): Promise<{
    processed: number;
    updated: number;
    errors: number;
  }> {
    this.logger.log(`Extracting attributes for order ${orderId}`);

    let processed = 0;
    let updated = 0;
    let errors = 0;

    try {
      // Get order items for this order
      const orderItems = await this.orderItemRepository.find({
        where: { orderId },
      });

      for (const orderItem of orderItems) {
        try {
          processed++;

          // Check if item already has attributes
          if (orderItem.style || orderItem.color || orderItem.design || orderItem.size) {
            continue; // Skip if already has some attributes
          }

          // Extract attributes from raw data
          const attributes = this.extractAttributesFromOrderItem(orderItem);

          // Update the order item with extracted attributes
          if (attributes.style || attributes.color || attributes.design || attributes.size) {
            orderItem.updateProductInfo({
              style: attributes.style,
              color: attributes.color,
              design: attributes.design,
              size: attributes.size,
            });

            // Update customization info
            if (attributes.isCustom || attributes.isEngrave) {
              orderItem.updateCustomization({
                customization: attributes.customization,
                engraving: attributes.engraving,
                isCustom: attributes.isCustom,
                isEngrave: attributes.isEngrave,
              });
            }

            // Add extracted attributes to metadata
            if (!orderItem.metadata) {
              orderItem.metadata = {};
            }
            orderItem.metadata.extractedAttributes = attributes;
            orderItem.metadata.attributeExtraction = {
              extractedAt: new Date().toISOString(),
              version: '1.0',
            };

            await this.orderItemRepository.save(orderItem);
            updated++;

            this.logger.debug(`Updated order item ${orderItem.id} with attributes`);
          }
        } catch (error) {
          this.logger.error(`Failed to extract attributes for order item ${orderItem.id}:`, error);
          errors++;
        }
      }

      this.logger.log(
        `Attribute extraction for order ${orderId} completed: ${processed} processed, ${updated} updated, ${errors} errors`,
      );

      return { processed, updated, errors };
    } catch (error) {
      this.logger.error(`Failed to extract attributes for order ${orderId}:`, error);
      throw error;
    }
  }

  /**
   * Extract attributes from an existing order item using its raw data
   */
  private extractAttributesFromOrderItem(orderItem: any): {
    style?: string;
    color?: string;
    size?: number;
    design?: string;
    isCustom: boolean;
    isEngrave: boolean;
    customization?: string;
    engraving?: string;
  } {
    const rawData = orderItem.rawData || {};
    const platform = rawData.platform;

    // Extract based on platform
    switch (platform) {
      case 'shopify':
        return this.extractFromShopifyData(rawData);
      case 'etsy':
        return this.extractFromEtsyData(rawData);
      case 'amazon':
        return this.extractFromAmazonData(rawData);
      default:
        // Fallback to basic extraction
        return ProductAttributeExtractor.extractAttributes({
          title: orderItem.title,
          sku: orderItem.sku,
        });
    }
  }

  /**
   * Extract attributes from Shopify raw data
   */
  private extractFromShopifyData(rawData: any): any {
    const lineItem = rawData.lineItem;
    if (!lineItem) return {};

    return ProductAttributeExtractor.extractAttributes({
      title: lineItem.title,
      description: lineItem.variant_title,
      sku: lineItem.sku,
      variantTitle: lineItem.variant_title,
      properties: lineItem.properties?.map((prop: any) => ({
        name: prop.name,
        value: prop.value,
      })),
      metadata: {
        vendor: lineItem.vendor,
        productId: lineItem.product_id,
        variantId: lineItem.variant_id,
      },
    });
  }

  /**
   * Extract attributes from Etsy raw data
   */
  private extractFromEtsyData(rawData: any): any {
    const transaction = rawData.transaction;
    if (!transaction) return {};

    return ProductAttributeExtractor.extractAttributes({
      title: transaction.title,
      description: transaction.description,
      sku: transaction.sku,
      properties: transaction.variations?.map((v: any) => ({
        name: v.formatted_name || '',
        value: v.formatted_value || '',
      })),
      variations: transaction.variations?.map((v: any) => ({
        name: v.formatted_name || '',
        value: v.formatted_value || '',
      })),
      productData: transaction.product_data,
      metadata: {
        listingId: transaction.listing_id,
        transactionId: transaction.transaction_id,
      },
    });
  }

  /**
   * Extract attributes from Amazon raw data
   */
  private extractFromAmazonData(rawData: any): any {
    const amazonData = rawData.amazonData;
    if (!amazonData) return {};

    return ProductAttributeExtractor.extractAttributes({
      title: amazonData.title,
      description: amazonData.productInfo?.title || '',
      sku: amazonData.sellerSku,
      metadata: {
        asin: amazonData.asin,
        orderItemId: amazonData.orderItemId,
        conditionId: amazonData.conditionId,
        isGift: amazonData.isGift,
        productInfo: amazonData.productInfo,
      },
    });
  }

  /**
   * Extract platform-specific data for order items
   */
  private extractPlatformSpecificData(item: any, platform: string): any {
    const rawData = item.rawData || {};

    switch (platform.toLowerCase()) {
      case 'etsy':
        return this.extractEtsyPlatformData(rawData);
      case 'shopify':
        return this.extractShopifyPlatformData(rawData);
      case 'amazon':
        return this.extractAmazonPlatformData(rawData);
      default:
        return {
          platform: platform,
          rawData: rawData,
        };
    }
  }

  /**
   * Extract Etsy-specific platform data
   */
  private extractEtsyPlatformData(rawData: any): any {
    const transaction = rawData.transaction;
    if (!transaction) {
      return {
        platform: 'etsy',
        rawData: rawData,
      };
    }

    return {
      platform: 'etsy',
      listingId: transaction.listing_id,
      transactionId: transaction.transaction_id,
      shopId: transaction.shop_id,
      variations:
        transaction.variations?.map((v: any) => ({
          name: v.formatted_name || v.name,
          value: v.formatted_value || v.value,
          propertyId: v.property_id,
        })) || [],
      productData:
        transaction.product_data?.map((pd: any) => ({
          propertyName: pd.property_name,
          values: pd.values,
          scaleId: pd.scale_id,
          scaleName: pd.scale_name,
        })) || [],
      images:
        transaction.images?.map((img: any) => ({
          url: img.url_fullxfull,
          width: img.full_width,
          height: img.full_height,
        })) || [],
      tags: transaction.tags || [],
      materials: transaction.materials || [],
      rawData: rawData,
    };
  }

  /**
   * Extract Shopify-specific platform data
   */
  private extractShopifyPlatformData(rawData: any): any {
    const lineItem = rawData.lineItem;
    if (!lineItem) {
      return {
        platform: 'shopify',
        rawData: rawData,
      };
    }

    return {
      platform: 'shopify',
      productId: lineItem.product_id,
      variantId: lineItem.variant_id,
      vendor: lineItem.vendor,
      productType: lineItem.product_type,
      properties:
        lineItem.properties?.map((prop: any) => ({
          name: prop.name,
          value: prop.value,
        })) || [],
      variantTitle: lineItem.variant_title,
      variantOptions: lineItem.variant_options || [],
      fulfillmentService: lineItem.fulfillment_service,
      requiresShipping: lineItem.requires_shipping,
      taxable: lineItem.taxable,
      giftCard: lineItem.gift_card,
      rawData: rawData,
    };
  }

  /**
   * Extract Amazon-specific platform data
   */
  private extractAmazonPlatformData(rawData: any): any {
    const amazonData = rawData.amazonData;
    if (!amazonData) {
      return {
        platform: 'amazon',
        rawData: rawData,
      };
    }

    return {
      platform: 'amazon',
      asin: amazonData.asin,
      orderItemId: amazonData.orderItemId,
      conditionId: amazonData.conditionId,
      conditionNote: amazonData.conditionNote,
      isGift: amazonData.isGift,
      isGiftMessageAvailable: amazonData.isGiftMessageAvailable,
      isGiftWrapAvailable: amazonData.isGiftWrapAvailable,
      productInfo: {
        title: amazonData.productInfo?.title,
        brand: amazonData.productInfo?.brand,
        color: amazonData.productInfo?.color,
        size: amazonData.productInfo?.size,
        features: amazonData.productInfo?.features || [],
        images: amazonData.productInfo?.images || [],
      },
      pricing: {
        itemPrice: amazonData.itemPrice,
        itemTax: amazonData.itemTax,
        shippingPrice: amazonData.shippingPrice,
        shippingTax: amazonData.shippingTax,
        giftWrapPrice: amazonData.giftWrapPrice,
        giftWrapTax: amazonData.giftWrapTax,
      },
      rawData: rawData,
    };
  }
}
