import {
  Controller,
  Get,
  Query,
  UseGuards,
  <PERSON><PERSON><PERSON>nt<PERSON><PERSON><PERSON>,
  De<PERSON>ultV<PERSON>uePipe,
  Param,
  Post,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';

import { JwtAuthGuard } from '@presentation/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@presentation/auth/guards/roles.guard';
import { Roles } from '@presentation/auth/decorators/roles.decorator';
import { UserRole } from '@domain/user/user-role.enum';
import { PlatformSource } from '@domain/product/platform-source.enum';
import { OrderStatus } from '@domain/order/order-status.enum';

import { OrderService } from './order.service';

export interface OrderFilters {
  platform?: PlatformSource;
  status?: OrderStatus;
  startDate?: Date;
  endDate?: Date;
  customerEmail?: string;
  customerName?: string;
  orderNumber?: string;
  externalOrderId?: string;
  search?: string;
  minAmount?: number;
  maxAmount?: number;
}

export interface PaginationParams {
  page: number;
  limit: number;
}

export interface OrderListResponse {
  orders: any[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  filters: OrderFilters;
}

@ApiTags('Orders')
@Controller('orders')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth('JWT-auth')
export class OrderController {
  constructor(private readonly orderService: OrderService) {}

  @Get()
  @Roles(UserRole.ADMIN, UserRole.USER)
  @ApiOperation({ summary: 'Get orders with pagination and filters' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 20, max: 100)',
  })
  @ApiQuery({
    name: 'platform',
    required: false,
    enum: PlatformSource,
    description: 'Filter by platform (alias: source)',
  })
  @ApiQuery({
    name: 'source',
    required: false,
    enum: PlatformSource,
    description: 'Filter by platform source (alias: platform)',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: OrderStatus,
    description: 'Filter by order status',
  })
  @ApiQuery({
    name: 'startDate',
    required: false,
    type: String,
    description: 'Start date (ISO string)',
  })
  @ApiQuery({
    name: 'endDate',
    required: false,
    type: String,
    description: 'End date (ISO string)',
  })
  @ApiQuery({
    name: 'customerEmail',
    required: false,
    type: String,
    description: 'Filter by customer email',
  })
  @ApiQuery({
    name: 'minAmount',
    required: false,
    type: Number,
    description: 'Minimum order amount',
  })
  @ApiQuery({
    name: 'maxAmount',
    required: false,
    type: Number,
    description: 'Maximum order amount',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Search in order number, customer name, customer email, or external order ID',
  })
  @ApiQuery({
    name: 'customerName',
    required: false,
    type: String,
    description: 'Filter by customer name',
  })
  @ApiQuery({
    name: 'orderNumber',
    required: false,
    type: String,
    description: 'Filter by order number',
  })
  @ApiQuery({
    name: 'externalOrderId',
    required: false,
    type: String,
    description: 'Filter by external order ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Orders retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            orders: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  externalOrderId: { type: 'string' },
                  orderNumber: { type: 'string' },
                  status: { type: 'string', enum: Object.values(OrderStatus) },
                  source: { type: 'string', enum: Object.values(PlatformSource) },
                  customerName: { type: 'string' },
                  customerEmail: { type: 'string' },
                  totalPrice: { type: 'number' },
                  currency: { type: 'string' },
                  externalCreatedAt: { type: 'string', format: 'date-time' },
                  createdAt: { type: 'string', format: 'date-time' },
                  updatedAt: { type: 'string', format: 'date-time' },
                },
              },
            },
            pagination: {
              type: 'object',
              properties: {
                page: { type: 'number' },
                limit: { type: 'number' },
                total: { type: 'number' },
                totalPages: { type: 'number' },
                hasNext: { type: 'boolean' },
                hasPrev: { type: 'boolean' },
              },
            },
            filters: { type: 'object' },
          },
        },
      },
    },
  })
  async getOrders(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(20), ParseIntPipe) limit: number,
    @Query('platform') platform?: PlatformSource,
    @Query('source') source?: PlatformSource,
    @Query('status') status?: OrderStatus,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('customerEmail') customerEmail?: string,
    @Query('customerName') customerName?: string,
    @Query('orderNumber') orderNumber?: string,
    @Query('externalOrderId') externalOrderId?: string,
    @Query('search') search?: string,
    @Query('minAmount') minAmount?: number,
    @Query('maxAmount') maxAmount?: number,
  ): Promise<OrderListResponse> {
    // Validate and sanitize inputs
    const validatedPage = Math.max(1, page);
    const validatedLimit = Math.min(100, Math.max(1, limit));

    const filters: OrderFilters = {};

    // Handle both 'platform' and 'source' parameters (source takes precedence)
    const platformFilter = source || platform;
    if (platformFilter && Object.values(PlatformSource).includes(platformFilter)) {
      filters.platform = platformFilter;
    }

    if (status && Object.values(OrderStatus).includes(status)) {
      filters.status = status;
    }

    if (startDate) {
      const parsedStartDate = new Date(startDate);
      if (!isNaN(parsedStartDate.getTime())) {
        filters.startDate = parsedStartDate;
      }
    }

    if (endDate) {
      const parsedEndDate = new Date(endDate);
      if (!isNaN(parsedEndDate.getTime())) {
        filters.endDate = parsedEndDate;
      }
    }

    if (customerEmail) {
      filters.customerEmail = customerEmail.trim();
    }

    if (customerName) {
      filters.customerName = customerName.trim();
    }

    if (orderNumber) {
      filters.orderNumber = orderNumber.trim();
    }

    if (externalOrderId) {
      filters.externalOrderId = externalOrderId.trim();
    }

    if (search) {
      filters.search = search.trim();
    }

    if (minAmount !== undefined && !isNaN(minAmount)) {
      filters.minAmount = Math.max(0, minAmount);
    }

    if (maxAmount !== undefined && !isNaN(maxAmount)) {
      filters.maxAmount = Math.max(0, maxAmount);
    }

    const pagination: PaginationParams = {
      page: validatedPage,
      limit: validatedLimit,
    };

    const result = await this.orderService.getOrdersWithPagination(pagination, filters);

    return result;
  }

  @Get('stats')
  @Roles(UserRole.ADMIN, UserRole.USER)
  @ApiOperation({ summary: 'Get order statistics' })
  @ApiResponse({
    status: 200,
    description: 'Order statistics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            totalOrders: { type: 'number' },
            totalRevenue: { type: 'number' },
            ordersByPlatform: { type: 'object' },
            ordersByStatus: { type: 'object' },
            recentOrders: { type: 'number' },
            averageOrderValue: { type: 'number' },
          },
        },
      },
    },
  })
  async getOrderStats(): Promise<{
    totalOrders: number;
    totalRevenue: number;
    ordersByPlatform: Record<PlatformSource, number>;
    ordersByStatus: Record<OrderStatus, number>;
    recentOrders: number;
    averageOrderValue: number;
  }> {
    const stats = await this.orderService.getOrderStatistics();

    return stats;
  }

  @Get('recent')
  @Roles(UserRole.ADMIN, UserRole.USER)
  @ApiOperation({ summary: 'Get recent orders from last week' })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of recent orders (default: 50)',
  })
  @ApiResponse({
    status: 200,
    description: 'Recent orders retrieved successfully',
  })
  async getRecentOrders(
    @Query('limit', new DefaultValuePipe(50), ParseIntPipe) limit: number,
  ): Promise<any[]> {
    const validatedLimit = Math.min(200, Math.max(1, limit));

    // Get orders from last week
    const lastWeek = new Date();
    lastWeek.setDate(lastWeek.getDate() - 7);

    const filters: OrderFilters = {
      startDate: lastWeek,
    };

    const pagination: PaginationParams = {
      page: 1,
      limit: validatedLimit,
    };

    const result = await this.orderService.getOrdersWithPagination(pagination, filters);

    return result.orders;
  }

  @Get(':id')
  @Roles(UserRole.ADMIN, UserRole.USER)
  @ApiOperation({ summary: 'Get order by ID' })
  @ApiResponse({
    status: 200,
    description: 'Order retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            externalOrderId: { type: 'string' },
            orderNumber: { type: 'string' },
            status: { type: 'string', enum: Object.values(OrderStatus) },
            source: { type: 'string', enum: Object.values(PlatformSource) },
            customerName: { type: 'string' },
            customerEmail: { type: 'string' },
            customerPhone: { type: 'string' },
            shippingAddress: {
              type: 'object',
              properties: {
                line1: { type: 'string' },
                line2: { type: 'string' },
                city: { type: 'string' },
                state: { type: 'string' },
                postalCode: { type: 'string' },
                country: { type: 'string' },
              },
            },
            subtotalPrice: { type: 'string' },
            shippingPrice: { type: 'string' },
            taxAmount: { type: 'string' },
            discountAmount: { type: 'string' },
            totalPrice: { type: 'string' },
            currency: { type: 'string' },
            customerNote: { type: 'string' },
            isProcessed: { type: 'boolean' },
            externalCreatedAt: { type: 'string', format: 'date-time' },
            createdAt: { type: 'string', format: 'date-time' },
            updatedAt: { type: 'string', format: 'date-time' },
            orderItems: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  sku: { type: 'string' },
                  title: { type: 'string' },
                  quantity: { type: 'number' },
                  unitPrice: { type: 'string' },
                  totalPrice: { type: 'string' },
                  currency: { type: 'string' },
                  isCustom: { type: 'boolean' },
                  isEngrave: { type: 'boolean' },
                  customText: { type: 'string' },
                  engraveText: { type: 'string' },
                  productAttributes: {
                    type: 'object',
                    properties: {
                      style: { type: 'string' },
                      color: { type: 'string' },
                      design: { type: 'string' },
                      size: { type: 'number' },
                      customization: { type: 'string' },
                      engraving: { type: 'string' },
                    },
                  },
                  platformData: {
                    type: 'object',
                    properties: {
                      platform: { type: 'string' },
                      listingId: { type: 'string' },
                      transactionId: { type: 'string' },
                      shopId: { type: 'string' },
                      variations: { type: 'array' },
                      productData: { type: 'array' },
                      images: { type: 'array' },
                      tags: { type: 'array' },
                      materials: { type: 'array' },
                    },
                  },
                },
              },
            },
            metadata: { type: 'object' },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Order not found',
  })
  async getOrderById(@Param('id') id: string): Promise<any> {
    const order = await this.orderService.getOrderById(id);
    if (!order) {
      throw new Error('Order not found');
    }
    return order;
  }

  @Post(':id/extract-attributes')
  @Roles(UserRole.ADMIN, UserRole.USER)
  @ApiOperation({ summary: 'Extract product attributes for order items' })
  @ApiResponse({
    status: 200,
    description: 'Attributes extracted successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean' },
        data: {
          type: 'object',
          properties: {
            processed: { type: 'number' },
            updated: { type: 'number' },
            errors: { type: 'number' },
          },
        },
      },
    },
  })
  async extractOrderItemAttributes(@Param('id') id: string): Promise<any> {
    const result = await this.orderService.extractOrderItemAttributes(id);

    return {
      success: true,
      data: result,
      message: `Attribute extraction completed: ${result.processed} processed, ${result.updated} updated, ${result.errors} errors`,
    };
  }
}
