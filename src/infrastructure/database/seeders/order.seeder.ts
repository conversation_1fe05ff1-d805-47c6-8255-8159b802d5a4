import { Injectable, Logger } from '@nestjs/common';
import { OrderRepository } from '../repositories/order.repository';
import { OrderItemRepository } from '../repositories/order-item.repository';
import { Order } from '@domain/order/order.entity';
import { OrderItem } from '@domain/order/order-item.entity';
import { OrderStatus } from '@domain/order/order-status.enum';
import { PlatformSource } from '@domain/product/platform-source.enum';

@Injectable()
export class OrderSeeder {
  private readonly logger = new Logger(OrderSeeder.name);

  constructor(
    private readonly orderRepository: OrderRepository,
    private readonly orderItemRepository: OrderItemRepository,
  ) {}

  async seed(): Promise<void> {
    this.logger.log('Starting order seeding...');

    // Check if orders already exist
    const existingOrders = await this.orderRepository.count();
    if (existingOrders > 0) {
      this.logger.log('Orders already exist, skipping seeding');
      return;
    }

    // Create sample orders
    const orders = await this.createSampleOrders();
    this.logger.log(`Created ${orders.length} sample orders`);
  }

  private async createSampleOrders(): Promise<Order[]> {
    const ordersData = [
      {
        externalOrderId: 'shopify-001',
        orderNumber: '#1001',
        status: OrderStatus.CONFIRMED,
        source: PlatformSource.SHOPIFY,
        externalCreatedAt: new Date('2024-07-10'),
        customerName: 'John Doe',
        customerEmail: '<EMAIL>',
        customerPhone: '+1234567890',
        shippingAddressLine1: '123 Main St',
        shippingCity: 'New York',
        shippingState: 'NY',
        shippingPostalCode: '10001',
        shippingCountry: 'US',
        subtotalPrice: 89.99,
        shippingPrice: 10.0,
        taxAmount: 8.0,
        discountAmount: 0,
        totalPrice: 107.99,
        currency: 'USD',
        customerNote: 'Please handle with care',
        items: [
          {
            sku: 'RING-001',
            title: 'Silver Wedding Ring',
            quantity: 1,
            unitPrice: 89.99,
            totalPrice: 89.99,
            currency: 'USD',
            isCustom: false,
            isEngrave: true,
            engraving: 'Forever & Always',
          },
        ],
      },
      {
        externalOrderId: 'etsy-002',
        orderNumber: '#1002',
        status: OrderStatus.PENDING,
        source: PlatformSource.ETSY,
        externalCreatedAt: new Date('2024-07-11'),
        customerName: 'Jane Smith',
        customerEmail: '<EMAIL>',
        customerPhone: '+1987654321',
        shippingAddressLine1: '456 Oak Ave',
        shippingCity: 'Los Angeles',
        shippingState: 'CA',
        shippingPostalCode: '90210',
        shippingCountry: 'US',
        subtotalPrice: 149.99,
        shippingPrice: 15.0,
        taxAmount: 12.5,
        discountAmount: 5.0,
        totalPrice: 172.49,
        currency: 'USD',
        customerNote: 'Gift wrapping requested',
        items: [
          {
            sku: 'NECKLACE-001',
            title: 'Gold Heart Necklace',
            quantity: 1,
            unitPrice: 149.99,
            totalPrice: 149.99,
            currency: 'USD',
            isCustom: true,
            isEngrave: false,
            customization: 'Custom chain length: 18 inches',
          },
        ],
      },
      {
        externalOrderId: 'shopify-003',
        orderNumber: '#1003',
        status: OrderStatus.SHIPPED,
        source: PlatformSource.SHOPIFY,
        externalCreatedAt: new Date('2024-07-09'),
        customerName: 'Bob Johnson',
        customerEmail: '<EMAIL>',
        shippingAddressLine1: '789 Pine St',
        shippingCity: 'Chicago',
        shippingState: 'IL',
        shippingPostalCode: '60601',
        shippingCountry: 'US',
        subtotalPrice: 199.98,
        shippingPrice: 12.0,
        taxAmount: 16.0,
        discountAmount: 10.0,
        totalPrice: 217.98,
        currency: 'USD',
        items: [
          {
            sku: 'EARRINGS-001',
            title: 'Diamond Stud Earrings',
            quantity: 2,
            unitPrice: 99.99,
            totalPrice: 199.98,
            currency: 'USD',
            isCustom: false,
            isEngrave: false,
          },
        ],
      },
      {
        externalOrderId: 'etsy-004',
        orderNumber: '#1004',
        status: OrderStatus.DELIVERED,
        source: PlatformSource.ETSY,
        externalCreatedAt: new Date('2024-07-08'),
        customerName: 'Alice Brown',
        customerEmail: '<EMAIL>',
        shippingAddressLine1: '321 Elm St',
        shippingCity: 'Miami',
        shippingState: 'FL',
        shippingPostalCode: '33101',
        shippingCountry: 'US',
        subtotalPrice: 75.0,
        shippingPrice: 8.0,
        taxAmount: 6.0,
        discountAmount: 0,
        totalPrice: 89.0,
        currency: 'USD',
        items: [
          {
            sku: 'BRACELET-001',
            title: 'Silver Charm Bracelet',
            quantity: 1,
            unitPrice: 75.0,
            totalPrice: 75.0,
            currency: 'USD',
            isCustom: false,
            isEngrave: true,
            engraving: 'A.B.',
          },
        ],
      },
    ];

    const createdOrders: Order[] = [];

    for (const orderData of ordersData) {
      // Create order
      const order = this.orderRepository.create({
        externalOrderId: orderData.externalOrderId,
        orderNumber: orderData.orderNumber,
        status: orderData.status,
        source: orderData.source,
        externalCreatedAt: orderData.externalCreatedAt,
        customerName: orderData.customerName,
        customerEmail: orderData.customerEmail,
        customerPhone: orderData.customerPhone,
        shippingAddressLine1: orderData.shippingAddressLine1,
        shippingCity: orderData.shippingCity,
        shippingState: orderData.shippingState,
        shippingPostalCode: orderData.shippingPostalCode,
        shippingCountry: orderData.shippingCountry,
        subtotalPrice: orderData.subtotalPrice,
        shippingPrice: orderData.shippingPrice,
        taxAmount: orderData.taxAmount,
        discountAmount: orderData.discountAmount,
        totalPrice: orderData.totalPrice,
        currency: orderData.currency,
        customerNote: orderData.customerNote,
      });

      const savedOrder = await this.orderRepository.save(order);

      // Create order items
      for (const itemData of orderData.items) {
        const orderItem = this.orderItemRepository.create({
          orderId: savedOrder.id,
          sku: itemData.sku,
          title: itemData.title,
          quantity: itemData.quantity,
          unitPrice: itemData.unitPrice,
          totalPrice: itemData.totalPrice,
          currency: itemData.currency,
          isCustom: itemData.isCustom,
          isEngrave: itemData.isEngrave,
          customization: (itemData as any).customization,
          engraving: (itemData as any).engraving,
        });

        await this.orderItemRepository.save(orderItem);
      }

      createdOrders.push(savedOrder);
    }

    return createdOrders;
  }
}
