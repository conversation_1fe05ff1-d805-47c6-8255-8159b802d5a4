import { Injectable } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, ILike } from 'typeorm';

import { User } from '@domain/user/user.entity';
import { UserRole } from '@domain/user/user-role.enum';
import { BaseRepository } from './base.repository';
import { PaginationOptions, PaginationResult } from '@domain/common/repository.interface';

export interface UserSearchOptions {
  email?: string;
  role?: UserRole;
  isActive?: boolean;
  isEmailVerified?: boolean;
  search?: string; // Search in name and email
}

@Injectable()
export class UserRepository extends BaseRepository<User> {
  constructor(@InjectDataSource() dataSource: DataSource) {
    super(dataSource, User);
  }

  /**
   * Find user by email
   */
  async findByEmail(email: string): Promise<User | null> {
    return this.repository.findOne({
      where: { email: email.toLowerCase() },
    });
  }

  /**
   * Find user by reset password token
   */
  async findByResetPasswordToken(token: string): Promise<User | null> {
    return this.repository.findOne({
      where: { 
        resetPasswordToken: token,
      },
    });
  }

  /**
   * Find user by email verification token
   */
  async findByEmailVerificationToken(token: string): Promise<User | null> {
    return this.repository.findOne({
      where: { 
        emailVerificationToken: token,
      },
    });
  }

  /**
   * Search users with filters and pagination
   */
  async searchUsers(
    searchOptions: UserSearchOptions,
    paginationOptions: PaginationOptions,
  ): Promise<PaginationResult<User>> {
    const queryBuilder = this.repository.createQueryBuilder('user');

    // Apply filters
    if (searchOptions.email) {
      queryBuilder.andWhere('user.email ILIKE :email', { 
        email: `%${searchOptions.email}%` 
      });
    }

    if (searchOptions.role) {
      queryBuilder.andWhere('user.role = :role', { role: searchOptions.role });
    }

    if (searchOptions.isActive !== undefined) {
      queryBuilder.andWhere('user.isActive = :isActive', { 
        isActive: searchOptions.isActive 
      });
    }

    if (searchOptions.isEmailVerified !== undefined) {
      queryBuilder.andWhere('user.isEmailVerified = :isEmailVerified', { 
        isEmailVerified: searchOptions.isEmailVerified 
      });
    }

    if (searchOptions.search) {
      queryBuilder.andWhere(
        '(user.firstName ILIKE :search OR user.lastName ILIKE :search OR user.email ILIKE :search)',
        { search: `%${searchOptions.search}%` }
      );
    }

    // Apply pagination
    const { page, limit, orderBy = 'createdAt', order = 'DESC' } = paginationOptions;
    const skip = (page - 1) * limit;

    queryBuilder
      .orderBy(`user.${orderBy}`, order)
      .skip(skip)
      .take(limit);

    const [data, total] = await queryBuilder.getManyAndCount();

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrevious: page > 1,
    };
  }

  /**
   * Find users by role
   */
  async findByRole(role: UserRole): Promise<User[]> {
    return this.repository.find({
      where: { role },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Find active users
   */
  async findActiveUsers(): Promise<User[]> {
    return this.repository.find({
      where: { isActive: true },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Find users with unverified emails
   */
  async findUsersWithUnverifiedEmails(): Promise<User[]> {
    return this.repository.find({
      where: { 
        isEmailVerified: false,
        isActive: true,
      },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Find users with expired password reset tokens
   */
  async findUsersWithExpiredPasswordResetTokens(): Promise<User[]> {
    return this.repository
      .createQueryBuilder('user')
      .where('user.resetPasswordToken IS NOT NULL')
      .andWhere('user.resetPasswordExpires < :now', { now: new Date() })
      .getMany();
  }

  /**
   * Count users by role
   */
  async countByRole(role: UserRole): Promise<number> {
    return this.repository.count({
      where: { role },
    });
  }

  /**
   * Count active users
   */
  async countActiveUsers(): Promise<number> {
    return this.repository.count({
      where: { isActive: true },
    });
  }

  /**
   * Get user statistics
   */
  async getUserStatistics(): Promise<{
    total: number;
    active: number;
    inactive: number;
    admins: number;
    users: number;
    verified: number;
    unverified: number;
  }> {
    const [
      total,
      active,
      inactive,
      admins,
      users,
      verified,
      unverified,
    ] = await Promise.all([
      this.repository.count(),
      this.repository.count({ where: { isActive: true } }),
      this.repository.count({ where: { isActive: false } }),
      this.repository.count({ where: { role: UserRole.ADMIN } }),
      this.repository.count({ where: { role: UserRole.USER } }),
      this.repository.count({ where: { isEmailVerified: true } }),
      this.repository.count({ where: { isEmailVerified: false } }),
    ]);

    return {
      total,
      active,
      inactive,
      admins,
      users,
      verified,
      unverified,
    };
  }

  /**
   * Clean up expired password reset tokens
   */
  async cleanupExpiredPasswordResetTokens(): Promise<number> {
    const result = await this.repository
      .createQueryBuilder()
      .update(User)
      .set({
        resetPasswordToken: null,
        resetPasswordExpires: null,
      })
      .where('resetPasswordToken IS NOT NULL')
      .andWhere('resetPasswordExpires < :now', { now: new Date() })
      .execute();

    return result.affected ?? 0;
  }

  /**
   * Update last login for user
   */
  async updateLastLogin(userId: string): Promise<void> {
    await this.repository.update(userId, {
      lastLoginAt: new Date(),
    });
  }

  /**
   * Check if email exists (case insensitive)
   */
  async emailExists(email: string, excludeUserId?: string): Promise<boolean> {
    const queryBuilder = this.repository
      .createQueryBuilder('user')
      .where('LOWER(user.email) = LOWER(:email)', { email });

    if (excludeUserId) {
      queryBuilder.andWhere('user.id != :excludeUserId', { excludeUserId });
    }

    const count = await queryBuilder.getCount();
    return count > 0;
  }
}
