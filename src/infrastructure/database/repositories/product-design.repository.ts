import { Injectable } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, ILike } from 'typeorm';

import { ProductDesign } from '@domain/product/product-design.entity';
import { BaseRepository } from './base.repository';
import { PaginationOptions, PaginationResult } from '@domain/common/repository.interface';

export interface ProductDesignSearchOptions {
  productId?: string;
  title?: string;
  isActive?: boolean;
  isOutOfStock?: boolean;
  search?: string;
}

@Injectable()
export class ProductDesignRepository extends BaseRepository<ProductDesign> {
  constructor(@InjectDataSource() dataSource: DataSource) {
    super(dataSource, ProductDesign);
  }

  /**
   * Find designs by product ID
   */
  async findByProductId(productId: string): Promise<ProductDesign[]> {
    return this.repository.find({
      where: { productId },
      order: { sortOrder: 'ASC', title: 'ASC' },
    });
  }

  /**
   * Find active designs by product ID
   */
  async findActiveByProductId(productId: string): Promise<ProductDesign[]> {
    return this.repository.find({
      where: { 
        productId,
        isActive: true,
      },
      order: { sortOrder: 'ASC', title: 'ASC' },
    });
  }

  /**
   * Search product designs with filters and pagination
   */
  async searchProductDesigns(
    searchOptions: ProductDesignSearchOptions,
    paginationOptions: PaginationOptions,
  ): Promise<PaginationResult<ProductDesign>> {
    const queryBuilder = this.repository.createQueryBuilder('design')
      .leftJoinAndSelect('design.product', 'product');

    // Apply filters
    if (searchOptions.productId) {
      queryBuilder.andWhere('design.productId = :productId', { 
        productId: searchOptions.productId 
      });
    }

    if (searchOptions.title) {
      queryBuilder.andWhere('design.title ILIKE :title', { 
        title: `%${searchOptions.title}%` 
      });
    }

    if (searchOptions.isActive !== undefined) {
      queryBuilder.andWhere('design.isActive = :isActive', { 
        isActive: searchOptions.isActive 
      });
    }

    if (searchOptions.isOutOfStock) {
      queryBuilder.andWhere('design.quantity <= 0');
    }

    if (searchOptions.search) {
      queryBuilder.andWhere(
        '(design.title ILIKE :search OR design.description ILIKE :search)',
        { search: `%${searchOptions.search}%` }
      );
    }

    // Apply pagination
    const { page, limit, orderBy = 'sortOrder', order = 'ASC' } = paginationOptions;
    const skip = (page - 1) * limit;

    queryBuilder
      .orderBy(`design.${orderBy}`, order)
      .addOrderBy('design.title', 'ASC')
      .skip(skip)
      .take(limit);

    const [data, total] = await queryBuilder.getManyAndCount();

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrevious: page > 1,
    };
  }

  /**
   * Find designs by title pattern
   */
  async findByTitlePattern(pattern: string): Promise<ProductDesign[]> {
    return this.repository.find({
      where: { title: ILike(`%${pattern}%`) },
      relations: ['product'],
      order: { title: 'ASC' },
    });
  }

  /**
   * Find out of stock designs
   */
  async findOutOfStockDesigns(): Promise<ProductDesign[]> {
    return this.repository.find({
      where: { 
        quantity: 0,
        isActive: true,
      },
      relations: ['product'],
      order: { title: 'ASC' },
    });
  }

  /**
   * Find low stock designs
   */
  async findLowStockDesigns(threshold: number = 5): Promise<ProductDesign[]> {
    return this.repository
      .createQueryBuilder('design')
      .leftJoinAndSelect('design.product', 'product')
      .where('design.quantity > 0')
      .andWhere('design.quantity < :threshold', { threshold })
      .andWhere('design.isActive = true')
      .orderBy('design.quantity', 'ASC')
      .getMany();
  }

  /**
   * Update design quantities in batch
   */
  async updateQuantities(updates: Array<{ id: string; quantity: number }>): Promise<void> {
    await this.dataSource.transaction(async (manager) => {
      const repository = manager.getRepository(ProductDesign);
      
      for (const update of updates) {
        await repository.update(update.id, { 
          quantity: Math.max(0, update.quantity) 
        });
      }
    });
  }

  /**
   * Reorder designs for a product
   */
  async reorderDesigns(productId: string, designIds: string[]): Promise<void> {
    await this.dataSource.transaction(async (manager) => {
      const repository = manager.getRepository(ProductDesign);
      
      for (let i = 0; i < designIds.length; i++) {
        await repository.update(
          { id: designIds[i], productId },
          { sortOrder: i + 1 }
        );
      }
    });
  }

  /**
   * Get next sort order for product
   */
  async getNextSortOrder(productId: string): Promise<number> {
    const result = await this.repository
      .createQueryBuilder('design')
      .select('MAX(design.sortOrder)', 'maxOrder')
      .where('design.productId = :productId', { productId })
      .getRawOne();

    return (result?.maxOrder || 0) + 1;
  }

  /**
   * Count designs by product
   */
  async countByProduct(productId: string): Promise<number> {
    return this.repository.count({
      where: { productId },
    });
  }

  /**
   * Count active designs by product
   */
  async countActiveByProduct(productId: string): Promise<number> {
    return this.repository.count({
      where: { 
        productId,
        isActive: true,
      },
    });
  }

  /**
   * Get design statistics
   */
  async getDesignStatistics(): Promise<{
    total: number;
    active: number;
    inactive: number;
    outOfStock: number;
    lowStock: number;
    withImages: number;
    withoutImages: number;
  }> {
    const [
      total,
      active,
      inactive,
      outOfStock,
      lowStock,
      withImages,
      withoutImages,
    ] = await Promise.all([
      this.repository.count(),
      this.repository.count({ where: { isActive: true } }),
      this.repository.count({ where: { isActive: false } }),
      this.repository.count({ where: { quantity: 0, isActive: true } }),
      this.repository
        .createQueryBuilder('design')
        .where('design.quantity > 0')
        .andWhere('design.quantity < 5')
        .andWhere('design.isActive = true')
        .getCount(),
      this.repository
        .createQueryBuilder('design')
        .where('design.imageUrl IS NOT NULL')
        .andWhere('design.imageUrl != \'\'')
        .getCount(),
      this.repository
        .createQueryBuilder('design')
        .where('design.imageUrl IS NULL OR design.imageUrl = \'\'')
        .getCount(),
    ]);

    return {
      total,
      active,
      inactive,
      outOfStock,
      lowStock,
      withImages,
      withoutImages,
    };
  }

  /**
   * Bulk activate/deactivate designs
   */
  async bulkUpdateActiveStatus(designIds: string[], isActive: boolean): Promise<number> {
    const result = await this.repository.update(designIds, { isActive });
    return result.affected ?? 0;
  }

  /**
   * Delete designs by product ID
   */
  async deleteByProductId(productId: string): Promise<number> {
    const result = await this.repository.delete({ productId });
    return result.affected ?? 0;
  }

  /**
   * Find designs with duplicate titles within the same product
   */
  async findDuplicateTitles(): Promise<Array<{ title: string; productId: string; count: number }>> {
    return this.repository
      .createQueryBuilder('design')
      .select(['design.title', 'design.productId', 'COUNT(*) as count'])
      .groupBy('design.title, design.productId')
      .having('COUNT(*) > 1')
      .getRawMany();
  }
}
