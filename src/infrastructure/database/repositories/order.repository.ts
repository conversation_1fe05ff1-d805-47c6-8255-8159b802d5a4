import { Injectable } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, Between, MoreThanOrEqual, LessThanOrEqual } from 'typeorm';

import { Order } from '@domain/order/order.entity';
import { OrderStatus } from '@domain/order/order-status.enum';
import { PlatformSource } from '@domain/product/platform-source.enum';
import { BaseRepository } from './base.repository';
import { PaginationOptions, PaginationResult } from '@domain/common/repository.interface';

export interface OrderSearchOptions {
  externalOrderId?: string;
  orderNumber?: string;
  status?: OrderStatus;
  source?: PlatformSource;
  customerEmail?: string;
  customerName?: string;
  dateFrom?: Date;
  dateTo?: Date;
  isProcessed?: boolean;
  isEngraveOrder?: boolean;
  isMixedOrder?: boolean;
  search?: string;
}

@Injectable()
export class OrderRepository extends BaseRepository<Order> {
  constructor(@InjectDataSource() dataSource: DataSource) {
    super(dataSource, Order);
  }

  /**
   * Find order by external order ID and source
   */
  async findByExternalId(externalOrderId: string, source: PlatformSource): Promise<Order | null> {
    return this.repository.findOne({
      where: { 
        externalOrderId,
        source,
      },
      relations: ['items'],
    });
  }

  /**
   * Find order by order number
   */
  async findByOrderNumber(orderNumber: string): Promise<Order | null> {
    return this.repository.findOne({
      where: { orderNumber },
      relations: ['items'],
    });
  }

  /**
   * Search orders with filters and pagination
   */
  async searchOrders(
    searchOptions: OrderSearchOptions,
    paginationOptions: PaginationOptions,
  ): Promise<PaginationResult<Order>> {
    const queryBuilder = this.repository.createQueryBuilder('order')
      .leftJoinAndSelect('order.items', 'items');

    // Apply filters
    if (searchOptions.externalOrderId) {
      queryBuilder.andWhere('order.externalOrderId ILIKE :externalOrderId', { 
        externalOrderId: `%${searchOptions.externalOrderId}%` 
      });
    }

    if (searchOptions.orderNumber) {
      queryBuilder.andWhere('order.orderNumber ILIKE :orderNumber', { 
        orderNumber: `%${searchOptions.orderNumber}%` 
      });
    }

    if (searchOptions.status) {
      queryBuilder.andWhere('order.status = :status', { status: searchOptions.status });
    }

    if (searchOptions.source) {
      queryBuilder.andWhere('order.source = :source', { source: searchOptions.source });
    }

    if (searchOptions.customerEmail) {
      queryBuilder.andWhere('order.customerEmail ILIKE :customerEmail', { 
        customerEmail: `%${searchOptions.customerEmail}%` 
      });
    }

    if (searchOptions.customerName) {
      queryBuilder.andWhere('order.customerName ILIKE :customerName', { 
        customerName: `%${searchOptions.customerName}%` 
      });
    }

    if (searchOptions.dateFrom && searchOptions.dateTo) {
      queryBuilder.andWhere('order.externalCreatedAt BETWEEN :dateFrom AND :dateTo', {
        dateFrom: searchOptions.dateFrom,
        dateTo: searchOptions.dateTo,
      });
    } else if (searchOptions.dateFrom) {
      queryBuilder.andWhere('order.externalCreatedAt >= :dateFrom', {
        dateFrom: searchOptions.dateFrom,
      });
    } else if (searchOptions.dateTo) {
      queryBuilder.andWhere('order.externalCreatedAt <= :dateTo', {
        dateTo: searchOptions.dateTo,
      });
    }

    if (searchOptions.isProcessed !== undefined) {
      queryBuilder.andWhere('order.isProcessed = :isProcessed', { 
        isProcessed: searchOptions.isProcessed 
      });
    }

    if (searchOptions.isEngraveOrder !== undefined) {
      queryBuilder.andWhere('order.isEngraveOrder = :isEngraveOrder', { 
        isEngraveOrder: searchOptions.isEngraveOrder 
      });
    }

    if (searchOptions.isMixedOrder !== undefined) {
      queryBuilder.andWhere('order.isMixedOrder = :isMixedOrder', { 
        isMixedOrder: searchOptions.isMixedOrder 
      });
    }

    if (searchOptions.search) {
      queryBuilder.andWhere(
        '(order.externalOrderId ILIKE :search OR order.orderNumber ILIKE :search OR order.customerName ILIKE :search OR order.customerEmail ILIKE :search)',
        { search: `%${searchOptions.search}%` }
      );
    }

    // Apply pagination
    const { page, limit, orderBy = 'externalCreatedAt', order = 'DESC' } = paginationOptions;
    const skip = (page - 1) * limit;

    queryBuilder
      .orderBy(`order.${orderBy}`, order)
      .skip(skip)
      .take(limit);

    const [data, total] = await queryBuilder.getManyAndCount();

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrevious: page > 1,
    };
  }

  /**
   * Find orders by status
   */
  async findByStatus(status: OrderStatus): Promise<Order[]> {
    return this.repository.find({
      where: { status },
      relations: ['items'],
      order: { externalCreatedAt: 'DESC' },
    });
  }

  /**
   * Find orders by platform source
   */
  async findByPlatformSource(source: PlatformSource): Promise<Order[]> {
    return this.repository.find({
      where: { source },
      relations: ['items'],
      order: { externalCreatedAt: 'DESC' },
    });
  }

  /**
   * Find orders by date range
   */
  async findByDateRange(startDate: Date, endDate: Date): Promise<Order[]> {
    return this.repository.find({
      where: {
        externalCreatedAt: Between(startDate, endDate),
      },
      relations: ['items'],
      order: { externalCreatedAt: 'DESC' },
    });
  }

  /**
   * Find recent orders (last N days)
   */
  async findRecentOrders(days: number = 7): Promise<Order[]> {
    const date = new Date();
    date.setDate(date.getDate() - days);

    return this.repository.find({
      where: {
        externalCreatedAt: MoreThanOrEqual(date),
      },
      relations: ['items'],
      order: { externalCreatedAt: 'DESC' },
    });
  }

  /**
   * Find unprocessed orders
   */
  async findUnprocessedOrders(): Promise<Order[]> {
    return this.repository.find({
      where: { isProcessed: false },
      relations: ['items'],
      order: { externalCreatedAt: 'ASC' },
    });
  }

  /**
   * Find engrave orders
   */
  async findEngraveOrders(): Promise<Order[]> {
    return this.repository.find({
      where: { isEngraveOrder: true },
      relations: ['items'],
      order: { externalCreatedAt: 'DESC' },
    });
  }

  /**
   * Find orders by customer email
   */
  async findByCustomerEmail(email: string): Promise<Order[]> {
    return this.repository.find({
      where: { customerEmail: email },
      relations: ['items'],
      order: { externalCreatedAt: 'DESC' },
    });
  }

  /**
   * Get order statistics
   */
  async getOrderStatistics(dateFrom?: Date, dateTo?: Date): Promise<{
    total: number;
    byStatus: Record<OrderStatus, number>;
    byPlatform: Record<PlatformSource, number>;
    totalRevenue: number;
    averageOrderValue: number;
    processed: number;
    unprocessed: number;
    engraveOrders: number;
    mixedOrders: number;
  }> {
    const queryBuilder = this.repository.createQueryBuilder('order');

    if (dateFrom && dateTo) {
      queryBuilder.where('order.externalCreatedAt BETWEEN :dateFrom AND :dateTo', {
        dateFrom,
        dateTo,
      });
    } else if (dateFrom) {
      queryBuilder.where('order.externalCreatedAt >= :dateFrom', { dateFrom });
    } else if (dateTo) {
      queryBuilder.where('order.externalCreatedAt <= :dateTo', { dateTo });
    }

    const [
      total,
      processed,
      unprocessed,
      engraveOrders,
      mixedOrders,
    ] = await Promise.all([
      queryBuilder.getCount(),
      queryBuilder.clone().andWhere('order.isProcessed = true').getCount(),
      queryBuilder.clone().andWhere('order.isProcessed = false').getCount(),
      queryBuilder.clone().andWhere('order.isEngraveOrder = true').getCount(),
      queryBuilder.clone().andWhere('order.isMixedOrder = true').getCount(),
    ]);

    // Get revenue statistics
    const revenueResult = await queryBuilder
      .select('SUM(order.totalPrice)', 'totalRevenue')
      .addSelect('AVG(order.totalPrice)', 'averageOrderValue')
      .getRawOne();

    const totalRevenue = parseFloat(revenueResult?.totalRevenue || '0');
    const averageOrderValue = parseFloat(revenueResult?.averageOrderValue || '0');

    // Get counts by status
    const byStatus: Record<OrderStatus, number> = {} as any;
    for (const status of Object.values(OrderStatus)) {
      byStatus[status] = await queryBuilder
        .clone()
        .andWhere('order.status = :status', { status })
        .getCount();
    }

    // Get counts by platform
    const byPlatform: Record<PlatformSource, number> = {} as any;
    for (const platform of Object.values(PlatformSource)) {
      byPlatform[platform] = await queryBuilder
        .clone()
        .andWhere('order.source = :source', { source: platform })
        .getCount();
    }

    return {
      total,
      byStatus,
      byPlatform,
      totalRevenue,
      averageOrderValue,
      processed,
      unprocessed,
      engraveOrders,
      mixedOrders,
    };
  }

  /**
   * Get daily order counts for a date range
   */
  async getDailyOrderCounts(startDate: Date, endDate: Date): Promise<Array<{
    date: string;
    count: number;
    revenue: number;
  }>> {
    return this.repository
      .createQueryBuilder('order')
      .select('DATE(order.externalCreatedAt)', 'date')
      .addSelect('COUNT(*)', 'count')
      .addSelect('SUM(order.totalPrice)', 'revenue')
      .where('order.externalCreatedAt BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .groupBy('DATE(order.externalCreatedAt)')
      .orderBy('date', 'ASC')
      .getRawMany();
  }

  /**
   * Mark orders as processed
   */
  async markAsProcessed(orderIds: string[]): Promise<number> {
    const result = await this.repository.update(orderIds, {
      isProcessed: true,
      processedAt: new Date(),
    });

    return result.affected ?? 0;
  }

  /**
   * Update order status in batch
   */
  async updateStatusBatch(orderIds: string[], status: OrderStatus): Promise<number> {
    const updateData: any = { status };

    // Add timestamp based on status
    switch (status) {
      case OrderStatus.SHIPPED:
        updateData.shippedAt = new Date();
        break;
      case OrderStatus.DELIVERED:
        updateData.deliveredAt = new Date();
        break;
      case OrderStatus.CANCELLED:
        updateData.cancelledAt = new Date();
        break;
    }

    const result = await this.repository.update(orderIds, updateData);
    return result.affected ?? 0;
  }

  /**
   * Check if external order exists
   */
  async externalOrderExists(
    externalOrderId: string, 
    source: PlatformSource, 
    excludeOrderId?: string
  ): Promise<boolean> {
    const queryBuilder = this.repository
      .createQueryBuilder('order')
      .where('order.externalOrderId = :externalOrderId', { externalOrderId })
      .andWhere('order.source = :source', { source });

    if (excludeOrderId) {
      queryBuilder.andWhere('order.id != :excludeOrderId', { excludeOrderId });
    }

    const count = await queryBuilder.getCount();
    return count > 0;
  }
}
