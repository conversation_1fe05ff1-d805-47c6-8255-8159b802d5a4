import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere } from 'typeorm';
import { OAuthToken, TokenProvider, TokenStatus } from '../../../domain/common/entities/oauth-token.entity';

@Injectable()
export class OAuthTokenRepository {
  constructor(
    @InjectRepository(OAuthToken)
    private readonly repository: Repository<OAuthToken>,
  ) {}

  /**
   * Find active token for a provider
   */
  async findActiveToken(provider: TokenProvider, shopId?: string): Promise<OAuthToken | null> {
    const where: FindOptionsWhere<OAuthToken> = {
      provider,
      status: TokenStatus.ACTIVE,
    };

    if (shopId) {
      where.shopId = shopId;
    }

    return this.repository.findOne({
      where,
      order: { lastUsedAt: 'DESC', createdAt: 'DESC' },
    });
  }

  /**
   * Find all tokens for a provider
   */
  async findByProvider(provider: TokenProvider): Promise<OAuthToken[]> {
    return this.repository.find({
      where: { provider },
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Find tokens that need refresh
   */
  async findTokensNeedingRefresh(): Promise<OAuthToken[]> {
    const fiveMinutesFromNow = new Date(Date.now() + 5 * 60 * 1000);
    
    return this.repository
      .createQueryBuilder('token')
      .where('token.status = :status', { status: TokenStatus.ACTIVE })
      .andWhere('token.expiresAt IS NOT NULL')
      .andWhere('token.expiresAt <= :expiresAt', { expiresAt: fiveMinutesFromNow })
      .andWhere('token.refreshFailureCount < :maxFailures', { maxFailures: 3 })
      .getMany();
  }

  /**
   * Find expired tokens
   */
  async findExpiredTokens(): Promise<OAuthToken[]> {
    const now = new Date();
    
    return this.repository
      .createQueryBuilder('token')
      .where('token.status = :status', { status: TokenStatus.ACTIVE })
      .andWhere('token.expiresAt IS NOT NULL')
      .andWhere('token.expiresAt <= :now', { now })
      .getMany();
  }

  /**
   * Save token
   */
  async save(token: OAuthToken): Promise<OAuthToken> {
    return this.repository.save(token);
  }

  /**
   * Create new token
   */
  async create(tokenData: Partial<OAuthToken>): Promise<OAuthToken> {
    const token = this.repository.create(tokenData);
    return this.repository.save(token);
  }

  /**
   * Update token
   */
  async update(id: string, updates: Partial<OAuthToken>): Promise<void> {
    await this.repository.update(id, updates);
  }

  /**
   * Delete token
   */
  async delete(id: string): Promise<void> {
    await this.repository.softDelete(id);
  }

  /**
   * Revoke all tokens for a provider
   */
  async revokeAllForProvider(provider: TokenProvider): Promise<void> {
    await this.repository.update(
      { provider },
      { status: TokenStatus.REVOKED }
    );
  }

  /**
   * Clean up old tokens (hard delete)
   */
  async cleanupOldTokens(olderThanDays: number = 90): Promise<number> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    const result = await this.repository
      .createQueryBuilder()
      .delete()
      .where('deletedAt IS NOT NULL')
      .andWhere('deletedAt < :cutoffDate', { cutoffDate })
      .execute();

    return result.affected || 0;
  }

  /**
   * Get token statistics
   */
  async getTokenStats(): Promise<{
    total: number;
    active: number;
    expired: number;
    revoked: number;
    error: number;
    byProvider: Record<string, number>;
  }> {
    const [total, active, expired, revoked, error] = await Promise.all([
      this.repository.count(),
      this.repository.count({ where: { status: TokenStatus.ACTIVE } }),
      this.repository.count({ where: { status: TokenStatus.EXPIRED } }),
      this.repository.count({ where: { status: TokenStatus.REVOKED } }),
      this.repository.count({ where: { status: TokenStatus.ERROR } }),
    ]);

    const byProviderResult = await this.repository
      .createQueryBuilder('token')
      .select('token.provider', 'provider')
      .addSelect('COUNT(*)', 'count')
      .groupBy('token.provider')
      .getRawMany();

    const byProvider = byProviderResult.reduce((acc, row) => {
      acc[row.provider] = parseInt(row.count);
      return acc;
    }, {});

    return {
      total,
      active,
      expired,
      revoked,
      error,
      byProvider,
    };
  }
}
