import { Injectable } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource, ILike } from 'typeorm';

import { OrderItem } from '@domain/order/order-item.entity';
import { BaseRepository } from './base.repository';
import { PaginationOptions, PaginationResult } from '@domain/common/repository.interface';

export interface OrderItemSearchOptions {
  orderId?: string;
  sku?: string;
  style?: string;
  color?: string;
  size?: number;
  design?: string;
  isCustom?: boolean;
  isEngrave?: boolean;
  search?: string;
}

@Injectable()
export class OrderItemRepository extends BaseRepository<OrderItem> {
  constructor(@InjectDataSource() dataSource: DataSource) {
    super(dataSource, OrderItem);
  }

  /**
   * Find items by order ID
   */
  async findByOrderId(orderId: string): Promise<OrderItem[]> {
    return this.repository.find({
      where: { orderId },
      relations: ['order'],
      order: { createdAt: 'ASC' },
    });
  }

  /**
   * Find items by SKU
   */
  async findBySku(sku: string): Promise<OrderItem[]> {
    return this.repository.find({
      where: { sku: sku.toUpperCase() },
      relations: ['order'],
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Search order items with filters and pagination
   */
  async searchOrderItems(
    searchOptions: OrderItemSearchOptions,
    paginationOptions: PaginationOptions,
  ): Promise<PaginationResult<OrderItem>> {
    const queryBuilder = this.repository.createQueryBuilder('item')
      .leftJoinAndSelect('item.order', 'order');

    // Apply filters
    if (searchOptions.orderId) {
      queryBuilder.andWhere('item.orderId = :orderId', { 
        orderId: searchOptions.orderId 
      });
    }

    if (searchOptions.sku) {
      queryBuilder.andWhere('item.sku ILIKE :sku', { 
        sku: `%${searchOptions.sku.toUpperCase()}%` 
      });
    }

    if (searchOptions.style) {
      queryBuilder.andWhere('item.style ILIKE :style', { 
        style: `%${searchOptions.style}%` 
      });
    }

    if (searchOptions.color) {
      queryBuilder.andWhere('item.color ILIKE :color', { 
        color: `%${searchOptions.color}%` 
      });
    }

    if (searchOptions.size) {
      queryBuilder.andWhere('item.size = :size', { size: searchOptions.size });
    }

    if (searchOptions.design) {
      queryBuilder.andWhere('item.design ILIKE :design', { 
        design: `%${searchOptions.design}%` 
      });
    }

    if (searchOptions.isCustom !== undefined) {
      queryBuilder.andWhere('item.isCustom = :isCustom', { 
        isCustom: searchOptions.isCustom 
      });
    }

    if (searchOptions.isEngrave !== undefined) {
      queryBuilder.andWhere('item.isEngrave = :isEngrave', { 
        isEngrave: searchOptions.isEngrave 
      });
    }

    if (searchOptions.search) {
      queryBuilder.andWhere(
        '(item.sku ILIKE :search OR item.title ILIKE :search OR item.style ILIKE :search OR item.color ILIKE :search)',
        { search: `%${searchOptions.search}%` }
      );
    }

    // Apply pagination
    const { page, limit, orderBy = 'createdAt', order = 'DESC' } = paginationOptions;
    const skip = (page - 1) * limit;

    queryBuilder
      .orderBy(`item.${orderBy}`, order)
      .skip(skip)
      .take(limit);

    const [data, total] = await queryBuilder.getManyAndCount();

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      page,
      limit,
      totalPages,
      hasNext: page < totalPages,
      hasPrevious: page > 1,
    };
  }

  /**
   * Find items by product criteria
   */
  async findByProductCriteria(criteria: {
    style?: string;
    color?: string;
    size?: number;
    design?: string;
  }): Promise<OrderItem[]> {
    const queryBuilder = this.repository.createQueryBuilder('item')
      .leftJoinAndSelect('item.order', 'order');

    if (criteria.style) {
      queryBuilder.andWhere('item.style ILIKE :style', { 
        style: `%${criteria.style}%` 
      });
    }

    if (criteria.color) {
      queryBuilder.andWhere('item.color ILIKE :color', { 
        color: `%${criteria.color}%` 
      });
    }

    if (criteria.size) {
      queryBuilder.andWhere('item.size = :size', { size: criteria.size });
    }

    if (criteria.design) {
      queryBuilder.andWhere('item.design ILIKE :design', { 
        design: `%${criteria.design}%` 
      });
    }

    return queryBuilder
      .orderBy('item.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Find custom items
   */
  async findCustomItems(): Promise<OrderItem[]> {
    return this.repository.find({
      where: { isCustom: true },
      relations: ['order'],
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Find engrave items
   */
  async findEngraveItems(): Promise<OrderItem[]> {
    return this.repository.find({
      where: { isEngrave: true },
      relations: ['order'],
      order: { createdAt: 'DESC' },
    });
  }

  /**
   * Get sales statistics by product
   */
  async getSalesStatisticsByProduct(
    dateFrom?: Date,
    dateTo?: Date,
  ): Promise<Array<{
    sku: string;
    style: string;
    color: string;
    size: number;
    design: string;
    totalQuantity: number;
    totalRevenue: number;
    orderCount: number;
  }>> {
    const queryBuilder = this.repository
      .createQueryBuilder('item')
      .leftJoin('item.order', 'order')
      .select([
        'item.sku as sku',
        'item.style as style',
        'item.color as color',
        'item.size as size',
        'item.design as design',
        'SUM(item.quantity) as totalQuantity',
        'SUM(item.totalPrice) as totalRevenue',
        'COUNT(DISTINCT item.orderId) as orderCount',
      ])
      .groupBy('item.sku, item.style, item.color, item.size, item.design');

    if (dateFrom && dateTo) {
      queryBuilder.where('order.externalCreatedAt BETWEEN :dateFrom AND :dateTo', {
        dateFrom,
        dateTo,
      });
    } else if (dateFrom) {
      queryBuilder.where('order.externalCreatedAt >= :dateFrom', { dateFrom });
    } else if (dateTo) {
      queryBuilder.where('order.externalCreatedAt <= :dateTo', { dateTo });
    }

    return queryBuilder
      .orderBy('totalQuantity', 'DESC')
      .getRawMany();
  }

  /**
   * Get top selling products
   */
  async getTopSellingProducts(
    limit: number = 10,
    dateFrom?: Date,
    dateTo?: Date,
  ): Promise<Array<{
    sku: string;
    title: string;
    totalQuantity: number;
    totalRevenue: number;
  }>> {
    const queryBuilder = this.repository
      .createQueryBuilder('item')
      .leftJoin('item.order', 'order')
      .select([
        'item.sku as sku',
        'item.title as title',
        'SUM(item.quantity) as totalQuantity',
        'SUM(item.totalPrice) as totalRevenue',
      ])
      .groupBy('item.sku, item.title');

    if (dateFrom && dateTo) {
      queryBuilder.where('order.externalCreatedAt BETWEEN :dateFrom AND :dateTo', {
        dateFrom,
        dateTo,
      });
    } else if (dateFrom) {
      queryBuilder.where('order.externalCreatedAt >= :dateFrom', { dateFrom });
    } else if (dateTo) {
      queryBuilder.where('order.externalCreatedAt <= :dateTo', { dateTo });
    }

    return queryBuilder
      .orderBy('totalQuantity', 'DESC')
      .limit(limit)
      .getRawMany();
  }

  /**
   * Get order item statistics
   */
  async getOrderItemStatistics(dateFrom?: Date, dateTo?: Date): Promise<{
    totalItems: number;
    totalQuantity: number;
    totalRevenue: number;
    averageItemPrice: number;
    customItems: number;
    engraveItems: number;
    uniqueSkus: number;
  }> {
    const queryBuilder = this.repository
      .createQueryBuilder('item')
      .leftJoin('item.order', 'order');

    if (dateFrom && dateTo) {
      queryBuilder.where('order.externalCreatedAt BETWEEN :dateFrom AND :dateTo', {
        dateFrom,
        dateTo,
      });
    } else if (dateFrom) {
      queryBuilder.where('order.externalCreatedAt >= :dateFrom', { dateFrom });
    } else if (dateTo) {
      queryBuilder.where('order.externalCreatedAt <= :dateTo', { dateTo });
    }

    const [
      totalItems,
      customItems,
      engraveItems,
    ] = await Promise.all([
      queryBuilder.getCount(),
      queryBuilder.clone().andWhere('item.isCustom = true').getCount(),
      queryBuilder.clone().andWhere('item.isEngrave = true').getCount(),
    ]);

    const aggregateResult = await queryBuilder
      .select([
        'SUM(item.quantity) as totalQuantity',
        'SUM(item.totalPrice) as totalRevenue',
        'AVG(item.unitPrice) as averageItemPrice',
        'COUNT(DISTINCT item.sku) as uniqueSkus',
      ])
      .getRawOne();

    return {
      totalItems,
      totalQuantity: parseInt(aggregateResult?.totalQuantity || '0'),
      totalRevenue: parseFloat(aggregateResult?.totalRevenue || '0'),
      averageItemPrice: parseFloat(aggregateResult?.averageItemPrice || '0'),
      customItems,
      engraveItems,
      uniqueSkus: parseInt(aggregateResult?.uniqueSkus || '0'),
    };
  }

  /**
   * Delete items by order ID
   */
  async deleteByOrderId(orderId: string): Promise<number> {
    const result = await this.repository.delete({ orderId });
    return result.affected ?? 0;
  }

  /**
   * Update quantities in batch
   */
  async updateQuantities(updates: Array<{ id: string; quantity: number }>): Promise<void> {
    await this.dataSource.transaction(async (manager) => {
      const repository = manager.getRepository(OrderItem);
      
      for (const update of updates) {
        const item = await repository.findOne({ where: { id: update.id } });
        if (item) {
          item.updateQuantity(update.quantity);
          await repository.save(item);
        }
      }
    });
  }

  /**
   * Find items with validation errors
   */
  async findItemsWithValidationErrors(): Promise<OrderItem[]> {
    return this.repository
      .createQueryBuilder('item')
      .where('item.quantity < 1')
      .orWhere('item.unitPrice < 0')
      .orWhere('ABS(item.quantity * item.unitPrice - item.totalPrice) > 0.01')
      .getMany();
  }
}
