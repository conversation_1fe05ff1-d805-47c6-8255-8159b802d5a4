import { DataSource } from 'typeorm';
import { config } from 'dotenv';

// Load environment variables
config();

export const AppDataSource = new DataSource({
  type: 'postgres',
  host: process.env.DATABASE_HOST || process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DATABASE_PORT || process.env.DB_PORT || '5432', 10),
  username: process.env.DATABASE_USERNAME || process.env.DB_USERNAME || 'postgres',
  password: process.env.DATABASE_PASSWORD || process.env.DB_PASSWORD || 'postgres',
  database: process.env.DATABASE_NAME || process.env.DB_DATABASE || 'knot_core',
  synchronize: false, // Always false for migrations
  logging: process.env.DATABASE_LOGGING === 'true' || process.env.DB_LOGGING === 'true',
  ssl: process.env.DATABASE_SSL === 'true' || process.env.DB_SSL === 'true' 
    ? { rejectUnauthorized: false } 
    : false,
  entities: [__dirname + '/../../domain/**/*.entity{.ts,.js}'],
  migrations: [__dirname + '/migrations/*{.ts,.js}'],
  migrationsTableName: 'migrations',
});

export default AppDataSource;
