import { Injectable, Logger } from '@nestjs/common';
import { BaseApiService } from '../../base-api.service';
import { ShopifyProduct, ShopifyVariant } from '../interfaces';
import { GetProductsParams } from '../types';

@Injectable()
export class ShopifyProductsService {
  private readonly logger = new Logger(ShopifyProductsService.name);

  constructor(private readonly apiService: BaseApiService) {}

  /**
   * Get products
   */
  async getProducts(params: GetProductsParams = {}): Promise<ShopifyProduct[]> {
    const response = await this.apiService.get('/products.json', { params });
    return response.data.products || [];
  }

  /**
   * Get product by ID
   */
  async getProduct(productId: number, fields?: string): Promise<ShopifyProduct> {
    const params = fields ? { fields } : {};
    const response = await this.apiService.get(`/products/${productId}.json`, { params });
    return response.data.product;
  }

  /**
   * Update product
   */
  async updateProduct(
    productId: number,
    product: Partial<ShopifyProduct>,
  ): Promise<ShopifyProduct> {
    const response = await this.apiService.put(`/products/${productId}.json`, { product });
    return response.data.product;
  }

  /**
   * Get product variants
   */
  async getProductVariants(productId: number): Promise<ShopifyVariant[]> {
    const response = await this.apiService.get(`/products/${productId}/variants.json`);
    return response.data.variants || [];
  }

  /**
   * Get variant by ID
   */
  async getVariant(variantId: number): Promise<ShopifyVariant> {
    const response = await this.apiService.get(`/variants/${variantId}.json`);
    return response.data.variant;
  }

  /**
   * Update variant
   */
  async updateVariant(
    variantId: number,
    variant: Partial<ShopifyVariant>,
  ): Promise<ShopifyVariant> {
    const response = await this.apiService.put(`/variants/${variantId}.json`, { variant });
    return response.data.variant;
  }
}
