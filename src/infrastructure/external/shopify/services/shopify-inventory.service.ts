import { Injectable, Logger } from '@nestjs/common';
import { BaseApiService } from '../../base-api.service';
import { ShopifyInventoryLevel } from '../interfaces';
import { GetInventoryLevelsParams, GetInventoryItemsParams } from '../types';

@Injectable()
export class ShopifyInventoryService {
  private readonly logger = new Logger(ShopifyInventoryService.name);

  constructor(private readonly apiService: BaseApiService) {}

  /**
   * Get inventory levels
   */
  async getInventoryLevels(params: GetInventoryLevelsParams): Promise<ShopifyInventoryLevel[]> {
    const response = await this.apiService.get('/inventory_levels.json', { params });
    return response.data.inventory_levels || [];
  }

  /**
   * Update inventory level
   */
  async updateInventoryLevel(
    inventoryItemId: number,
    locationId: number,
    available: number,
  ): Promise<ShopifyInventoryLevel> {
    const response = await this.apiService.post('/inventory_levels/set.json', {
      inventory_item_id: inventoryItemId,
      location_id: locationId,
      available,
    });
    return response.data.inventory_level;
  }

  /**
   * Adjust inventory level
   */
  async adjustInventoryLevel(
    inventoryItemId: number,
    locationId: number,
    quantity: number,
  ): Promise<ShopifyInventoryLevel> {
    const response = await this.apiService.post('/inventory_levels/adjust.json', {
      inventory_item_id: inventoryItemId,
      location_id: locationId,
      quantity,
    });
    return response.data.inventory_level;
  }

  /**
   * Get locations
   */
  async getLocations(): Promise<any[]> {
    const response = await this.apiService.get('/locations.json');
    return response.data.locations || [];
  }

  /**
   * Get inventory items
   */
  async getInventoryItems(params: GetInventoryItemsParams = {}): Promise<any[]> {
    const response = await this.apiService.get('/inventory_items.json', { params });
    return response.data.inventory_items || [];
  }

  /**
   * Get inventory item by ID
   */
  async getInventoryItem(inventoryItemId: number): Promise<any> {
    const response = await this.apiService.get(`/inventory_items/${inventoryItemId}.json`);
    return response.data.inventory_item;
  }

  /**
   * Update inventory item
   */
  async updateInventoryItem(inventoryItemId: number, inventoryItem: any): Promise<any> {
    const response = await this.apiService.put(`/inventory_items/${inventoryItemId}.json`, {
      inventory_item: inventoryItem,
    });
    return response.data.inventory_item;
  }
}
