import { PageInfo } from '../types';

/**
 * Parse Shopify Link header for pagination info
 */
export function parseLinkHeader(linkHeader?: string): PageInfo | undefined {
  if (!linkHeader) {
    return undefined;
  }

  const links = linkHeader.split(',').map(link => link.trim());
  const result: PageInfo = {
    hasNext: false,
    hasPrevious: false,
    nextPageInfo: undefined,
    previousPageInfo: undefined,
  };

  for (const link of links) {
    const match = link.match(/<([^>]+)>;\s*rel="([^"]+)"/);
    if (match) {
      const [, url, rel] = match;
      const urlObj = new URL(url);
      const pageInfo = urlObj.searchParams.get('page_info');

      if (rel === 'next' && pageInfo) {
        result.hasNext = true;
        result.nextPageInfo = pageInfo;
      } else if (rel === 'previous' && pageInfo) {
        result.hasPrevious = true;
        result.previousPageInfo = pageInfo;
      }
    }
  }

  return result;
}
