import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from '@domain/common/base.entity';
import { UserRole } from './user-role.enum';
import { Email } from './value-objects/email.vo';
import { Password } from './value-objects/password.vo';

@Entity('users')
@Index(['email'], { unique: true })
export class User extends BaseEntity {
  @Column({ type: 'varchar', length: 100, nullable: true })
  firstName?: string | null;

  @Column({ type: 'varchar', length: 100, nullable: true })
  lastName?: string | null;

  @Column({ type: 'varchar', length: 255, unique: true })
  email!: string;

  @Column({ type: 'varchar', length: 255 })
  password!: string;

  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.USER,
  })
  role!: UserRole;

  @Column({ type: 'varchar', length: 255, nullable: true })
  avatar?: string | null;

  @Column({ type: 'boolean', default: true })
  isActive!: boolean;

  @Column({ type: 'timestamp', nullable: true })
  lastLoginAt?: Date;

  @Column({ type: 'varchar', length: 255, nullable: true })
  resetPasswordToken?: string;

  @Column({ type: 'timestamp', nullable: true })
  resetPasswordExpires?: Date;

  @Column({ type: 'varchar', length: 255, nullable: true })
  emailVerificationToken?: string;

  @Column({ type: 'boolean', default: false })
  isEmailVerified!: boolean;

  @Column({ type: 'timestamp', nullable: true })
  emailVerifiedAt?: Date;

  /**
   * Get full name
   */
  get fullName(): string {
    if (this.firstName && this.lastName) {
      return `${this.firstName} ${this.lastName}`;
    }
    return this.firstName || this.lastName || this.email;
  }

  /**
   * Get initials
   */
  get initials(): string {
    if (this.firstName && this.lastName) {
      return `${this.firstName.charAt(0)}${this.lastName.charAt(0)}`.toUpperCase();
    }
    if (this.firstName) {
      return this.firstName.charAt(0).toUpperCase();
    }
    return this.email.charAt(0).toUpperCase();
  }

  /**
   * Check if user is admin
   */
  get isAdmin(): boolean {
    return this.role === UserRole.ADMIN;
  }

  /**
   * Check if user is regular user
   */
  get isUser(): boolean {
    return this.role === UserRole.USER;
  }

  /**
   * Check if user can access admin features
   */
  canAccessAdmin(): boolean {
    return this.isAdmin && this.isActive;
  }

  /**
   * Check if password reset is valid
   */
  isPasswordResetValid(): boolean {
    return !!(
      this.resetPasswordToken &&
      this.resetPasswordExpires &&
      this.resetPasswordExpires > new Date()
    );
  }

  /**
   * Check if email verification is pending
   */
  isEmailVerificationPending(): boolean {
    return !this.isEmailVerified && !!this.emailVerificationToken;
  }

  /**
   * Update last login timestamp
   */
  updateLastLogin(): void {
    this.lastLoginAt = new Date();
  }

  /**
   * Set password reset token
   */
  setPasswordResetToken(token: string, expiresInMinutes: number = 60): void {
    this.resetPasswordToken = token;
    this.resetPasswordExpires = new Date(Date.now() + expiresInMinutes * 60 * 1000);
  }

  /**
   * Clear password reset token
   */
  clearPasswordResetToken(): void {
    this.resetPasswordToken = undefined;
    this.resetPasswordExpires = undefined;
  }

  /**
   * Set email verification token
   */
  setEmailVerificationToken(token: string): void {
    this.emailVerificationToken = token;
    this.isEmailVerified = false;
    this.emailVerifiedAt = undefined;
  }

  /**
   * Verify email
   */
  verifyEmail(): void {
    this.isEmailVerified = true;
    this.emailVerifiedAt = new Date();
    this.emailVerificationToken = undefined;
  }

  /**
   * Activate user
   */
  activate(): void {
    this.isActive = true;
  }

  /**
   * Deactivate user
   */
  deactivate(): void {
    this.isActive = false;
  }

  /**
   * Change role
   */
  changeRole(role: UserRole): void {
    this.role = role;
  }

  /**
   * Update profile
   */
  updateProfile(data: { firstName?: string; lastName?: string; avatar?: string }): void {
    if (data.firstName !== undefined) {
      this.firstName = data.firstName;
    }
    if (data.lastName !== undefined) {
      this.lastName = data.lastName;
    }
    if (data.avatar !== undefined) {
      this.avatar = data.avatar;
    }
  }

  /**
   * Create email value object
   */
  getEmailVO(): Email {
    return new Email(this.email);
  }

  /**
   * Create password value object
   */
  getPasswordVO(): Password {
    return new Password(this.password, true); // Already hashed
  }

  /**
   * Convert to safe JSON (without sensitive data)
   */
  toSafeJSON(): Record<string, any> {
    const json = this.toJSON();
    delete json.password;
    delete json.resetPasswordToken;
    delete json.emailVerificationToken;
    return json;
  }
}
