import { ValueObject } from '@domain/common/value-object';

interface EmailProps {
  value: string;
}

export class Email extends ValueObject<EmailProps> {
  constructor(email: string) {
    if (!Email.isValid(email)) {
      throw new Error(`Invalid email format: ${email}`);
    }

    super({ value: email.toLowerCase().trim() });
  }

  /**
   * Get email value
   */
  get emailValue(): string {
    return this.props.value;
  }

  /**
   * Get local part (before @)
   */
  get localPart(): string {
    return this.props.value.split('@')[0];
  }

  /**
   * Get domain part (after @)
   */
  get domain(): string {
    return this.props.value.split('@')[1];
  }

  /**
   * Check if email is from a specific domain
   */
  isFromDomain(domain: string): boolean {
    return this.domain.toLowerCase() === domain.toLowerCase();
  }

  /**
   * Check if email is from any of the specified domains
   */
  isFromDomains(domains: string[]): boolean {
    return domains.some(domain => this.isFromDomain(domain));
  }

  /**
   * Get masked email for privacy (e.g., j***@example.com)
   */
  getMasked(): string {
    const [local, domain] = this.props.value.split('@');
    if (local.length <= 2) {
      return `${local[0]}***@${domain}`;
    }
    return `${local[0]}${'*'.repeat(local.length - 2)}${local[local.length - 1]}@${domain}`;
  }

  /**
   * Validate email format
   */
  static isValid(email: string): boolean {
    if (!email || typeof email !== 'string') {
      return false;
    }

    // Basic email regex that covers most cases
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    // Additional checks
    const trimmedEmail = email.trim();

    // Check basic format
    if (!emailRegex.test(trimmedEmail)) {
      return false;
    }

    // Check length constraints
    if (trimmedEmail.length > 254) {
      return false;
    }

    const [localPart, domain] = trimmedEmail.split('@');

    // Check local part length
    if (localPart.length > 64) {
      return false;
    }

    // Check domain length
    if (domain.length > 253) {
      return false;
    }

    // Check for consecutive dots
    if (trimmedEmail.includes('..')) {
      return false;
    }

    // Check for leading/trailing dots in local part
    if (localPart.startsWith('.') || localPart.endsWith('.')) {
      return false;
    }

    return true;
  }

  /**
   * Create email from string
   */
  static create(email: string): Email {
    return new Email(email);
  }

  /**
   * Convert to string
   */
  toString(): string {
    return this.props.value;
  }
}
