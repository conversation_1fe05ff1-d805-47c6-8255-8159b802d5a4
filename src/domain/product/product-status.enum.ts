export enum ProductStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  ARCHIVED = 'archived',
  DISCONTINUED = 'discontinued',
  DRAFT = 'draft',
}

export const PRODUCT_STATUS_DESCRIPTIONS = {
  [ProductStatus.ACTIVE]: 'Product is active and available for sale',
  [ProductStatus.INACTIVE]: 'Product is temporarily inactive',
  [ProductStatus.ARCHIVED]: 'Product is archived and no longer sold',
  [ProductStatus.DISCONTINUED]: 'Product is discontinued',
  [ProductStatus.DRAFT]: 'Product is in draft state',
} as const;

/**
 * Get status description
 */
export function getStatusDescription(status: ProductStatus): string {
  return PRODUCT_STATUS_DESCRIPTIONS[status];
}

/**
 * Check if status is active
 */
export function isActiveStatus(status: ProductStatus): boolean {
  return status === ProductStatus.ACTIVE;
}

/**
 * Check if status allows sales
 */
export function allowsSales(status: ProductStatus): boolean {
  return status === ProductStatus.ACTIVE;
}

/**
 * Get all sellable statuses
 */
export function getSellableStatuses(): ProductStatus[] {
  return [ProductStatus.ACTIVE];
}

/**
 * Get all non-sellable statuses
 */
export function getNonSellableStatuses(): ProductStatus[] {
  return [
    ProductStatus.INACTIVE,
    ProductStatus.ARCHIVED,
    ProductStatus.DISCONTINUED,
    ProductStatus.DRAFT,
  ];
}
