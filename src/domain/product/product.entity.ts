import { Entity, Column, Index, OneToMany } from 'typeorm';
import { BaseEntity } from '@domain/common/base.entity';
import { ProductDesign } from './product-design.entity';
import { SKU } from './value-objects/sku.vo';
import { Price } from './value-objects/price.vo';
import { Quantity } from './value-objects/quantity.vo';
import { ProductStatus } from './product-status.enum';
import { PlatformSource } from './platform-source.enum';

@Entity('products')
@Index(['sku'], { unique: true })
@Index(['externalProductId', 'source'])
@Index(['style', 'color', 'size'])
export class Product extends BaseEntity {
  @Column({ type: 'varchar', length: 100, unique: true })
  sku!: string;

  @Column({ type: 'varchar', length: 255, default: '-' })
  externalProductId!: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  fnsku?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  asin?: string;

  @Column({ type: 'varchar', length: 255, default: '-' })
  title!: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  style?: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  color?: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  design?: string;

  @Column({ type: 'int', nullable: true })
  size?: number;

  @Column({
    type: 'enum',
    enum: ProductStatus,
    default: ProductStatus.ACTIVE,
  })
  status!: ProductStatus;

  @Column({
    type: 'enum',
    enum: PlatformSource,
    default: PlatformSource.SHOPIFY,
  })
  source!: PlatformSource;

  @Column({ type: 'int', default: 0 })
  quantity!: number;

  @Column({ type: 'int', default: 0 })
  amazonQuantity!: number;

  @Column({ type: 'int', default: 0 })
  shopifyQuantity!: number;

  @Column({ type: 'int', default: 0 })
  etsyQuantity!: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  price?: number;

  @Column({ type: 'varchar', length: 10, default: 'USD' })
  currency!: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  inventoryItemId?: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  inventoryManagement?: string;

  @Column({ type: 'timestamp', nullable: true })
  externalCreatedAt?: Date;

  @Column({ type: 'timestamp', nullable: true })
  oosDate?: Date;

  @Column({ type: 'timestamp', nullable: true })
  amazonUpdatedTime?: Date;

  @Column({ type: 'timestamp', nullable: true })
  checkingTime?: Date;

  @Column({ type: 'boolean', default: false })
  checked!: boolean;

  @Column({ type: 'text', nullable: true })
  note?: string;

  @Column({ type: 'text', nullable: true })
  amazonSkus?: string;

  @Column({ type: 'text', nullable: true })
  etsyOfferings?: string;

  @OneToMany(() => ProductDesign, (design) => design.product, {
    cascade: true,
    eager: false,
  })
  designs?: ProductDesign[];

  /**
   * Get total quantity across all platforms
   */
  get totalQuantity(): number {
    return this.quantity + this.amazonQuantity + this.shopifyQuantity + this.etsyQuantity;
  }

  /**
   * Check if product is out of stock
   */
  get isOutOfStock(): boolean {
    return this.totalQuantity <= 0;
  }

  /**
   * Check if product is low stock (less than 10 units)
   */
  get isLowStock(): boolean {
    return this.totalQuantity > 0 && this.totalQuantity < 10;
  }

  /**
   * Check if product is active
   */
  get isActive(): boolean {
    return this.status === ProductStatus.ACTIVE;
  }

  /**
   * Check if product is archived
   */
  get isArchived(): boolean {
    return this.status === ProductStatus.ARCHIVED;
  }

  /**
   * Check if product is discontinued
   */
  get isDiscontinued(): boolean {
    return this.status === ProductStatus.DISCONTINUED;
  }

  /**
   * Check if product has complete information
   */
  get hasCompleteInfo(): boolean {
    return !!(this.style && this.color && this.size && this.size > 0);
  }

  /**
   * Get platform-specific quantity
   */
  getQuantityForPlatform(platform: PlatformSource): number {
    switch (platform) {
      case PlatformSource.AMAZON:
        return this.amazonQuantity;
      case PlatformSource.SHOPIFY:
        return this.shopifyQuantity;
      case PlatformSource.ETSY:
        return this.etsyQuantity;
      default:
        return this.quantity;
    }
  }

  /**
   * Update platform-specific quantity
   */
  updateQuantityForPlatform(platform: PlatformSource, quantity: number): void {
    const qty = Math.max(0, quantity); // Ensure non-negative

    switch (platform) {
      case PlatformSource.AMAZON:
        this.amazonQuantity = qty;
        break;
      case PlatformSource.SHOPIFY:
        this.shopifyQuantity = qty;
        break;
      case PlatformSource.ETSY:
        this.etsyQuantity = qty;
        break;
      default:
        this.quantity = qty;
    }

    this.updateOosDate();
  }

  /**
   * Update out of stock date based on current quantities
   */
  updateOosDate(): void {
    if (this.isOutOfStock && !this.oosDate) {
      this.oosDate = new Date();
    } else if (!this.isOutOfStock && this.oosDate) {
      this.oosDate = undefined;
    }
  }

  /**
   * Mark as checked
   */
  markAsChecked(): void {
    this.checked = true;
    this.checkingTime = new Date();
  }

  /**
   * Update Amazon information
   */
  updateAmazonInfo(data: {
    asin?: string;
    fnsku?: string;
    quantity?: number;
    skus?: string;
  }): void {
    if (data.asin) this.asin = data.asin;
    if (data.fnsku) this.fnsku = data.fnsku;
    if (data.quantity !== undefined) this.updateQuantityForPlatform(PlatformSource.AMAZON, data.quantity);
    if (data.skus) this.amazonSkus = data.skus;
    
    this.amazonUpdatedTime = new Date();
  }

  /**
   * Update Etsy information
   */
  updateEtsyInfo(data: {
    quantity?: number;
    offerings?: string;
  }): void {
    if (data.quantity !== undefined) this.updateQuantityForPlatform(PlatformSource.ETSY, data.quantity);
    if (data.offerings) this.etsyOfferings = data.offerings;
  }

  /**
   * Update Shopify information
   */
  updateShopifyInfo(data: {
    inventoryItemId?: string;
    inventoryManagement?: string;
    quantity?: number;
  }): void {
    if (data.inventoryItemId) this.inventoryItemId = data.inventoryItemId;
    if (data.inventoryManagement) this.inventoryManagement = data.inventoryManagement;
    if (data.quantity !== undefined) this.updateQuantityForPlatform(PlatformSource.SHOPIFY, data.quantity);
  }

  /**
   * Change status
   */
  changeStatus(status: ProductStatus): void {
    this.status = status;
  }

  /**
   * Archive product
   */
  archive(): void {
    this.changeStatus(ProductStatus.ARCHIVED);
  }

  /**
   * Activate product
   */
  activate(): void {
    this.changeStatus(ProductStatus.ACTIVE);
  }

  /**
   * Discontinue product
   */
  discontinue(): void {
    this.changeStatus(ProductStatus.DISCONTINUED);
  }

  /**
   * Update basic information
   */
  updateBasicInfo(data: {
    title?: string;
    style?: string;
    color?: string;
    design?: string;
    size?: number;
    price?: number;
    currency?: string;
    note?: string;
  }): void {
    if (data.title) this.title = data.title;
    if (data.style) this.style = data.style;
    if (data.color) this.color = data.color;
    if (data.design) this.design = data.design;
    if (data.size) this.size = data.size;
    if (data.price !== undefined) this.price = data.price;
    if (data.currency) this.currency = data.currency;
    if (data.note !== undefined) this.note = data.note;
  }

  /**
   * Get SKU value object
   */
  getSkuVO(): SKU {
    return new SKU(this.sku);
  }

  /**
   * Get price value object
   */
  getPriceVO(): Price | null {
    if (this.price === null || this.price === undefined) {
      return null;
    }
    return new Price(this.price, this.currency);
  }

  /**
   * Get quantity value object
   */
  getQuantityVO(): Quantity {
    return new Quantity(this.totalQuantity);
  }

  /**
   * Check if product matches criteria
   */
  matches(criteria: {
    style?: string;
    color?: string;
    size?: number;
    source?: PlatformSource;
    status?: ProductStatus;
  }): boolean {
    if (criteria.style && this.style !== criteria.style) return false;
    if (criteria.color && this.color !== criteria.color) return false;
    if (criteria.size && this.size !== criteria.size) return false;
    if (criteria.source && this.source !== criteria.source) return false;
    if (criteria.status && this.status !== criteria.status) return false;
    
    return true;
  }
}
