export enum PlatformSource {
  AMAZON = 'amazon',
  ETSY = 'etsy',
  SHOPIFY = 'shopify',
  ORDERS = 'orders',
  MANUAL = 'manual',
}

export const PLATFORM_SOURCE_DESCRIPTIONS = {
  [PlatformSource.AMAZON]: 'Amazon Marketplace',
  [PlatformSource.ETSY]: 'Etsy Marketplace',
  [PlatformSource.SHOPIFY]: 'Shopify Store',
  [PlatformSource.ORDERS]: 'Order System',
  [PlatformSource.MANUAL]: 'Manual Entry',
} as const;

export const PLATFORM_SOURCE_URLS = {
  [PlatformSource.AMAZON]: 'https://sellercentral.amazon.com',
  [PlatformSource.ETSY]: 'https://www.etsy.com/your/shops',
  [PlatformSource.SHOPIFY]: 'https://admin.shopify.com',
  [PlatformSource.ORDERS]: null,
  [PlatformSource.MANUAL]: null,
} as const;

/**
 * Get platform description
 */
export function getPlatformDescription(platform: PlatformSource): string {
  return PLATFORM_SOURCE_DESCRIPTIONS[platform];
}

/**
 * Get platform URL
 */
export function getPlatformUrl(platform: PlatformSource): string | null {
  return PLATFORM_SOURCE_URLS[platform];
}

/**
 * Check if platform is external marketplace
 */
export function isExternalMarketplace(platform: PlatformSource): boolean {
  return [
    PlatformSource.AMAZON,
    PlatformSource.ETSY,
    PlatformSource.SHOPIFY,
  ].includes(platform);
}

/**
 * Check if platform supports inventory sync
 */
export function supportsInventorySync(platform: PlatformSource): boolean {
  return [
    PlatformSource.AMAZON,
    PlatformSource.ETSY,
    PlatformSource.SHOPIFY,
  ].includes(platform);
}

/**
 * Get all external platforms
 */
export function getExternalPlatforms(): PlatformSource[] {
  return [
    PlatformSource.AMAZON,
    PlatformSource.ETSY,
    PlatformSource.SHOPIFY,
  ];
}

/**
 * Get all internal sources
 */
export function getInternalSources(): PlatformSource[] {
  return [
    PlatformSource.ORDERS,
    PlatformSource.MANUAL,
  ];
}
