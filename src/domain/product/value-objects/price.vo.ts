import { ValueObject } from '@domain/common/value-object';

interface PriceProps {
  amount: number;
  currency: string;
}

export class Price extends ValueObject<PriceProps> {
  private static readonly VALID_CURRENCIES = [
    'USD', 'EUR', 'GBP', 'JPY', 'AUD', 'CAD', 'CHF', 'CNY', 'SEK', 'NZD',
    'MXN', 'SGD', 'HKD', 'NOK', 'TRY', 'RUB', 'INR', 'BRL', 'ZAR', 'KRW',
  ];

  constructor(amount: number, currency: string = 'USD') {
    if (!Price.isValidAmount(amount)) {
      throw new Error(`Invalid price amount: ${amount}`);
    }

    if (!Price.isValidCurrency(currency)) {
      throw new Error(`Invalid currency: ${currency}`);
    }

    super({
      amount: Math.round(amount * 100) / 100, // Round to 2 decimal places
      currency: currency.toUpperCase(),
    });
  }

  /**
   * Get price amount
   */
  get amount(): number {
    return this.props.amount;
  }

  /**
   * Get currency code
   */
  get currency(): string {
    return this.props.currency;
  }

  /**
   * Get amount in cents (for payment processing)
   */
  get amountInCents(): number {
    return Math.round(this.props.amount * 100);
  }

  /**
   * Check if price is zero
   */
  get isZero(): boolean {
    return this.props.amount === 0;
  }

  /**
   * Check if price is positive
   */
  get isPositive(): boolean {
    return this.props.amount > 0;
  }

  /**
   * Check if price is negative
   */
  get isNegative(): boolean {
    return this.props.amount < 0;
  }

  /**
   * Add another price (must be same currency)
   */
  add(other: Price): Price {
    if (this.props.currency !== other.props.currency) {
      throw new Error(`Cannot add prices with different currencies: ${this.props.currency} and ${other.props.currency}`);
    }

    return new Price(this.props.amount + other.props.amount, this.props.currency);
  }

  /**
   * Subtract another price (must be same currency)
   */
  subtract(other: Price): Price {
    if (this.props.currency !== other.props.currency) {
      throw new Error(`Cannot subtract prices with different currencies: ${this.props.currency} and ${other.props.currency}`);
    }

    return new Price(this.props.amount - other.props.amount, this.props.currency);
  }

  /**
   * Multiply by a factor
   */
  multiply(factor: number): Price {
    if (!Price.isValidAmount(factor)) {
      throw new Error(`Invalid multiplication factor: ${factor}`);
    }

    return new Price(this.props.amount * factor, this.props.currency);
  }

  /**
   * Divide by a factor
   */
  divide(factor: number): Price {
    if (!Price.isValidAmount(factor) || factor === 0) {
      throw new Error(`Invalid division factor: ${factor}`);
    }

    return new Price(this.props.amount / factor, this.props.currency);
  }

  /**
   * Apply percentage discount
   */
  applyDiscount(percentage: number): Price {
    if (percentage < 0 || percentage > 100) {
      throw new Error(`Invalid discount percentage: ${percentage}`);
    }

    const discountFactor = (100 - percentage) / 100;
    return this.multiply(discountFactor);
  }

  /**
   * Apply tax
   */
  applyTax(taxRate: number): Price {
    if (taxRate < 0) {
      throw new Error(`Invalid tax rate: ${taxRate}`);
    }

    const taxFactor = 1 + (taxRate / 100);
    return this.multiply(taxFactor);
  }

  /**
   * Compare with another price
   */
  compare(other: Price): number {
    if (this.props.currency !== other.props.currency) {
      throw new Error(`Cannot compare prices with different currencies: ${this.props.currency} and ${other.props.currency}`);
    }

    if (this.props.amount < other.props.amount) return -1;
    if (this.props.amount > other.props.amount) return 1;
    return 0;
  }

  /**
   * Check if greater than another price
   */
  isGreaterThan(other: Price): boolean {
    return this.compare(other) > 0;
  }

  /**
   * Check if less than another price
   */
  isLessThan(other: Price): boolean {
    return this.compare(other) < 0;
  }

  /**
   * Check if equal to another price
   */
  isEqualTo(other: Price): boolean {
    return this.compare(other) === 0;
  }

  /**
   * Format price for display
   */
  format(locale: string = 'en-US'): string {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: this.props.currency,
    }).format(this.props.amount);
  }

  /**
   * Convert to different currency (requires exchange rate)
   */
  convertTo(targetCurrency: string, exchangeRate: number): Price {
    if (!Price.isValidCurrency(targetCurrency)) {
      throw new Error(`Invalid target currency: ${targetCurrency}`);
    }

    if (!Price.isValidAmount(exchangeRate) || exchangeRate <= 0) {
      throw new Error(`Invalid exchange rate: ${exchangeRate}`);
    }

    const convertedAmount = this.props.amount * exchangeRate;
    return new Price(convertedAmount, targetCurrency);
  }

  /**
   * Validate price amount
   */
  static isValidAmount(amount: number): boolean {
    return typeof amount === 'number' && 
           !isNaN(amount) && 
           isFinite(amount) && 
           amount >= 0;
  }

  /**
   * Validate currency code
   */
  static isValidCurrency(currency: string): boolean {
    return typeof currency === 'string' && 
           Price.VALID_CURRENCIES.includes(currency.toUpperCase());
  }

  /**
   * Create zero price
   */
  static zero(currency: string = 'USD'): Price {
    return new Price(0, currency);
  }

  /**
   * Create price from cents
   */
  static fromCents(cents: number, currency: string = 'USD'): Price {
    return new Price(cents / 100, currency);
  }

  /**
   * Create price from string
   */
  static fromString(priceString: string, currency: string = 'USD'): Price {
    const amount = parseFloat(priceString);
    if (isNaN(amount)) {
      throw new Error(`Invalid price string: ${priceString}`);
    }
    return new Price(amount, currency);
  }

  /**
   * Get supported currencies
   */
  static getSupportedCurrencies(): string[] {
    return [...Price.VALID_CURRENCIES];
  }

  /**
   * Convert to string
   */
  toString(): string {
    return `${this.props.amount} ${this.props.currency}`;
  }
}
