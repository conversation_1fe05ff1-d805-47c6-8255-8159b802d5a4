/**
 * Base class for Value Objects
 * Value objects are immutable and equality is based on their properties
 */
export abstract class ValueObject<T> {
  protected readonly props: T;

  constructor(props: T) {
    this.props = Object.freeze(props);
  }

  /**
   * Check equality with another value object
   */
  equals(other: ValueObject<T>): boolean {
    if (other === null || other === undefined) {
      return false;
    }

    if (other.constructor !== this.constructor) {
      return false;
    }

    return this.deepEquals(this.props, other.props);
  }

  /**
   * Get the value of the value object
   */
  get value(): T {
    return this.props;
  }

  /**
   * Convert to JSON representation
   */
  toJSON(): T {
    return this.props;
  }

  /**
   * Convert to string representation
   */
  toString(): string {
    return JSON.stringify(this.props);
  }

  /**
   * Deep equality check for objects
   */
  private deepEquals(a: any, b: any): boolean {
    if (a === b) {
      return true;
    }

    if (a instanceof Date && b instanceof Date) {
      return a.getTime() === b.getTime();
    }

    if (!a || !b || (typeof a !== 'object' && typeof b !== 'object')) {
      return a === b;
    }

    if (a === null || a === undefined || b === null || b === undefined) {
      return false;
    }

    if (a.prototype !== b.prototype) {
      return false;
    }

    const keys = Object.keys(a);
    if (keys.length !== Object.keys(b).length) {
      return false;
    }

    return keys.every(k => this.deepEquals(a[k], b[k]));
  }
}
