import { BaseEntity } from './base.entity';

export interface FindOptions {
  where?: Record<string, any>;
  relations?: string[];
  order?: Record<string, 'ASC' | 'DESC'>;
  skip?: number;
  take?: number;
}

export interface PaginationOptions {
  page: number;
  limit: number;
  orderBy?: string;
  order?: 'ASC' | 'DESC';
}

export interface PaginationResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

/**
 * Generic repository interface for domain entities
 */
export interface IRepository<T extends BaseEntity> {
  /**
   * Find entity by ID
   */
  findById(id: string): Promise<T | null>;

  /**
   * Find entities by criteria
   */
  find(options?: FindOptions): Promise<T[]>;

  /**
   * Find one entity by criteria
   */
  findOne(options: FindOptions): Promise<T | null>;

  /**
   * Find entities with pagination
   */
  findWithPagination(options: PaginationOptions & FindOptions): Promise<PaginationResult<T>>;

  /**
   * Count entities by criteria
   */
  count(where?: Record<string, any>): Promise<number>;

  /**
   * Check if entity exists
   */
  exists(where: Record<string, any>): Promise<boolean>;

  /**
   * Save entity (create or update)
   */
  save(entity: T): Promise<T>;

  /**
   * Save multiple entities
   */
  saveMany(entities: T[]): Promise<T[]>;

  /**
   * Create new entity
   */
  create(data: Partial<T>): T;

  /**
   * Update entity by ID
   */
  update(id: string, data: Partial<T>): Promise<T | null>;

  /**
   * Delete entity by ID (hard delete)
   */
  delete(id: string): Promise<boolean>;

  /**
   * Soft delete entity by ID
   */
  softDelete(id: string): Promise<boolean>;

  /**
   * Restore soft deleted entity
   */
  restore(id: string): Promise<boolean>;

  /**
   * Delete entities by criteria
   */
  deleteMany(where: Record<string, any>): Promise<number>;

  /**
   * Execute raw query
   */
  query(sql: string, parameters?: any[]): Promise<any>;

  /**
   * Start transaction
   */
  transaction<R>(fn: (repository: IRepository<T>) => Promise<R>): Promise<R>;
}
