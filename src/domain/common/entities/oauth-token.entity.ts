import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  VersionColumn,
  Index,
} from 'typeorm';

export enum TokenProvider {
  ETSY = 'etsy',
  SHOPIFY = 'shopify',
  AMAZON = 'amazon',
}

export enum TokenStatus {
  ACTIVE = 'active',
  EXPIRED = 'expired',
  REVOKED = 'revoked',
  ERROR = 'error',
}

@Entity('oauth_tokens')
@Index(['provider', 'status'])
@Index(['provider', 'expiresAt'])
export class OAuthToken {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt?: Date;

  @VersionColumn()
  version: number;

  @Column({
    type: 'enum',
    enum: TokenProvider,
  })
  provider: TokenProvider;

  @Column({
    type: 'enum',
    enum: TokenStatus,
    default: TokenStatus.ACTIVE,
  })
  status: TokenStatus;

  @Column({
    type: 'text',
    comment: 'Encrypted access token',
  })
  encryptedAccessToken: string;

  @Column({
    type: 'text',
    comment: 'Encrypted refresh token',
  })
  encryptedRefreshToken: string;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: 'When the access token expires',
  })
  expiresAt?: Date;

  @Column({
    type: 'varchar',
    length: 500,
    nullable: true,
    comment: 'OAuth scopes granted',
  })
  scopes?: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Associated shop/account ID',
  })
  shopId?: string;

  @Column({
    type: 'varchar',
    length: 255,
    nullable: true,
    comment: 'Associated shop/account name',
  })
  shopName?: string;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: 'Last time token was successfully used',
  })
  lastUsedAt?: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: 'Last time token was refreshed',
  })
  lastRefreshedAt?: Date;

  @Column({
    type: 'int',
    default: 0,
    comment: 'Number of times token refresh failed',
  })
  refreshFailureCount: number;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Last error message if token refresh failed',
  })
  lastError?: string;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Additional provider-specific metadata',
  })
  metadata?: Record<string, any>;

  // Helper methods
  isExpired(): boolean {
    if (!this.expiresAt) return false;
    return new Date() >= this.expiresAt;
  }

  isActive(): boolean {
    return this.status === TokenStatus.ACTIVE && !this.isExpired();
  }

  needsRefresh(): boolean {
    if (!this.expiresAt) {
      // If no expiration date, refresh if token is older than 1 hour
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
      return this.lastUsedAt ? this.lastUsedAt <= oneHourAgo : true;
    }
    // Refresh if token expires within 5 minutes
    const fiveMinutesFromNow = new Date(Date.now() + 5 * 60 * 1000);
    return this.expiresAt <= fiveMinutesFromNow;
  }

  markAsUsed(): void {
    this.lastUsedAt = new Date();
  }

  markAsRefreshed(): void {
    this.lastRefreshedAt = new Date();
    this.refreshFailureCount = 0;
    this.lastError = null;
    this.status = TokenStatus.ACTIVE;
  }

  markRefreshFailed(error: string): void {
    this.refreshFailureCount += 1;
    this.lastError = error;
    if (this.refreshFailureCount >= 3) {
      this.status = TokenStatus.ERROR;
    }
  }

  markAsExpired(): void {
    this.status = TokenStatus.EXPIRED;
  }

  markAsRevoked(): void {
    this.status = TokenStatus.REVOKED;
  }
}
