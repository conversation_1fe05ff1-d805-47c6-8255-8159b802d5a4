export enum OrderStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  PROCESSING = 'processing',
  SHIPPED = 'shipped',
  DELIVERED = 'delivered',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
  RETURNED = 'returned',
}

export const ORDER_STATUS_DESCRIPTIONS = {
  [OrderStatus.PENDING]: 'Order is pending confirmation',
  [OrderStatus.CONFIRMED]: 'Order has been confirmed and is being prepared',
  [OrderStatus.PROCESSING]: 'Order is being processed',
  [OrderStatus.SHIPPED]: 'Order has been shipped',
  [OrderStatus.DELIVERED]: 'Order has been delivered',
  [OrderStatus.CANCELLED]: 'Order has been cancelled',
  [OrderStatus.REFUNDED]: 'Order has been refunded',
  [OrderStatus.RETURNED]: 'Order has been returned',
} as const;

export const ORDER_STATUS_COLORS = {
  [OrderStatus.PENDING]: '#fbbf24', // yellow
  [OrderStatus.CONFIRMED]: '#3b82f6', // blue
  [OrderStatus.PROCESSING]: '#8b5cf6', // purple
  [OrderStatus.SHIPPED]: '#06b6d4', // cyan
  [OrderStatus.DELIVERED]: '#10b981', // green
  [OrderStatus.CANCELLED]: '#ef4444', // red
  [OrderStatus.REFUNDED]: '#f97316', // orange
  [OrderStatus.RETURNED]: '#6b7280', // gray
} as const;

/**
 * Get status description
 */
export function getStatusDescription(status: OrderStatus): string {
  return ORDER_STATUS_DESCRIPTIONS[status];
}

/**
 * Get status color
 */
export function getStatusColor(status: OrderStatus): string {
  return ORDER_STATUS_COLORS[status];
}

/**
 * Check if status is active (not cancelled, refunded, or returned)
 */
export function isActiveStatus(status: OrderStatus): boolean {
  return ![OrderStatus.CANCELLED, OrderStatus.REFUNDED, OrderStatus.RETURNED].includes(status);
}

/**
 * Check if status is final (cannot be changed)
 */
export function isFinalStatus(status: OrderStatus): boolean {
  return [
    OrderStatus.DELIVERED,
    OrderStatus.CANCELLED,
    OrderStatus.REFUNDED,
    OrderStatus.RETURNED,
  ].includes(status);
}

/**
 * Check if status allows cancellation
 */
export function allowsCancellation(status: OrderStatus): boolean {
  return [OrderStatus.PENDING, OrderStatus.CONFIRMED].includes(status);
}

/**
 * Check if status allows refund
 */
export function allowsRefund(status: OrderStatus): boolean {
  return [
    OrderStatus.CONFIRMED,
    OrderStatus.PROCESSING,
    OrderStatus.SHIPPED,
    OrderStatus.DELIVERED,
  ].includes(status);
}

/**
 * Get next possible statuses
 */
export function getNextPossibleStatuses(currentStatus: OrderStatus): OrderStatus[] {
  switch (currentStatus) {
    case OrderStatus.PENDING:
      return [OrderStatus.CONFIRMED, OrderStatus.CANCELLED];
    case OrderStatus.CONFIRMED:
      return [OrderStatus.PROCESSING, OrderStatus.CANCELLED];
    case OrderStatus.PROCESSING:
      return [OrderStatus.SHIPPED, OrderStatus.CANCELLED];
    case OrderStatus.SHIPPED:
      return [OrderStatus.DELIVERED, OrderStatus.RETURNED];
    case OrderStatus.DELIVERED:
      return [OrderStatus.REFUNDED, OrderStatus.RETURNED];
    default:
      return [];
  }
}

/**
 * Check if status transition is valid
 */
export function isValidTransition(from: OrderStatus, to: OrderStatus): boolean {
  const possibleStatuses = getNextPossibleStatuses(from);
  return possibleStatuses.includes(to);
}

/**
 * Get all active statuses
 */
export function getActiveStatuses(): OrderStatus[] {
  return [
    OrderStatus.PENDING,
    OrderStatus.CONFIRMED,
    OrderStatus.PROCESSING,
    OrderStatus.SHIPPED,
    OrderStatus.DELIVERED,
  ];
}

/**
 * Get all inactive statuses
 */
export function getInactiveStatuses(): OrderStatus[] {
  return [
    OrderStatus.CANCELLED,
    OrderStatus.REFUNDED,
    OrderStatus.RETURNED,
  ];
}
