import { Entity, Column, Index, OneToMany } from 'typeorm';
import { BaseEntity } from '@domain/common/base.entity';
import { OrderItem } from './order-item.entity';
import { OrderStatus } from './order-status.enum';
import { PlatformSource } from '@domain/product/platform-source.enum';
import { Price } from '@domain/product/value-objects/price.vo';

@Entity('orders')
@Index(['externalOrderId', 'source'], { unique: true })
@Index(['orderNumber'])
@Index(['customerEmail'])
@Index(['status'])
@Index(['externalCreatedAt'])
export class Order extends BaseEntity {
  @Column({ type: 'varchar', length: 255 })
  externalOrderId!: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  orderNumber?: string;

  @Column({
    type: 'enum',
    enum: OrderStatus,
    default: OrderStatus.PENDING,
  })
  status!: OrderStatus;

  @Column({
    type: 'enum',
    enum: PlatformSource,
  })
  source!: PlatformSource;

  @Column({ type: 'timestamp' })
  externalCreatedAt!: Date;

  @Column({ type: 'varchar', length: 255, nullable: true })
  customerName?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  customerEmail?: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  customerPhone?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  shippingAddressLine1?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  shippingAddressLine2?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  shippingCity?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  shippingState?: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  shippingPostalCode?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  shippingCountry?: string;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  subtotalPrice!: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  shippingPrice!: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  taxAmount!: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  discountAmount!: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  totalPrice!: number;

  @Column({ type: 'varchar', length: 10, default: 'USD' })
  currency!: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  fulfillmentChannel?: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  salesChannel?: string;

  @Column({ type: 'text', nullable: true })
  customerNote?: string;

  @Column({ type: 'text', nullable: true })
  internalNote?: string;

  @Column({ type: 'boolean', default: false })
  isEngraveOrder!: boolean;

  @Column({ type: 'boolean', default: false })
  isMixedOrder!: boolean;

  @Column({ type: 'boolean', default: false })
  isProcessed!: boolean;

  @Column({ type: 'timestamp', nullable: true })
  processedAt?: Date;

  @Column({ type: 'timestamp', nullable: true })
  shippedAt?: Date;

  @Column({ type: 'timestamp', nullable: true })
  deliveredAt?: Date;

  @Column({ type: 'timestamp', nullable: true })
  cancelledAt?: Date;

  @Column({ type: 'text', nullable: true })
  cancellationReason?: string;

  @Column({ type: 'json', nullable: true })
  metadata?: Record<string, any>;

  @Column({
    type: 'json',
    nullable: true,
    comment:
      'Raw data from the platform API (Shopify, Etsy, Amazon) for debugging and future data extraction',
  })
  rawData?: Record<string, any>;

  @OneToMany(() => OrderItem, item => item.order, {
    cascade: true,
    eager: false,
  })
  items?: OrderItem[];

  /**
   * Check if order is pending
   */
  get isPending(): boolean {
    return this.status === OrderStatus.PENDING;
  }

  /**
   * Check if order is confirmed
   */
  get isConfirmed(): boolean {
    return this.status === OrderStatus.CONFIRMED;
  }

  /**
   * Check if order is shipped
   */
  get isShipped(): boolean {
    return this.status === OrderStatus.SHIPPED;
  }

  /**
   * Check if order is delivered
   */
  get isDelivered(): boolean {
    return this.status === OrderStatus.DELIVERED;
  }

  /**
   * Check if order is cancelled
   */
  get isCancelled(): boolean {
    return this.status === OrderStatus.CANCELLED;
  }

  /**
   * Check if order is refunded
   */
  get isRefunded(): boolean {
    return this.status === OrderStatus.REFUNDED;
  }

  /**
   * Check if order can be cancelled
   */
  get canBeCancelled(): boolean {
    return [OrderStatus.PENDING, OrderStatus.CONFIRMED].includes(this.status);
  }

  /**
   * Check if order can be shipped
   */
  get canBeShipped(): boolean {
    return this.status === OrderStatus.CONFIRMED;
  }

  /**
   * Check if order can be refunded
   */
  get canBeRefunded(): boolean {
    return [OrderStatus.CONFIRMED, OrderStatus.SHIPPED, OrderStatus.DELIVERED].includes(
      this.status,
    );
  }

  /**
   * Get order age in days
   */
  get ageInDays(): number {
    return Math.floor((Date.now() - this.externalCreatedAt.getTime()) / (1000 * 60 * 60 * 24));
  }

  /**
   * Check if order is recent (within last 7 days)
   */
  get isRecent(): boolean {
    return this.ageInDays <= 7;
  }

  /**
   * Get full shipping address
   */
  get fullShippingAddress(): string {
    const parts = [
      this.shippingAddressLine1,
      this.shippingAddressLine2,
      this.shippingCity,
      this.shippingState,
      this.shippingPostalCode,
      this.shippingCountry,
    ].filter(Boolean);

    return parts.join(', ');
  }

  /**
   * Update status
   */
  updateStatus(status: OrderStatus, reason?: string): void {
    const oldStatus = this.status;
    this.status = status;

    // Update timestamps based on status
    switch (status) {
      case OrderStatus.CONFIRMED:
        if (!this.processedAt) {
          this.processedAt = new Date();
          this.isProcessed = true;
        }
        break;
      case OrderStatus.SHIPPED:
        this.shippedAt = new Date();
        break;
      case OrderStatus.DELIVERED:
        this.deliveredAt = new Date();
        break;
      case OrderStatus.CANCELLED:
        this.cancelledAt = new Date();
        if (reason) {
          this.cancellationReason = reason;
        }
        break;
    }

    // Add to metadata
    if (!this.metadata) {
      this.metadata = {};
    }
    this.metadata.statusHistory = this.metadata.statusHistory || [];
    this.metadata.statusHistory.push({
      from: oldStatus,
      to: status,
      timestamp: new Date().toISOString(),
      reason,
    });
  }

  /**
   * Confirm order
   */
  confirm(): void {
    if (!this.isPending) {
      throw new Error(`Cannot confirm order with status: ${this.status}`);
    }
    this.updateStatus(OrderStatus.CONFIRMED);
  }

  /**
   * Ship order
   */
  ship(): void {
    if (!this.canBeShipped) {
      throw new Error(`Cannot ship order with status: ${this.status}`);
    }
    this.updateStatus(OrderStatus.SHIPPED);
  }

  /**
   * Deliver order
   */
  deliver(): void {
    if (!this.isShipped) {
      throw new Error(`Cannot deliver order with status: ${this.status}`);
    }
    this.updateStatus(OrderStatus.DELIVERED);
  }

  /**
   * Cancel order
   */
  cancel(reason?: string): void {
    if (!this.canBeCancelled) {
      throw new Error(`Cannot cancel order with status: ${this.status}`);
    }
    this.updateStatus(OrderStatus.CANCELLED, reason);
  }

  /**
   * Refund order
   */
  refund(reason?: string): void {
    if (!this.canBeRefunded) {
      throw new Error(`Cannot refund order with status: ${this.status}`);
    }
    this.updateStatus(OrderStatus.REFUNDED, reason);
  }

  /**
   * Update customer information
   */
  updateCustomerInfo(data: { name?: string; email?: string; phone?: string }): void {
    if (data.name !== undefined) this.customerName = data.name;
    if (data.email !== undefined) this.customerEmail = data.email;
    if (data.phone !== undefined) this.customerPhone = data.phone;
  }

  /**
   * Update shipping address
   */
  updateShippingAddress(data: {
    addressLine1?: string;
    addressLine2?: string;
    city?: string;
    state?: string;
    postalCode?: string;
    country?: string;
  }): void {
    if (data.addressLine1 !== undefined) this.shippingAddressLine1 = data.addressLine1;
    if (data.addressLine2 !== undefined) this.shippingAddressLine2 = data.addressLine2;
    if (data.city !== undefined) this.shippingCity = data.city;
    if (data.state !== undefined) this.shippingState = data.state;
    if (data.postalCode !== undefined) this.shippingPostalCode = data.postalCode;
    if (data.country !== undefined) this.shippingCountry = data.country;
  }

  /**
   * Update pricing
   */
  updatePricing(data: {
    subtotalPrice?: number;
    shippingPrice?: number;
    taxAmount?: number;
    discountAmount?: number;
    totalPrice?: number;
    currency?: string;
  }): void {
    if (data.subtotalPrice !== undefined) this.subtotalPrice = data.subtotalPrice;
    if (data.shippingPrice !== undefined) this.shippingPrice = data.shippingPrice;
    if (data.taxAmount !== undefined) this.taxAmount = data.taxAmount;
    if (data.discountAmount !== undefined) this.discountAmount = data.discountAmount;
    if (data.totalPrice !== undefined) this.totalPrice = data.totalPrice;
    if (data.currency !== undefined) this.currency = data.currency;
  }

  /**
   * Add metadata
   */
  addMetadata(key: string, value: any): void {
    if (!this.metadata) {
      this.metadata = {};
    }
    this.metadata[key] = value;
  }

  /**
   * Get metadata value
   */
  getMetadata(key: string): any {
    return this.metadata?.[key];
  }

  /**
   * Get total price value object
   */
  getTotalPriceVO(): Price {
    return new Price(this.totalPrice, this.currency);
  }

  /**
   * Get subtotal price value object
   */
  getSubtotalPriceVO(): Price {
    return new Price(this.subtotalPrice, this.currency);
  }

  /**
   * Calculate total from components
   */
  calculateTotal(): number {
    return this.subtotalPrice + this.shippingPrice + this.taxAmount - this.discountAmount;
  }

  /**
   * Validate pricing consistency
   */
  validatePricing(): boolean {
    const calculatedTotal = this.calculateTotal();
    return Math.abs(calculatedTotal - this.totalPrice) < 0.01; // Allow for rounding differences
  }
}
