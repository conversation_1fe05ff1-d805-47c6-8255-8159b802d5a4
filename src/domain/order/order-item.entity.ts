import { <PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Join<PERSON><PERSON>umn, Index } from 'typeorm';
import { BaseEntity } from '@domain/common/base.entity';
import { Order } from './order.entity';
import { Price } from '@domain/product/value-objects/price.vo';
import { Quantity } from '@domain/product/value-objects/quantity.vo';
import { SKU } from '@domain/product/value-objects/sku.vo';

@Entity('order_items')
@Index(['orderId'])
@Index(['sku'])
@Index(['style', 'color', 'size'])
export class OrderItem extends BaseEntity {
  @Column({ type: 'varchar', length: 100 })
  sku!: string;

  @Column({ type: 'varchar', length: 255 })
  title!: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  style?: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  color?: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  design?: string;

  @Column({ type: 'int', nullable: true })
  size?: number;

  @Column({ type: 'int' })
  quantity!: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  unitPrice!: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  totalPrice!: number;

  @Column({ type: 'varchar', length: 10, default: 'USD' })
  currency!: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  externalProductId?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  externalVariantId?: string;

  @Column({ type: 'text', nullable: true })
  customization?: string;

  @Column({ type: 'text', nullable: true })
  engraving?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  imageUrl?: string;

  @Column({ type: 'text', nullable: true })
  note?: string;

  @Column({ type: 'boolean', default: false })
  isCustom!: boolean;

  @Column({ type: 'boolean', default: false })
  isEngrave!: boolean;

  @Column({ type: 'json', nullable: true })
  metadata?: Record<string, any>;

  @Column({
    type: 'json',
    nullable: true,
    comment: 'Raw item data from the platform API for debugging and future data extraction',
  })
  rawData?: Record<string, any>;

  @Column({ type: 'uuid' })
  orderId!: string;

  @ManyToOne(() => Order, order => order.items, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'orderId' })
  order!: Order;

  /**
   * Check if item is customized
   */
  get isCustomized(): boolean {
    return this.isCustom || this.isEngrave || !!this.customization || !!this.engraving;
  }

  /**
   * Check if item has complete product information
   */
  get hasCompleteProductInfo(): boolean {
    return !!(this.style && this.color && this.size && this.size > 0);
  }

  /**
   * Get line total (quantity * unit price)
   */
  get lineTotal(): number {
    return this.quantity * this.unitPrice;
  }

  /**
   * Check if calculated total matches stored total
   */
  get isTotalValid(): boolean {
    return Math.abs(this.lineTotal - this.totalPrice) < 0.01;
  }

  /**
   * Get display name
   */
  get displayName(): string {
    const parts = [this.style, this.color, this.size?.toString(), this.design].filter(Boolean);
    return parts.length > 0 ? parts.join(' - ') : this.title;
  }

  /**
   * Update quantity and recalculate total
   */
  updateQuantity(quantity: number): void {
    if (quantity < 1) {
      throw new Error('Quantity must be at least 1');
    }

    this.quantity = quantity;
    this.recalculateTotal();
  }

  /**
   * Update unit price and recalculate total
   */
  updateUnitPrice(unitPrice: number): void {
    if (unitPrice < 0) {
      throw new Error('Unit price cannot be negative');
    }

    this.unitPrice = unitPrice;
    this.recalculateTotal();
  }

  /**
   * Recalculate total price
   */
  recalculateTotal(): void {
    this.totalPrice = this.quantity * this.unitPrice;
  }

  /**
   * Update product information
   */
  updateProductInfo(data: {
    title?: string;
    style?: string;
    color?: string;
    design?: string;
    size?: number;
    externalProductId?: string;
    externalVariantId?: string;
    imageUrl?: string;
  }): void {
    if (data.title !== undefined) this.title = data.title;
    if (data.style !== undefined) this.style = data.style;
    if (data.color !== undefined) this.color = data.color;
    if (data.design !== undefined) this.design = data.design;
    if (data.size !== undefined) this.size = data.size;
    if (data.externalProductId !== undefined) this.externalProductId = data.externalProductId;
    if (data.externalVariantId !== undefined) this.externalVariantId = data.externalVariantId;
    if (data.imageUrl !== undefined) this.imageUrl = data.imageUrl;
  }

  /**
   * Update customization
   */
  updateCustomization(data: {
    customization?: string;
    engraving?: string;
    isCustom?: boolean;
    isEngrave?: boolean;
  }): void {
    if (data.customization !== undefined) {
      this.customization = data.customization;
      this.isCustom = !!data.customization;
    }
    if (data.engraving !== undefined) {
      this.engraving = data.engraving;
      this.isEngrave = !!data.engraving;
    }
    if (data.isCustom !== undefined) this.isCustom = data.isCustom;
    if (data.isEngrave !== undefined) this.isEngrave = data.isEngrave;
  }

  /**
   * Add metadata
   */
  addMetadata(key: string, value: any): void {
    if (!this.metadata) {
      this.metadata = {};
    }
    this.metadata[key] = value;
  }

  /**
   * Get metadata value
   */
  getMetadata(key: string): any {
    return this.metadata?.[key];
  }

  /**
   * Get SKU value object
   */
  getSkuVO(): SKU {
    return new SKU(this.sku);
  }

  /**
   * Get unit price value object
   */
  getUnitPriceVO(): Price {
    return new Price(this.unitPrice, this.currency);
  }

  /**
   * Get total price value object
   */
  getTotalPriceVO(): Price {
    return new Price(this.totalPrice, this.currency);
  }

  /**
   * Get quantity value object
   */
  getQuantityVO(): Quantity {
    return new Quantity(this.quantity);
  }

  /**
   * Check if item matches product criteria
   */
  matchesProduct(criteria: {
    sku?: string;
    style?: string;
    color?: string;
    size?: number;
    design?: string;
  }): boolean {
    if (criteria.sku && this.sku !== criteria.sku) return false;
    if (criteria.style && this.style !== criteria.style) return false;
    if (criteria.color && this.color !== criteria.color) return false;
    if (criteria.size && this.size !== criteria.size) return false;
    if (criteria.design && this.design !== criteria.design) return false;

    return true;
  }

  /**
   * Get customization summary
   */
  getCustomizationSummary(): string {
    const parts: string[] = [];

    if (this.customization) {
      parts.push(`Custom: ${this.customization}`);
    }

    if (this.engraving) {
      parts.push(`Engraving: ${this.engraving}`);
    }

    return parts.join(', ') || 'No customization';
  }

  /**
   * Validate item data
   */
  validate(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.sku) {
      errors.push('SKU is required');
    }

    if (!this.title) {
      errors.push('Title is required');
    }

    if (this.quantity < 1) {
      errors.push('Quantity must be at least 1');
    }

    if (this.unitPrice < 0) {
      errors.push('Unit price cannot be negative');
    }

    if (!this.isTotalValid) {
      errors.push('Total price does not match calculated value');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}
