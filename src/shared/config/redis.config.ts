import { registerAs } from '@nestjs/config';
import { CacheModuleOptions } from '@nestjs/cache-manager';
import * as redisStore from 'cache-manager-redis-store';
import * as Joi from 'joi';

export interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db: number;
  ttl: number;
  maxRetriesPerRequest: number;
  retryDelayOnFailover: number;
  enableReadyCheck: boolean;
}

export const redisConfig = registerAs('redis', (): CacheModuleOptions => {
  const config = {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379', 10),
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB || '0', 10),
    ttl: parseInt(process.env.REDIS_TTL || '300', 10), // 5 minutes default
    maxRetriesPerRequest: parseInt(process.env.REDIS_MAX_RETRIES || '3', 10),
    retryDelayOnFailover: parseInt(process.env.REDIS_RETRY_DELAY || '100', 10),
    enableReadyCheck: process.env.REDIS_READY_CHECK !== 'false',
  };

  // Validate configuration
  const schema = Joi.object({
    host: Joi.string().required(),
    port: Joi.number().port().required(),
    password: Joi.string().allow('').optional(),
    db: Joi.number().min(0).max(15).required(),
    ttl: Joi.number().min(1).required(),
    maxRetriesPerRequest: Joi.number().min(0).required(),
    retryDelayOnFailover: Joi.number().min(0).required(),
    enableReadyCheck: Joi.boolean().required(),
  });

  const { error } = schema.validate(config);
  if (error) {
    throw new Error(`Redis configuration validation error: ${error.message}`);
  }

  const redisOptions: any = {
    store: redisStore as any,
    host: config.host,
    port: config.port,
    db: config.db,
    ttl: config.ttl,
    maxRetriesPerRequest: config.maxRetriesPerRequest,
    retryDelayOnFailover: config.retryDelayOnFailover,
    enableReadyCheck: config.enableReadyCheck,
    isGlobal: true,
  };

  // Only add password if it's not empty
  if (config.password) {
    redisOptions.password = config.password;
  }

  return redisOptions;
});
