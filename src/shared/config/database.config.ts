import { registerAs } from '@nestjs/config';
import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import * as Jo<PERSON> from 'joi';

export interface DatabaseConfig {
  host: string;
  port: number;
  username: string;
  password: string;
  database: string;
  synchronize: boolean;
  logging: boolean;
  ssl: boolean;
  maxConnections: number;
  acquireTimeout: number;
  timeout: number;
}

export const databaseConfig = registerAs('database', (): TypeOrmModuleOptions => {
  const config = {
    host: process.env.DATABASE_HOST || process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DATABASE_PORT || process.env.DB_PORT || '5432', 10),
    username: process.env.DATABASE_USERNAME || process.env.DB_USERNAME || 'postgres',
    password: process.env.DATABASE_PASSWORD || process.env.DB_PASSWORD || 'postgres',
    database: process.env.DATABASE_NAME || process.env.DB_DATABASE || 'knot_core',
    synchronize:
      process.env.DATABASE_SYNCHRONIZE === 'true' || process.env.DB_SYNCHRONIZE === 'true' || true,
    logging: process.env.DATABASE_LOGGING === 'true' || process.env.DB_LOGGING === 'true',
    ssl: process.env.DATABASE_SSL === 'true' || process.env.DB_SSL === 'true',
    maxConnections: parseInt(
      process.env.DATABASE_MAX_CONNECTIONS || process.env.DB_MAX_CONNECTIONS || '20',
      10,
    ),
    acquireTimeout: parseInt(
      process.env.DATABASE_ACQUIRE_TIMEOUT || process.env.DB_ACQUIRE_TIMEOUT || '60000',
      10,
    ),
    timeout: parseInt(process.env.DATABASE_TIMEOUT || process.env.DB_TIMEOUT || '60000', 10),
  };

  // Validate configuration
  const schema = Joi.object({
    host: Joi.string().required(),
    port: Joi.number().port().required(),
    username: Joi.string().required(),
    password: Joi.string().required(),
    database: Joi.string().required(),
    synchronize: Joi.boolean().required(),
    logging: Joi.boolean().required(),
    ssl: Joi.boolean().required(),
    maxConnections: Joi.number().min(1).max(100).required(),
    acquireTimeout: Joi.number().min(1000).required(),
    timeout: Joi.number().min(1000).required(),
  });

  const { error } = schema.validate(config);
  if (error) {
    throw new Error(`Database configuration validation error: ${error.message}`);
  }

  return {
    type: 'postgres',
    host: config.host,
    port: config.port,
    username: config.username,
    password: config.password,
    database: config.database,
    synchronize: config.synchronize,
    logging: config.logging,
    ssl: config.ssl ? { rejectUnauthorized: false } : false,
    entities: [__dirname + '/../../domain/**/*.entity{.ts,.js}'],
    migrations: [__dirname + '/../../infrastructure/database/migrations/*{.ts,.js}'],
    migrationsRun: !config.synchronize,
    extra: {
      max: config.maxConnections,
      connectionTimeoutMillis: config.acquireTimeout,
      idleTimeoutMillis: config.timeout,
    },
    retryAttempts: 3,
    retryDelay: 3000,
    autoLoadEntities: true,
    keepConnectionAlive: true,
  };
});
