import { LoggerModuleAsyncParams } from 'nestjs-pino';
import { ConfigService } from '@nestjs/config';

export const loggerConfig = (): LoggerModuleAsyncParams => ({
  useFactory: (configService: ConfigService) => {
    const nodeEnv = configService.get<string>('NODE_ENV', 'development');
    const logLevel = configService.get<string>('LOG_LEVEL', 'info');
    const logFormat = configService.get<string>('LOG_FORMAT', 'json');

    const isDevelopment = nodeEnv === 'development';

    return {
      pinoHttp: {
        level: logLevel,
        transport: isDevelopment && logFormat !== 'json' ? {
          target: 'pino-pretty',
          options: {
            colorize: true,
            translateTime: 'SYS:standard',
            ignore: 'pid,hostname',
          },
        } : undefined,
        formatters: {
          level: (label: string) => ({ level: label }),
        },
        timestamp: () => `,"timestamp":"${new Date().toISOString()}"`,
        serializers: {
          req: (req: any) => ({
            id: req.id,
            method: req.method,
            url: req.url,
            headers: {
              host: req.headers.host,
              'user-agent': req.headers['user-agent'],
              'content-type': req.headers['content-type'],
            },
          }),
          res: (res: any) => ({
            statusCode: res.statusCode,
            headers: {
              'content-type': res.headers['content-type'],
            },
          }),
          err: (err: any) => ({
            type: err.type,
            message: err.message,
            stack: err.stack,
          }),
        },
        customLogLevel: (req: any, res: any, err: any) => {
          if (res.statusCode >= 400 && res.statusCode < 500) {
            return 'warn';
          } else if (res.statusCode >= 500 || err) {
            return 'error';
          }
          return 'info';
        },
        customSuccessMessage: (req: any, res: any) => {
          return `${req.method} ${req.url} ${res.statusCode}`;
        },
        customErrorMessage: (req: any, res: any, err: any) => {
          return `${req.method} ${req.url} ${res.statusCode} - ${err.message}`;
        },
      },
    };
  },
  inject: [ConfigService],
});
