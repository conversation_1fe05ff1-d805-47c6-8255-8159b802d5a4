import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  <PERSON><PERSON><PERSON><PERSON>,
  Logger,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger(LoggingInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();
    const { method, url, ip, headers } = request;
    const userAgent = headers['user-agent'] || '';

    const startTime = Date.now();

    return next.handle().pipe(
      tap(() => {
        const endTime = Date.now();
        const duration = endTime - startTime;
        const { statusCode } = response;

        const logData = {
          method,
          url,
          statusCode,
          duration: `${duration}ms`,
          ip,
          userAgent,
        };

        if (statusCode >= 400) {
          this.logger.warn(`${method} ${url} ${statusCode} - ${duration}ms`, logData);
        } else {
          this.logger.log(`${method} ${url} ${statusCode} - ${duration}ms`, logData);
        }
      }),
    );
  }
}
