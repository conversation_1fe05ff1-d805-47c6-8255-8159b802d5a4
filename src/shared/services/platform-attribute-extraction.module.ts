import { Modu<PERSON> } from '@nestjs/common';
import { PlatformAttributeExtractionService } from './platform-attribute-extraction.service';
import { ShopifyAttributeExtractor } from './platform-attribute-extractors/shopify-attribute-extractor.service';
import { EtsyAttributeExtractor } from './platform-attribute-extractors/etsy-attribute-extractor.service';
import { AmazonAttributeExtractor } from './platform-attribute-extractors/amazon-attribute-extractor.service';

@Module({
  providers: [
    PlatformAttributeExtractionService,
    ShopifyAttributeExtractor,
    EtsyAttributeExtractor,
    AmazonAttributeExtractor,
  ],
  exports: [
    PlatformAttributeExtractionService,
    ShopifyAttributeExtractor,
    EtsyAttributeExtractor,
    AmazonAttributeExtractor,
  ],
})
export class PlatformAttributeExtractionModule {}
