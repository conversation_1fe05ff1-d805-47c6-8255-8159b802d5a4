import { Test, TestingModule } from '@nestjs/testing';
import { SkuExtractionService } from '../sku-extraction.service';

describe('SkuExtractionService - Individual SKU Testing', () => {
  let service: SkuExtractionService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [SkuExtractionService],
    }).compile();

    service = module.get<SkuExtractionService>(SkuExtractionService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('Individual SKU extraction tests', () => {
    const testCases = [
      {
        sku: 'G-CF6-Cosmic-Custom-S07',
        expected: { style: 'CF6', color: 'Cosmic', size: 'S07', outside: undefined },
      },
      {
        sku: 'G-SCF-Custom-Black-S10',
        expected: { style: 'SCF', color: 'Black', size: 'S10', outside: undefined },
      },
      {
        sku: 'G-D6-S10-Enchanted-Forest',
        expected: { style: 'D6', color: 'Enchanted', size: 'S10', outside: 'Forest' },
      },
      {
        sku: 'ComfortFit-4mm-NeonLemonN-05',
        expected: { style: 'CF4', color: 'NeonLemon', size: 'S05', outside: undefined },
      },
      {
        sku: 'G-C4-SilverFiligree-BBlue-09',
        expected: {
          style: 'CF4',
          color: 'BBlue',
          size: 'S09',
          outside: 'Fili',
        },
      },
      {
        sku: 'CF-4mm-StarPurple-05',
        expected: { style: 'CF4', color: 'StarPurple', size: 'S05', outside: undefined },
      },
      {
        sku: 'ComfortFit-6mm-AgedGold-R10',
        expected: { style: 'CF6', color: 'Gold', size: 'S10', outside: undefined },
      },
      {
        sku: 'G-SCF-S09-Blue-Mount',
        expected: { style: 'SCF', color: 'Blue', size: 'S09', outside: 'Mount' },
      },
      {
        sku: 'G-C4-GoldFloral-White-05',
        expected: { style: 'CF4', color: 'White', size: 'S05', outside: 'GoldFloral' },
      },
      {
        sku: 'ComfortFit-6mm-RedGreenGold-09',
        expected: { style: 'CF6', color: 'Gold', size: 'S09', outside: undefined },
      },
      {
        sku: 'ComfortFit-4mm-Silver-5',
        expected: { style: 'CF4', color: 'Silver', size: 'S05', outside: undefined },
      },
      {
        sku: 'ComfortFit-4mm-AgedGold-05',
        expected: { style: 'CF4', color: 'Gold', size: 'S05', outside: undefined },
      },
      {
        sku: 'G-D6-S08-Silver-StrengthWarrior',
        expected: { style: 'D6', color: 'Silver', size: 'S08', outside: 'StrengthWarrior' },
      },
      {
        sku: 'G-D6-S06-Gold-SerenityWarrior',
        expected: { style: 'D6', color: 'Gold', size: 'S06', outside: 'SerenityWarrior' },
      },
      {
        sku: 'ComfortFit-6mm-Silver-13',
        expected: { style: 'CF6', color: 'Silver', size: 'S13' },
      },
      {
        sku: 'G-D6-S08-DSilver-Custom',
        expected: { style: 'D6', color: 'DSilver', size: 'S08' },
      },
      {
        sku: 'G-Custom-CF6-S08-Blue',
        expected: { style: 'CF6', color: 'Blue', size: 'S08' },
      },
      {
        sku: 'ComfortFit-4mm-Neon4packN-08',
        expected: { style: 'CF4', size: 'S08' },
      },
      {
        sku: 'G-D6-horse_Shoe-S06-SkyBlue',
        expected: { style: 'D6', size: 'S06', color: 'SkyBlue', outside: 'horse_Shoe' },
      },
      {
        sku: 'G-CF4-S05-Blue-Mount',
        expected: { style: 'CF4', color: 'Blue', size: 'S05', outside: 'Mount' },
      },
      {
        sku: 'G-C4-GoldFili-RGold-07',
        expected: { style: 'CF4', color: 'RoseGold', size: 'S07', outside: 'GoldFili' },
      },
      {
        sku: 'G-Rune_MyLuv-SCF-S14-Black',
        expected: { style: 'SCF', color: 'Black', size: 'S14', outside: 'Rune_MyLuv' },
      },
      {
        sku: 'CF-6mm-StarPurple-11',
        expected: { style: 'CF6', color: 'StarPurple', size: 'S11' },
      },
      {
        sku: 'ComfortFit-4mm-AgedGold-07',
        expected: { style: 'CF4', color: 'Gold', size: 'S07' },
      },
      {
        sku: 'Pyra-PearlOrange-09',
        expected: { style: 'Pyra', color: 'Orange', size: 'S09', outside: undefined },
      },
      {
        sku: 'G-CF4-StarFrost-Lotus-S08',
        expected: { style: 'CF4', color: 'StarFrost', size: 'S08', outside: 'Lotus' },
      },
      {
        sku: 'G-CF4-Snake-NL-08-goldInkIO',
        expected: { style: 'CF4', color: 'NLights', size: 'S08', outside: 'Snake' },
      },
      {
        sku: 'SCF-Tan-12',
        expected: { style: 'SCF', color: 'Tan', size: 'S12', outside: undefined },
      },
      {
        sku: 'G-CF6-S07-Silver-Mount',
        expected: { style: 'CF6', color: 'Silver', size: 'S07', outside: 'Mount' },
      },
      {
        sku: 'G-D6-S12-Blue-Trinity',
        expected: { style: 'D6', color: 'Blue', size: 'S12', outside: 'Trinity' },
      },
      {
        sku: 'G-CF6-S10-DSilver-Mount',
        expected: { style: 'CF6', color: 'DSilver', size: 'S10', outside: 'Mount' },
      },
      {
        sku: 'G-SCF-Custom-Blue-S10',
        expected: { style: 'SCF', color: 'Blue', size: 'S10', outside: undefined },
      },
      {
        sku: 'G-Custom-CF4-S06-Blue',
        expected: { style: 'CF4', color: 'Blue', size: 'S06', outside: undefined },
      },
      {
        sku: 'G-Custom-CF6-S08-Blue',
        expected: { style: 'CF6', color: 'Blue', size: 'S08', outside: undefined },
      },
      {
        sku: 'BevelCF-6mm-BlackMarble-06',
        expected: { style: 'BevelCF', color: 'BMarble', size: 'S06', outside: undefined },
      },
      {
        sku: 'G-D6-S11-Enchanted-Forest',
        expected: { style: 'D6', color: 'Enchanted', size: 'S11', outside: 'Forest' },
      },
      {
        sku: 'ComfortFit-4mm-RoseGold-8',
        expected: { style: 'CF4', color: 'RoseGold', size: 'S08', outside: undefined },
      },
      {
        sku: 'BevelCF-6mm-MetallicDarkRedN-07',
        expected: { style: 'BevelCF', color: 'Red', size: 'S07', outside: undefined },
      },
      {
        sku: 'G-CF4-Flower01-S07-Enchanted',
        expected: { style: 'CF4', color: 'Enchanted', size: 'S07', outside: 'Flower01' },
      },
      {
        sku: 'ComfortFit-6mm-DarkBronze-14',
        expected: { style: 'CF6', color: 'DarkBronze', size: 'S14', outside: undefined },
      },
      {
        sku: 'ComfortFit-4mm-GreenEnchanted-04',
        expected: { style: 'CF4', color: 'Enchanted', size: 'S04', outside: undefined },
      },
      {
        sku: 'ComfortFit-6mm-GreenEnchanted-09',
        expected: { style: 'CF6', color: 'Enchanted', size: 'S09', outside: undefined },
      },
      {
        sku: 'ComfortFit-4mm-NeonPinkN-05',
        expected: { style: 'CF4', color: 'NeonPink', size: 'S05', outside: undefined },
      },
      {
        sku: 'G-CF6-Enchanted-Custom-S09',
        expected: { style: 'CF6', color: 'Enchanted', size: 'S09', outside: undefined },
      },
      {
        sku: 'ComfortFit-4mm-Silver-8',
        expected: { style: 'CF4', color: 'Silver', size: 'S08', outside: undefined },
      },
      {
        sku: 'G-CF4-S05-Blue-Waves',
        expected: { style: 'CF4', color: 'Blue', size: 'S05', outside: 'Waves' },
      },
      {
        sku: 'G-D6-S08-Champ-Unicorn',
        expected: { style: 'D6', color: 'Champagne', size: 'S08', outside: 'Unicorn' },
      },
      {
        sku: 'G-C4-GoldFili-RGold-09',
        expected: { style: 'CF4', color: 'RoseGold', size: 'S09', outside: 'GoldFili' },
      },
      {
        sku: 'G-D6-S06-Enchanted-Mount',
        expected: { style: 'D6', color: 'Enchanted', size: 'S06', outside: 'Mount' },
      },
      {
        sku: 'ComfortFit-6mm-AgedGold-R10',
        expected: { style: 'CF6', color: 'Gold', size: 'S10', outside: undefined },
      },
      {
        sku: 'G-D6-S07-Enchanted-Mount',
        expected: { style: 'D6', color: 'Enchanted', size: 'S07', outside: 'Mount' },
      },
      {
        sku: 'G-CF6-S07-Blue-Mount',
        expected: { style: 'CF6', color: 'Blue', size: 'S07', outside: 'Mount' },
      },
      {
        sku: 'G-SCF-S10-Blue-Mount',
        expected: { style: 'SCF', color: 'Blue', size: 'S10', outside: 'Mount' },
      },
      {
        sku: 'BevelCF-6mm-Silver-08',
        expected: { style: undefined, color: 'Silver', size: 'S08', outside: undefined },
      },
      {
        sku: 'G-CF4-Black-Custom-S06',
        expected: { style: 'CF4', color: 'Black', size: 'S06', outside: undefined },
      },
      {
        sku: 'G-CF6-Forest-DSilver-S08',
        expected: { style: 'CF6', color: 'DSilver', size: 'S08', outside: 'Forest' },
      },
      {
        sku: 'G-SCF-S14-DSilver-RuneRustic',
        expected: { style: 'SCF', color: 'DSilver', size: 'S14', outside: 'RuneRustic' },
      },
      {
        sku: 'G-CelticInfinity-D6-Silver-S11',
        expected: { style: 'D6', color: 'Silver', size: 'S11', outside: 'CelticInfinity' },
      },
      {
        sku: 'Test-RoseGold-Design-S10',
        expected: { style: undefined, color: 'RoseGold', size: 'S10', outside: undefined },
      },
      {
        sku: 'Test-GoldFiligree-Silver-S08',
        expected: { style: undefined, color: 'Silver', size: 'S08', outside: 'Fili' },
      },
      {
        sku: 'CF-4mm-StarPurple-05',
        expected: { style: 'CF4', color: undefined, size: 'S05', outside: undefined },
      },
      {
        sku: 'Bevel-WhiteLine-12',
        expected: { style: undefined, color: 'White', size: 'S12', outside: undefined },
      },
      {
        sku: 'G-D6-Lace-Gold-12',
        expected: { style: 'D6', color: 'Gold', size: 'S12', outside: 'Lace' },
      },
      {
        sku: 'G-D6-S09-Silver-Waves',
        expected: { style: 'D6', color: 'Silver', size: 'S09', outside: 'Waves' },
      },
      {
        sku: 'G-D6-S12-Silver-Forest',
        expected: { style: 'D6', color: 'Silver', size: 'S12', outside: 'Forest' },
      },
      {
        sku: 'G-SCF-Custom-Blue-S13',
        expected: { style: 'SCF', color: 'Blue', size: 'S13', outside: undefined },
      },
      {
        sku: 'BevelCF-6mm-MetallicDarkBlue-09',
        expected: { style: undefined, color: 'Blue', size: 'S09', outside: undefined },
      },
      {
        sku: 'G-CF6-Forest-DSilver-S16',
        expected: { style: 'CF6', color: 'DSilver', size: 'S16', outside: 'Forest' },
      },
      {
        sku: 'ComfortFit-4mm-PearlWhite-6',
        expected: { style: 'CF4', color: 'White', size: 'S06', outside: undefined },
      },
      {
        sku: 'ComfortFit-6mm-PearlWhite-8',
        expected: { style: 'CF6', color: 'White', size: 'S08', outside: undefined },
      },
      {
        sku: 'G-Custom-CF6-S10-Other',
        expected: { style: 'CF6', color: 'Other', size: 'S10', outside: undefined },
      },
      {
        sku: 'G-CF4-S07-Enchanted-Celtic',
        expected: { style: 'CF4', color: 'Enchanted', size: 'S07', outside: 'Celtic' },
      },
      {
        sku: 'G-D6-S11-DSilver-SerenityWarrior',
        expected: { style: 'D6', color: 'DSilver', size: 'S11', outside: 'SerenityWarrior' },
      },
      {
        sku: 'G-CF6-S11-DSilver-Mount',
        expected: { style: 'CF6', color: 'DSilver', size: 'S11', outside: 'Mount' },
      },
      {
        sku: 'G-CelticSailor-D6-DSilver-S10',
        expected: { style: 'D6', color: 'DSilver', size: 'S10', outside: 'CelticSailor' },
      },
      {
        sku: 'G-D6-S06-Enchanted-Mount',
        expected: { style: 'D6', color: 'Enchanted', size: 'S06', outside: 'Mount' },
      },
      {
        sku: 'G-Rune_MyLuv-SCF-S14-Black',
        expected: { style: 'SCF', color: 'Black', size: 'S14', outside: 'Rune_MyLuv' },
      },
      {
        sku: 'CF-4mm-PearlTiffany-06',
        expected: { style: 'CF4', color: undefined, size: 'S06', outside: undefined },
      },
      {
        sku: 'ComfortFit-4mm-MetalDarkRed-05',
        expected: { style: 'CF4', color: 'Red', size: 'S05', outside: undefined },
      },
      // Special test cases for inlay options and prefixed designs
      {
        sku: 'G-ComfortFit-6mm-Silver-12-csFili',
        expected: {
          style: 'CF6',
          color: 'Silver',
          size: 'S12',
          outside: 'Fili',
          inlayOption: {
            insideInlay: false,
            outsideInlay: true,
            color: 'clear',
          },
        },
      },
      {
        sku: 'G-ComfortFit-6mm-Silver-12-gsTrinity',
        expected: {
          style: 'CF6',
          color: 'Silver',
          size: 'S12',
          outside: 'Trinity',
          inlayOption: {
            insideInlay: false,
            outsideInlay: true,
            color: 'gold',
          },
        },
      },
      {
        sku: 'G-ComfortFit-6mm-Silver-12-ssCeltic',
        expected: {
          style: 'CF6',
          color: 'Silver',
          size: 'S12',
          outside: 'Celtic',
          inside: undefined,
          inlayOption: {
            insideInlay: false,
            outsideInlay: true,
            color: 'silver',
          },
        },
      },
    ];

    testCases.forEach((testCase, index) => {
      it(`SKU ${index + 1}: ${testCase.sku}`, () => {
        const result = service.extractCompleteOrderData({
          sku: testCase.sku,
          title: `Ring with ${testCase.sku}`,
          source: 'etsy' as const,
        });

        // Check each expected field
        if (testCase.expected.style !== undefined) {
          expect(result.style).toBe(testCase.expected.style);
        }
        if (testCase.expected.color !== undefined) {
          expect(result.color).toBe(testCase.expected.color);
        }
        if (testCase.expected.size !== undefined) {
          expect(result.size).toBe(testCase.expected.size);
        }
        if (testCase.expected.outside !== undefined) {
          expect(result.outside).toBe(testCase.expected.outside);
        }
        if (testCase.expected.inside !== undefined) {
          expect(result.inside).toBe(testCase.expected.inside);
        }
        if (testCase.expected.inlayOption !== undefined) {
          expect(result.inlayOption).toEqual(testCase.expected.inlayOption);
        }
      });
    });
  });
});
