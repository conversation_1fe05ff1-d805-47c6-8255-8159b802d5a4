import { Injectable } from '@nestjs/common';
import { PlatformSource } from '@domain/product/platform-source.enum';

export interface InlayOption {
  insideInlay: boolean;
  outsideInlay: boolean;
  color?: string;
}

export interface ExtractedAttributes {
  style?: string;
  color?: string;
  size?: number;
  insideDesign?: string;
  outsideDesign?: string;
  design?: string; // Keep for backward compatibility
  isCustom: boolean;
  isEngrave: boolean;
  customization?: string;
  engraving?: string;
  inlayOption?: InlayOption;
  inlayType?: 'gold' | 'silver' | 'clear' | 'none'; // Keep for backward compatibility
}

export interface PlatformExtractionData {
  skuExtracted: ExtractedAttributes;
  platformData: any;
  title?: string;
  sku?: string;
}

@Injectable()
export abstract class PlatformAttributeExtractor {
  protected abstract readonly platform: PlatformSource;

  /**
   * Extract attributes from platform-specific data
   * This runs after SKU extraction to fill in any missing attributes
   */
  abstract extractPlatformAttributes(data: PlatformExtractionData): ExtractedAttributes;

  /**
   * Extract attributes from properties/variations (platform-specific format)
   */
  protected abstract extractFromProperties(properties: any[]): Partial<ExtractedAttributes>;

  /**
   * Extract customization and engraving info (platform-specific format)
   */
  protected abstract extractCustomizationInfo(properties: any[]): {
    isCustom: boolean;
    isEngrave: boolean;
    customization?: string;
    engraving?: string;
  };

  /**
   * Merge SKU extracted attributes with platform-specific attributes
   * Platform attributes take priority for missing fields
   */
  protected mergeAttributes(
    skuExtracted: ExtractedAttributes,
    platformExtracted: Partial<ExtractedAttributes>,
  ): ExtractedAttributes {
    return {
      style: skuExtracted.style || platformExtracted.style,
      color: skuExtracted.color || platformExtracted.color,
      size: skuExtracted.size || platformExtracted.size,
      insideDesign: skuExtracted.insideDesign || platformExtracted.insideDesign,
      outsideDesign: skuExtracted.outsideDesign || platformExtracted.outsideDesign,
      design:
        skuExtracted.design ||
        platformExtracted.design ||
        skuExtracted.insideDesign ||
        platformExtracted.insideDesign, // Fallback to insideDesign
      inlayOption: this.mergeInlayOptions(skuExtracted.inlayOption, platformExtracted.inlayOption),
      inlayType: skuExtracted.inlayType || platformExtracted.inlayType,
      isCustom: skuExtracted.isCustom || platformExtracted.isCustom || false,
      isEngrave: skuExtracted.isEngrave || platformExtracted.isEngrave || false,
      customization: skuExtracted.customization || platformExtracted.customization,
      engraving: skuExtracted.engraving || platformExtracted.engraving,
    };
  }

  /**
   * Merge inlay options from SKU and platform extraction
   */
  private mergeInlayOptions(
    skuInlay?: InlayOption,
    platformInlay?: InlayOption,
  ): InlayOption | undefined {
    if (!skuInlay && !platformInlay) {
      return undefined;
    }

    if (!skuInlay) return platformInlay;
    if (!platformInlay) return skuInlay;

    return {
      insideInlay: skuInlay.insideInlay || platformInlay.insideInlay,
      outsideInlay: skuInlay.outsideInlay || platformInlay.outsideInlay,
      color: skuInlay.color || platformInlay.color,
    };
  }

  /**
   * Helper method to normalize size values
   */
  protected normalizeSize(sizeValue: any): number | undefined {
    if (typeof sizeValue === 'number') {
      return sizeValue;
    }

    if (typeof sizeValue === 'string') {
      // Extract number from strings like "6", "Size 6", "6mm", "6 (6mm bandwidth)"
      const match = sizeValue.match(/(\d+)/);
      if (match) {
        return parseInt(match[1], 10);
      }
    }

    return undefined;
  }

  /**
   * Helper method to normalize string values
   */
  protected normalizeString(value: any): string | undefined {
    if (typeof value === 'string' && value.trim()) {
      return value.trim();
    }
    return undefined;
  }

  /**
   * Helper method to extract inlay option from text
   */
  protected extractInlayOption(text: string): InlayOption | undefined {
    const lowerText = text.toLowerCase();

    const hasInsideInlay =
      lowerText.includes('inside inlay') ||
      lowerText.includes('inside fill') ||
      lowerText.includes('inner inlay');
    const hasOutsideInlay =
      lowerText.includes('outside inlay') ||
      lowerText.includes('outside fill') ||
      lowerText.includes('outer inlay');

    if (!hasInsideInlay && !hasOutsideInlay) {
      return undefined;
    }

    // Extract color from text
    const inlayColors = ['gold', 'silver', 'clear', 'transparent', 'none', 'no'];
    let inlayColor: string | undefined;

    for (const color of inlayColors) {
      if (lowerText.includes(color)) {
        inlayColor = color === 'transparent' ? 'clear' : color === 'no' ? 'none' : color;
        break;
      }
    }

    return {
      insideInlay: hasInsideInlay,
      outsideInlay: hasOutsideInlay,
      color: inlayColor,
    };
  }

  /**
   * Helper method to check for customization keywords
   */
  protected checkCustomizationKeywords(text: string): {
    isCustom: boolean;
    isEngrave: boolean;
    hasEngravingText: boolean;
  } {
    const lowerText = text.toLowerCase();

    return {
      isCustom: lowerText.includes('custom') || lowerText.includes('personalized'),
      isEngrave: lowerText.includes('engrav') || lowerText.includes('inscribed'),
      hasEngravingText:
        lowerText.includes('engraving text') || lowerText.includes('personalization'),
    };
  }
}
