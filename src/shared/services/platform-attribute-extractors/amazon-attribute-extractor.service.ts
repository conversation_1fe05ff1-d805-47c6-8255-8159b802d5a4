import { Injectable } from '@nestjs/common';
import { PlatformSource } from '@domain/product/platform-source.enum';
import {
  PlatformAttributeExtractor,
  ExtractedAttributes,
  PlatformExtractionData,
} from '../platform-attribute-extractor.abstract';

export interface AmazonProductInfo {
  title?: string;
  description?: string;
  features?: string[];
  specifications?: Record<string, string>;
}

export interface AmazonPlatformData {
  platform: 'amazon';
  asin: string;
  sellerSku: string;
  title?: string;
  productInfo?: AmazonProductInfo;
  conditionId?: string;
  isGift?: boolean;
}

@Injectable()
export class AmazonAttributeExtractor extends PlatformAttributeExtractor {
  protected readonly platform = PlatformSource.AMAZON;

  /**
   * Extract attributes from Amazon-specific data
   */
  extractPlatformAttributes(data: PlatformExtractionData): ExtractedAttributes {
    const { skuExtracted, platformData } = data;

    if (!platformData || platformData.platform !== 'amazon') {
      return skuExtracted;
    }

    const amazonData = platformData as AmazonPlatformData;

    // Extract from product info and title
    const productInfoExtracted = this.extractFromProductInfo(amazonData);

    // Extract customization info from title and description
    const customizationInfo = this.extractCustomizationInfoFromAmazonData(amazonData);

    // Merge all extracted data
    const platformExtracted: Partial<ExtractedAttributes> = {
      ...productInfoExtracted,
      ...customizationInfo,
    };

    return this.mergeAttributes(skuExtracted, platformExtracted);
  }

  /**
   * Extract attributes from Amazon properties/variations (not applicable for Amazon)
   */
  protected extractFromProperties(properties: any[]): Partial<ExtractedAttributes> {
    // Amazon doesn't have properties like Shopify/Etsy, return empty object
    return {};
  }

  /**
   * Extract attributes from Amazon product info
   */
  protected extractFromProductInfo(amazonData: AmazonPlatformData): Partial<ExtractedAttributes> {
    const extracted: Partial<ExtractedAttributes> = {};

    const title = amazonData.title || '';
    const productInfo = amazonData.productInfo;
    const description = productInfo?.description || '';
    const features = productInfo?.features || [];
    const specifications = productInfo?.specifications || {};

    const combinedText = `${title} ${description} ${features.join(' ')}`.toLowerCase();

    // Extract size from title, description, or specifications
    const sizeSources = [title, description, ...features, ...Object.values(specifications)];

    for (const source of sizeSources) {
      if (typeof source === 'string') {
        const size = this.extractSizeFromText(source);
        if (size) {
          extracted.size = size;
          break;
        }
      }
    }

    // Extract color from title, description, or specifications
    const color = this.extractColorFromText(combinedText);
    if (color) {
      extracted.color = color;
    }

    // Extract style from title or description
    const style = this.extractStyleFromText(combinedText);
    if (style) {
      extracted.style = style;
    }

    // Extract design from title or description
    const design = this.extractDesignFromText(combinedText);
    if (design) {
      extracted.design = design;
    }

    return extracted;
  }

  /**
   * Extract customization and engraving info from Amazon data
   */
  protected extractCustomizationInfo(properties: any[]): {
    isCustom: boolean;
    isEngrave: boolean;
    customization?: string;
    engraving?: string;
  } {
    // For Amazon, we'll use the platform data from the main extraction method
    // This is a placeholder implementation since Amazon doesn't have properties array
    return {
      isCustom: false,
      isEngrave: false,
    };
  }

  /**
   * Extract customization and engraving info from Amazon data (internal method)
   */
  private extractCustomizationInfoFromAmazonData(amazonData: AmazonPlatformData): {
    isCustom: boolean;
    isEngrave: boolean;
    customization?: string;
    engraving?: string;
  } {
    const title = amazonData.title || '';
    const productInfo = amazonData.productInfo;
    const description = productInfo?.description || '';
    const features = productInfo?.features || [];

    const combinedText = `${title} ${description} ${features.join(' ')}`;

    const customizationKeywords = this.checkCustomizationKeywords(combinedText);

    return {
      isCustom: customizationKeywords.isCustom,
      isEngrave: customizationKeywords.isEngrave,
      customization: customizationKeywords.hasEngravingText ? combinedText : undefined,
      engraving: customizationKeywords.hasEngravingText ? combinedText : undefined,
    };
  }

  /**
   * Extract size from text
   */
  private extractSizeFromText(text: string): number | undefined {
    // Common size patterns in Amazon product descriptions
    const sizePatterns = [
      /size\s*(\d+)/i,
      /(\d+)\s*mm/i,
      /(\d+)\s*inch/i,
      /(\d+)\s*band/i,
      /(\d+)\s*width/i,
      /(\d+)\s*\(/i,
      /ring\s*size\s*(\d+)/i,
      /(\d+)\s*size/i,
    ];

    for (const pattern of sizePatterns) {
      const match = text.match(pattern);
      if (match) {
        const size = parseInt(match[1], 10);
        if (size && size > 0 && size < 20) {
          // Reasonable ring size range
          return size;
        }
      }
    }

    return undefined;
  }

  /**
   * Extract color from text
   */
  private extractColorFromText(text: string): string | undefined {
    const colorKeywords = [
      'rose gold',
      'rosegold',
      'rgold',
      'sky blue',
      'skyblue',
      'champagne',
      'champ',
      'northern lights',
      'nlights',
      'nl',
      'enchanted',
      'ef',
      'dark silver',
      'dsilver',
      'light gold',
      'lgold',
      'light grey',
      'lgrey',
      'baby blue',
      'bblue',
      'black marble',
      'bmarble',
      'white marble',
      'wmarble',
      'pure white',
      'pwhite',
      'pure pink',
      'ppink',
      'baby pink',
      'bpink',
      'neon pink',
      'neonp',
      'neon lemon',
      'neonl',
      'neon orange',
      'neono',
      'neon swirl',
      'neons',
      'dark bronze',
      'dbronze',
      'metal teal',
      'metalteal',
      'metal earth green',
      'metearthgreen',
      'star purple',
      'starpurple',
      'star frost',
      'starfrost',
      'aqua blue',
      'aquablue',
      'blue sky',
      'bluesky',
      'silver',
      'gold',
      'black',
      'white',
      'blue',
      'red',
      'green',
      'purple',
      'pink',
      'yellow',
      'orange',
    ];

    const lowerText = text.toLowerCase();

    for (const keyword of colorKeywords) {
      if (lowerText.includes(keyword)) {
        return this.convertColorKeyword(keyword);
      }
    }

    return undefined;
  }

  /**
   * Extract style from text
   */
  private extractStyleFromText(text: string): string | undefined {
    const styleKeywords = [
      'comfortfit',
      'comfort fit',
      'classic',
      'traditional',
      'modern',
      'contemporary',
      'vintage',
      'antique',
      'minimalist',
      'simple',
      'elegant',
      'sophisticated',
      'casual',
      'formal',
      'wedding',
      'engagement',
      'promise',
      'friendship',
      'anniversary',
      'graduation',
    ];

    const lowerText = text.toLowerCase();

    for (const keyword of styleKeywords) {
      if (lowerText.includes(keyword)) {
        return this.normalizeString(keyword);
      }
    }

    return undefined;
  }

  /**
   * Extract design from text
   */
  private extractDesignFromText(text: string): string | undefined {
    const designKeywords = [
      'filigree',
      'fili',
      'trinity',
      'trinityz',
      'floral',
      'flower',
      'celtic',
      'celtic infinity',
      'warrior',
      'strength warrior',
      'courage warrior',
      'infinity',
      'infinity symbol',
      'heart',
      'love',
      'star',
      'constellation',
      'geometric',
      'abstract',
      'nature',
      'leaf',
      'butterfly',
      'dragonfly',
      'wave',
      'ocean',
      'mountain',
      'tree',
    ];

    const lowerText = text.toLowerCase();

    for (const keyword of designKeywords) {
      if (lowerText.includes(keyword)) {
        return this.normalizeString(keyword);
      }
    }

    return undefined;
  }

  /**
   * Convert color keyword to proper color name
   */
  private convertColorKeyword(keyword: string): string {
    const colorMappings: Record<string, string> = {
      'rose gold': 'RoseGold',
      rosegold: 'RoseGold',
      rgold: 'RoseGold',
      'sky blue': 'SkyBlue',
      skyblue: 'SkyBlue',
      champagne: 'Champagne',
      champ: 'Champagne',
      'northern lights': 'NLights',
      nlights: 'NLights',
      nl: 'NLights',
      enchanted: 'Enchanted',
      ef: 'Enchanted',
      'dark silver': 'DSilver',
      dsilver: 'DSilver',
      'light gold': 'LGold',
      lgold: 'LGold',
      'light grey': 'LGrey',
      lgrey: 'LGrey',
      'baby blue': 'BabyBlue',
      bblue: 'BabyBlue',
      'black marble': 'BMarble',
      bmarble: 'BMarble',
      'white marble': 'WMarble',
      wmarble: 'WMarble',
      'pure white': 'PWhite',
      pwhite: 'PWhite',
      'pure pink': 'PPink',
      ppink: 'PPink',
      'baby pink': 'BPink',
      bpink: 'BPink',
      'neon pink': 'NeonPink',
      neonp: 'NeonPink',
      'neon lemon': 'NeonLemon',
      neonl: 'NeonLemon',
      'neon orange': 'NeonOrange',
      neono: 'NeonOrange',
      'neon swirl': 'NeonSwirl',
      neons: 'NeonSwirl',
      'dark bronze': 'DarkBronze',
      dbronze: 'DarkBronze',
      'metal teal': 'MetalTeal',
      metalteal: 'MetalTeal',
      'metal earth green': 'MetEarthGreen',
      metearthgreen: 'MetEarthGreen',
      'star purple': 'StarPurple',
      starpurple: 'StarPurple',
      'star frost': 'StarFrost',
      starfrost: 'StarFrost',
      'aqua blue': 'AquaBlue',
      aquablue: 'AquaBlue',
      'blue sky': 'BlueSky',
      bluesky: 'BlueSky',
      silver: 'Silver',
      gold: 'Gold',
      black: 'Black',
      white: 'White',
      blue: 'Blue',
      red: 'Red',
      green: 'Green',
      purple: 'Purple',
      pink: 'Pink',
      yellow: 'Yellow',
      orange: 'Orange',
    };

    return colorMappings[keyword] || keyword;
  }
}
