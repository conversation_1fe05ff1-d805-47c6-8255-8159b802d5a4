import { Injectable } from '@nestjs/common';
import { PlatformSource } from '@domain/product/platform-source.enum';
import {
  PlatformAttributeExtractor,
  ExtractedAttributes,
  PlatformExtractionData,
} from '../platform-attribute-extractor.abstract';

export interface EtsyVariation {
  formatted_name: string;
  formatted_value: string;
}

export interface EtsyProductData {
  property_name: string;
  values: string[];
}

export interface EtsyPlatformData {
  platform: 'etsy';
  listingId: number;
  transactionId?: number;
  variations?: EtsyVariation[];
  productData?: EtsyProductData[];
}

@Injectable()
export class EtsyAttributeExtractor extends PlatformAttributeExtractor {
  protected readonly platform = PlatformSource.ETSY;

  /**
   * Extract attributes from Etsy-specific data
   */
  extractPlatformAttributes(data: PlatformExtractionData): ExtractedAttributes {
    const { skuExtracted, platformData } = data;

    if (!platformData || platformData.platform !== 'etsy') {
      return skuExtracted;
    }

    const etsyData = platformData as EtsyPlatformData;

    // Extract from variations
    const variationsExtracted = this.extractFromProperties(etsyData.variations || []);

    // Extract from product data
    const productDataExtracted = this.extractFromProductData(etsyData.productData || []);

    // Extract customization info
    const customizationInfo = this.extractCustomizationInfo(etsyData.variations || []);

    // Merge all extracted data
    const platformExtracted: Partial<ExtractedAttributes> = {
      ...variationsExtracted,
      ...productDataExtracted,
      ...customizationInfo,
    };

    return this.mergeAttributes(skuExtracted, platformExtracted);
  }

  /**
   * Extract attributes from Etsy variations
   */
  protected extractFromProperties(variations: EtsyVariation[]): Partial<ExtractedAttributes> {
    const extracted: Partial<ExtractedAttributes> = {};

    for (const variation of variations) {
      const name = variation.formatted_name.toLowerCase().trim();
      const value = variation.formatted_value.trim();

      // Skip empty values
      if (!value || value.toLowerCase() === 'none') {
        continue;
      }

      // Extract size
      if (name.includes('size') && !name.includes('text') && !name.includes('color')) {
        const size = this.normalizeSize(value);
        if (size) {
          extracted.size = size;
        }
      }

      // Extract color
      if (name.includes('color') && !name.includes('text') && !name.includes('inside')) {
        const color = this.normalizeString(value);
        if (color) {
          extracted.color = color;
        }
      }

      // Extract style
      if (name.includes('style') || name.includes('type')) {
        const style = this.normalizeString(value);
        if (style) {
          extracted.style = style;
        }
      }

      // Extract design/pattern
      if (name.includes('design') || name.includes('pattern')) {
        const design = this.normalizeString(value);
        if (design) {
          extracted.design = design;
        }
      }
    }

    return extracted;
  }

  /**
   * Extract attributes from Etsy product data
   */
  protected extractFromProductData(productData: EtsyProductData[]): Partial<ExtractedAttributes> {
    const extracted: Partial<ExtractedAttributes> = {};

    for (const data of productData) {
      const name = data.property_name.toLowerCase().trim();
      const values = data.values || [];

      if (values.length === 0) {
        continue;
      }

      const value = values[0].trim();

      // Skip empty values
      if (!value || value.toLowerCase() === 'none') {
        continue;
      }

      // Extract size
      if (name.includes('size') || name === 'custom property') {
        const size = this.normalizeSize(value);
        if (size) {
          extracted.size = size;
        }
      }

      // Extract color
      if (name.includes('color') || name.includes('colour') || name === 'primary color') {
        const color = this.normalizeString(value);
        if (color) {
          extracted.color = color;
        }
      }

      // Extract style
      if (name.includes('style') || name.includes('type')) {
        const style = this.normalizeString(value);
        if (style) {
          extracted.style = style;
        }
      }

      // Extract design
      if (name.includes('design') || name.includes('pattern')) {
        const design = this.normalizeString(value);
        if (design) {
          extracted.design = design;
        }
      }
    }

    return extracted;
  }

  /**
   * Extract customization and engraving info from Etsy variations
   */
  protected extractCustomizationInfo(variations: EtsyVariation[]): {
    isCustom: boolean;
    isEngrave: boolean;
    customization?: string;
    engraving?: string;
  } {
    let isCustom = false;
    let isEngrave = false;
    let customization: string | undefined;
    let engraving: string | undefined;

    for (const variation of variations) {
      const name = variation.formatted_name.toLowerCase().trim();
      const value = variation.formatted_value.trim();

      // Check for personalization/engraving
      if (
        name.includes('personalization') ||
        name.includes('engraving') ||
        name.includes('custom text') ||
        name.includes('message')
      ) {
        if (value && value.toLowerCase() !== 'none' && value.toLowerCase() !== 'no') {
          isEngrave = true;
          engraving = value;
        }
      }

      // Check for customization
      if (name.includes('customization') || name.includes('custom')) {
        if (value && value.toLowerCase() !== 'none' && value.toLowerCase() !== 'no') {
          isCustom = true;
          customization = value;
        }
      }

      // Check property names for customization indicators
      const nameKeywords = this.checkCustomizationKeywords(name);
      if (nameKeywords.isCustom) {
        isCustom = true;
      }
      if (nameKeywords.isEngrave) {
        isEngrave = true;
      }
    }

    return {
      isCustom,
      isEngrave,
      customization,
      engraving,
    };
  }

  /**
   * Extract attributes from transaction title and description
   */
  extractFromTransactionData(title?: string, description?: string): Partial<ExtractedAttributes> {
    const extracted: Partial<ExtractedAttributes> = {};

    const titleText = title || '';
    const descriptionText = description || '';
    const combinedText = `${titleText} ${descriptionText}`.toLowerCase();

    // Extract size from title/description (e.g., "Size 6", "6mm", "6 (6mm bandwidth)")
    const sizeMatch = combinedText.match(/size\s*(\d+)|(\d+)\s*mm|(\d+)\s*\(/);
    if (sizeMatch) {
      const size = parseInt(sizeMatch[1] || sizeMatch[2] || sizeMatch[3], 10);
      if (size) {
        extracted.size = size;
      }
    }

    // Extract color from title/description
    const colorKeywords = [
      'rose gold',
      'rosegold',
      'rgold',
      'sky blue',
      'skyblue',
      'champagne',
      'champ',
      'northern lights',
      'nlights',
      'nl',
      'enchanted',
      'ef',
      'dark silver',
      'dsilver',
      'light gold',
      'lgold',
      'light grey',
      'lgrey',
      'baby blue',
      'bblue',
      'black marble',
      'bmarble',
      'white marble',
      'wmarble',
      'pure white',
      'pwhite',
      'pure pink',
      'ppink',
      'baby pink',
      'bpink',
      'neon pink',
      'neonp',
      'neon lemon',
      'neonl',
      'neon orange',
      'neono',
      'neon swirl',
      'neons',
      'dark bronze',
      'dbronze',
      'metal teal',
      'metalteal',
      'metal earth green',
      'metearthgreen',
      'star purple',
      'starpurple',
      'star frost',
      'starfrost',
      'aqua blue',
      'aquablue',
      'blue sky',
      'bluesky',
    ];

    for (const keyword of colorKeywords) {
      if (combinedText.includes(keyword)) {
        extracted.color = this.convertColorKeyword(keyword);
        break;
      }
    }

    // Check for customization keywords
    const customizationKeywords = this.checkCustomizationKeywords(combinedText);
    extracted.isCustom = customizationKeywords.isCustom;
    extracted.isEngrave = customizationKeywords.isEngrave;

    return extracted;
  }

  /**
   * Convert color keyword to proper color name
   */
  private convertColorKeyword(keyword: string): string {
    const colorMappings: Record<string, string> = {
      'rose gold': 'RoseGold',
      rosegold: 'RoseGold',
      rgold: 'RoseGold',
      'sky blue': 'SkyBlue',
      skyblue: 'SkyBlue',
      champagne: 'Champagne',
      champ: 'Champagne',
      'northern lights': 'NLights',
      nlights: 'NLights',
      nl: 'NLights',
      enchanted: 'Enchanted',
      ef: 'Enchanted',
      'dark silver': 'DSilver',
      dsilver: 'DSilver',
      'light gold': 'LGold',
      lgold: 'LGold',
      'light grey': 'LGrey',
      lgrey: 'LGrey',
      'baby blue': 'BabyBlue',
      bblue: 'BabyBlue',
      'black marble': 'BMarble',
      bmarble: 'BMarble',
      'white marble': 'WMarble',
      wmarble: 'WMarble',
      'pure white': 'PWhite',
      pwhite: 'PWhite',
      'pure pink': 'PPink',
      ppink: 'PPink',
      'baby pink': 'BPink',
      bpink: 'BPink',
      'neon pink': 'NeonPink',
      neonp: 'NeonPink',
      'neon lemon': 'NeonLemon',
      neonl: 'NeonLemon',
      'neon orange': 'NeonOrange',
      neono: 'NeonOrange',
      'neon swirl': 'NeonSwirl',
      neons: 'NeonSwirl',
      'dark bronze': 'DarkBronze',
      dbronze: 'DarkBronze',
      'metal teal': 'MetalTeal',
      metalteal: 'MetalTeal',
      'metal earth green': 'MetEarthGreen',
      metearthgreen: 'MetEarthGreen',
      'star purple': 'StarPurple',
      starpurple: 'StarPurple',
      'star frost': 'StarFrost',
      starfrost: 'StarFrost',
      'aqua blue': 'AquaBlue',
      aquablue: 'AquaBlue',
      'blue sky': 'BlueSky',
      bluesky: 'BlueSky',
    };

    return colorMappings[keyword] || keyword;
  }
}
