import { Injectable } from '@nestjs/common';
import { PlatformSource } from '@domain/product/platform-source.enum';
import {
  PlatformAttributeExtractor,
  ExtractedAttributes,
  PlatformExtractionData,
} from './platform-attribute-extractor.abstract';
import { ShopifyAttributeExtractor } from './platform-attribute-extractors/shopify-attribute-extractor.service';
import { EtsyAttributeExtractor } from './platform-attribute-extractors/etsy-attribute-extractor.service';
import { AmazonAttributeExtractor } from './platform-attribute-extractors/amazon-attribute-extractor.service';

@Injectable()
export class PlatformAttributeExtractionService {
  private readonly extractors: Map<PlatformSource, PlatformAttributeExtractor>;

  constructor(
    private readonly shopifyExtractor: ShopifyAttributeExtractor,
    private readonly etsyExtractor: EtsyAttributeExtractor,
    private readonly amazonExtractor: AmazonAttributeExtractor,
  ) {
    this.extractors = new Map<PlatformSource, PlatformAttributeExtractor>([
      [PlatformSource.SHOPIFY, this.shopifyExtractor],
      [PlatformSource.ETSY, this.etsyExtractor],
      [PlatformSource.AMAZON, this.amazonExtractor],
    ]);
  }

  /**
   * Extract attributes using platform-specific logic after SKU extraction
   */
  extractPlatformAttributes(
    platform: PlatformSource,
    skuExtracted: ExtractedAttributes,
    platformData: any,
    title?: string,
    sku?: string,
  ): ExtractedAttributes {
    const extractor = this.extractors.get(platform);

    if (!extractor) {
      console.warn(`No extractor found for platform: ${platform}`);
      return skuExtracted;
    }

    const extractionData: PlatformExtractionData = {
      skuExtracted,
      platformData,
      title,
      sku,
    };

    try {
      return extractor.extractPlatformAttributes(extractionData);
    } catch (error) {
      console.error(`Error extracting platform attributes for ${platform}:`, error);
      return skuExtracted;
    }
  }

  /**
   * Get extractor for specific platform
   */
  getExtractor(platform: PlatformSource): PlatformAttributeExtractor | undefined {
    return this.extractors.get(platform);
  }

  /**
   * Check if platform is supported
   */
  isPlatformSupported(platform: PlatformSource): boolean {
    return this.extractors.has(platform);
  }

  /**
   * Get all supported platforms
   */
  getSupportedPlatforms(): PlatformSource[] {
    return Array.from(this.extractors.keys());
  }
}
