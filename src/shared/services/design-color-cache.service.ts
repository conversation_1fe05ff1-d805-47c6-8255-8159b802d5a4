import { Injectable, Logger } from '@nestjs/common';

export interface DesignData {
  id: string;
  name: string;
  type: string;
  category?: string;
  description?: string;
  [key: string]: any;
}

export interface ColorData {
  id: string;
  name: string;
  hex?: string;
  rgb?: string;
  category?: string;
  [key: string]: any;
}

export interface InlayDesignData {
  id: string;
  name: string;
  type: 'gold' | 'silver' | 'clear';
  category?: string;
  [key: string]: any;
}

export interface RingSpecData {
  id: string;
  name: string;
  inside: {
    surfaceLength: number;
    surfaceWidth: number;
    designWidth: number;
    textWidth: number;
    power: number;
  };
  outside: {
    surfaceLength: number;
    surfaceWidth: number;
    designWidth: number;
    textWidth: number;
    power: number;
  };
  [key: string]: any;
}

export interface ConfigData {
  designs: DesignData[];
  colors: ColorData[];
  inlayDesigns: InlayDesignData[];
  inlayGoldDesigns: InlayDesignData[];
  inlaySilverDesigns: InlayDesignData[];
  ringSpecs: RingSpecData[];
  general: Record<string, any>;
  postageTypes: any[];
  lastUpdated: Date;
}

@Injectable()
export class DesignColorCacheService {
  private readonly logger = new Logger(DesignColorCacheService.name);

  // In-memory cache for simplified configuration
  private configCache: ConfigData | null = null;
  private lastUpdated: Date | null = null;
  private readonly CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

  constructor() {
    // Initialize with default configuration
    this.initializeDefaultConfig();
  }

  /**
   * Initialize default configuration data
   */
  private initializeDefaultConfig(): void {
    this.configCache = {
      designs: this.getDefaultDesigns(),
      colors: this.getDefaultColors(),
      inlayDesigns: [],
      inlayGoldDesigns: [],
      inlaySilverDesigns: [],
      ringSpecs: [],
      general: {},
      postageTypes: [],
      lastUpdated: new Date(),
    };
    this.lastUpdated = new Date();
    this.logger.log('Initialized default configuration cache');
  }

  /**
   * Cache configuration data in memory
   */
  async cacheConfigData(configData: ConfigData): Promise<void> {
    try {
      this.configCache = {
        ...configData,
        lastUpdated: new Date(),
      };
      this.lastUpdated = new Date();
      this.logger.log('Successfully cached configuration data in memory');
    } catch (error) {
      this.logger.error('Failed to cache config data:', error);
      throw error;
    }
  }

  /**
   * Get all cached config data
   */
  async getConfigData(): Promise<ConfigData | null> {
    if (this.isCacheExpired()) {
      this.initializeDefaultConfig();
    }
    return this.configCache;
  }

  /**
   * Get cached designs
   */
  async getDesigns(): Promise<DesignData[]> {
    const config = await this.getConfigData();
    return config?.designs || [];
  }

  /**
   * Get cached colors
   */
  async getColors(): Promise<ColorData[]> {
    const config = await this.getConfigData();
    return config?.colors || [];
  }

  /**
   * Get cached inlay designs
   */
  async getInlayDesigns(): Promise<InlayDesignData[]> {
    const config = await this.getConfigData();
    return config?.inlayDesigns || [];
  }

  /**
   * Get cached gold inlay designs
   */
  async getInlayGoldDesigns(): Promise<InlayDesignData[]> {
    const config = await this.getConfigData();
    return config?.inlayGoldDesigns || [];
  }

  /**
   * Get cached silver inlay designs
   */
  async getInlaySilverDesigns(): Promise<InlayDesignData[]> {
    const config = await this.getConfigData();
    return config?.inlaySilverDesigns || [];
  }

  /**
   * Get cached ring specifications
   */
  async getRingSpecs(): Promise<RingSpecData[]> {
    const config = await this.getConfigData();
    return config?.ringSpecs || [];
  }

  /**
   * Get cached general config
   */
  async getGeneralConfig(): Promise<Record<string, any>> {
    const config = await this.getConfigData();
    return config?.general || {};
  }

  /**
   * Get cached postage types
   */
  async getPostageTypes(): Promise<any[]> {
    const config = await this.getConfigData();
    return config?.postageTypes || [];
  }

  /**
   * Check if config data is cached and not expired
   */
  async isConfigCached(): Promise<boolean> {
    return this.configCache !== null && !this.isCacheExpired();
  }

  /**
   * Check if cache is expired
   */
  private isCacheExpired(): boolean {
    if (!this.lastUpdated) return true;
    const now = new Date().getTime();
    const cacheTime = this.lastUpdated.getTime();
    return now - cacheTime > this.CACHE_DURATION;
  }

  /**
   * Get last updated timestamp
   */
  async getLastUpdated(): Promise<Date | null> {
    return this.lastUpdated;
  }

  /**
   * Clear all cached config data
   */
  async clearConfigCache(): Promise<void> {
    this.configCache = null;
    this.lastUpdated = null;
    this.logger.log('Cleared configuration cache');
  }

  /**
   * Get default designs for initialization
   */
  private getDefaultDesigns(): DesignData[] {
    return [
      { id: 'celtic', name: 'Celtic', type: 'design', category: 'traditional' },
      { id: 'trinity', name: 'TrinityZ', type: 'design', category: 'traditional' },
      { id: 'forest', name: 'Forest', type: 'design', category: 'nature' },
      { id: 'waves', name: 'Waves', type: 'design', category: 'nature' },
      { id: 'mount', name: 'Mount', type: 'design', category: 'nature' },
      { id: 'lotus', name: 'Lotus', type: 'design', category: 'spiritual' },
      { id: 'om', name: 'Om', type: 'design', category: 'spiritual' },
      { id: 'fili', name: 'Fili', type: 'design', category: 'decorative' },
      { id: 'floral', name: 'Floral', type: 'design', category: 'decorative' },
    ];
  }

  /**
   * Get default colors for initialization
   */
  private getDefaultColors(): ColorData[] {
    return [
      { id: 'gold', name: 'Gold', category: 'metallic' },
      { id: 'silver', name: 'Silver', category: 'metallic' },
      { id: 'rosegold', name: 'RoseGold', category: 'metallic' },
      { id: 'black', name: 'Black', category: 'basic' },
      { id: 'white', name: 'White', category: 'basic' },
      { id: 'blue', name: 'Blue', category: 'basic' },
      { id: 'red', name: 'Red', category: 'basic' },
      { id: 'enchanted', name: 'Enchanted', category: 'special' },
      { id: 'skyblue', name: 'SkyBlue', category: 'special' },
      { id: 'champagne', name: 'Champagne', category: 'special' },
      { id: 'nlights', name: 'NLights', category: 'special' },
    ];
  }

  /**
   * Find design by name or ID
   */
  async findDesign(identifier: string): Promise<DesignData | null> {
    try {
      const designs = await this.getDesigns();
      return (
        designs.find(
          design =>
            design.id === identifier ||
            design.name?.toLowerCase() === identifier.toLowerCase() ||
            design.name?.toLowerCase().includes(identifier.toLowerCase()),
        ) || null
      );
    } catch (error) {
      this.logger.error(`Failed to find design ${identifier}:`, error);
      return null;
    }
  }

  /**
   * Find color by name or ID
   */
  async findColor(identifier: string): Promise<ColorData | null> {
    try {
      const colors = await this.getColors();
      return (
        colors.find(
          color =>
            color.id === identifier ||
            color.name?.toLowerCase() === identifier.toLowerCase() ||
            color.name?.toLowerCase().includes(identifier.toLowerCase()),
        ) || null
      );
    } catch (error) {
      this.logger.error(`Failed to find color ${identifier}:`, error);
      return null;
    }
  }

  /**
   * Find inlay design by name or ID
   */
  async findInlayDesign(
    identifier: string,
    type?: 'gold' | 'silver' | 'clear',
  ): Promise<InlayDesignData | null> {
    try {
      let designs: InlayDesignData[] = [];

      if (type === 'gold') {
        designs = await this.getInlayGoldDesigns();
      } else if (type === 'silver') {
        designs = await this.getInlaySilverDesigns();
      } else {
        designs = await this.getInlayDesigns();
      }

      return (
        designs.find(
          design =>
            design.id === identifier ||
            design.name?.toLowerCase() === identifier.toLowerCase() ||
            design.name?.toLowerCase().includes(identifier.toLowerCase()),
        ) || null
      );
    } catch (error) {
      this.logger.error(`Failed to find inlay design ${identifier}:`, error);
      return null;
    }
  }

  /**
   * Get all available design names for validation
   */
  async getDesignNames(): Promise<string[]> {
    try {
      const designs = await this.getDesigns();
      return designs.map(design => design.name).filter(Boolean);
    } catch (error) {
      this.logger.error('Failed to get design names:', error);
      return [];
    }
  }

  /**
   * Get all available color names for validation
   */
  async getColorNames(): Promise<string[]> {
    try {
      const colors = await this.getColors();
      return colors.map(color => color.name).filter(Boolean);
    } catch (error) {
      this.logger.error('Failed to get color names:', error);
      return [];
    }
  }

  /**
   * Validate if a design exists
   */
  async validateDesign(designName: string): Promise<boolean> {
    try {
      const design = await this.findDesign(designName);
      return design !== null;
    } catch (error) {
      this.logger.error(`Failed to validate design ${designName}:`, error);
      return false;
    }
  }

  /**
   * Validate if a color exists
   */
  async validateColor(colorName: string): Promise<boolean> {
    try {
      const color = await this.findColor(colorName);
      return color !== null;
    } catch (error) {
      this.logger.error(`Failed to validate color ${colorName}:`, error);
      return false;
    }
  }
}
