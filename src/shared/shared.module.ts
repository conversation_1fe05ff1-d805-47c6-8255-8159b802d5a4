import { Global, Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { OAuthToken } from '../domain/common/entities/oauth-token.entity';
import { OAuthTokenRepository } from '../infrastructure/database/repositories/oauth-token.repository';

import { DesignColorCacheService } from './services/design-color-cache.service';
import { SkuExtractionService } from './services/sku-extraction.service';
import { DataExtractionService } from './services/data-extraction.service';
import { SimpleTokenManagerService } from './services/simple-token-manager.service';
import { PlatformAttributeExtractionModule } from './services/platform-attribute-extraction.module';
import { PlatformAttributeExtractionService } from './services/platform-attribute-extraction.service';

@Global()
@Module({
  imports: [
    ConfigModule,
    TypeOrmModule.forFeature([OAuthToken]),
    PlatformAttributeExtractionModule,
  ],
  providers: [
    OAuthTokenRepository,
    DesignColorCacheService,
    SkuExtractionService,
    DataExtractionService,
    SimpleTokenManagerService,
  ],
  exports: [
    DesignColorCacheService,
    SkuExtractionService,
    DataExtractionService,
    SimpleTokenManagerService,
    PlatformAttributeExtractionModule,
  ],
})
export class SharedModule {}
