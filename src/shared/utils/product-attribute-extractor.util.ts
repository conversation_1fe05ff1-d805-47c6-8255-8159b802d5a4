import { SKU } from '@domain/product/value-objects/sku.vo';

export interface ProductAttributes {
  style?: string;
  color?: string;
  size?: number;
  design?: string;
  isCustom: boolean;
  isEngrave: boolean;
  customization?: string;
  engraving?: string;
}

export interface AttributeExtractionSource {
  title?: string;
  description?: string;
  sku?: string;
  variantTitle?: string;
  properties?: Array<{ name: string; value: string }>;
  variations?: Array<{ name: string; value: string }>;
  productData?: Array<{ property_name: string; values: string[] }>;
  metadata?: Record<string, any>;
}

export class ProductAttributeExtractor {
  /**
   * Extract product attributes from various platform data sources
   */
  static extractAttributes(source: AttributeExtractionSource): ProductAttributes {
    const result: ProductAttributes = {
      style: undefined,
      color: undefined,
      size: undefined,
      design: undefined,
      isCustom: false,
      isEngrave: false,
      customization: undefined,
      engraving: undefined,
    };

    // 1. Extract from properties (Shopify, Etsy)
    if (source.properties && Array.isArray(source.properties)) {
      this.extractFromProperties(source.properties, result);
    }

    // 2. Extract from variations (Etsy)
    if (source.variations && Array.isArray(source.variations)) {
      this.extractFromVariations(source.variations, result);
    }

    // 3. Extract from product_data (Etsy)
    if (source.productData && Array.isArray(source.productData)) {
      this.extractFromProductData(source.productData, result);
    }

    // 4. Extract from SKU as fallback
    if (source.sku && (!result.style || !result.color || !result.size)) {
      this.extractFromSku(source.sku, result);
    }

    // 5. Extract from title and description
    if (source.title || source.description) {
      this.extractFromText(source.title || '', source.description || '', result);
    }

    // 6. Check for customization and engraving
    this.detectCustomization(source, result);

    return result;
  }

  /**
   * Extract attributes from properties array
   */
  private static extractFromProperties(
    properties: Array<{ name: string; value: string }>,
    result: ProductAttributes,
  ): void {
    for (const prop of properties) {
      const name = prop.name?.toLowerCase() || '';
      const value = prop.value || '';

      this.mapPropertyToAttribute(name, value, result);
    }
  }

  /**
   * Extract attributes from variations array
   */
  private static extractFromVariations(
    variations: Array<{ name: string; value: string }>,
    result: ProductAttributes,
  ): void {
    for (const variation of variations) {
      const name = variation.name?.toLowerCase() || '';
      const value = variation.value || '';

      this.mapPropertyToAttribute(name, value, result);
    }
  }

  /**
   * Extract attributes from product_data array
   */
  private static extractFromProductData(
    productData: Array<{ property_name: string; values: string[] }>,
    result: ProductAttributes,
  ): void {
    for (const data of productData) {
      const propertyName = data.property_name?.toLowerCase() || '';
      const values = data.values || [];
      const value = values[0] || '';

      this.mapPropertyToAttribute(propertyName, value, result);
    }
  }

  /**
   * Map property name to attribute
   */
  private static mapPropertyToAttribute(
    propertyName: string,
    value: string,
    result: ProductAttributes,
  ): void {
    if (
      propertyName.includes('color') ||
      propertyName.includes('colour') ||
      propertyName === 'primary color'
    ) {
      result.color = result.color || value;
    } else if (propertyName.includes('size') || propertyName === 'custom property') {
      // For custom property, check if it contains size info
      const sizeMatch = value.match(/(\d+)/);
      if (sizeMatch) {
        result.size = result.size || parseInt(sizeMatch[1], 10);
      }
    } else if (propertyName.includes('style') || propertyName.includes('type')) {
      result.style = result.style || value;
    } else if (propertyName.includes('design') || propertyName.includes('pattern')) {
      result.design = result.design || value;
    } else if (propertyName.includes('custom') || propertyName.includes('personalization')) {
      result.customization = result.customization || value;
      result.isCustom = true;
    } else if (propertyName.includes('engrave') || propertyName.includes('engraving')) {
      result.engraving = result.engraving || value;
      result.isEngrave = true;
    }
  }

  /**
   * Extract attributes from SKU
   */
  private static extractFromSku(sku: string, result: ProductAttributes): void {
    try {
      const skuVO = new SKU(sku);
      const parts = skuVO.extractParts();

      if (parts.style && !result.style) {
        result.style = parts.style;
      }
      if (parts.color && !result.color) {
        result.color = parts.color;
      }
      if (parts.size && !result.size) {
        result.size = parts.size;
      }
      if (parts.design && !result.design) {
        result.design = parts.design;
      }
    } catch (error) {
      // Fallback to manual parsing if SKU VO fails
      const skuParts = sku.split('-');

      if (skuParts.length >= 3) {
        // ComfortFit-6mm-Silver-12 format
        if (!result.style && skuParts[0]) {
          result.style = skuParts[0];
        }
        if (!result.color && skuParts[2]) {
          result.color = skuParts[2];
        }
        if (!result.size && skuParts[3]) {
          const sizeMatch = skuParts[3].match(/(\d+)/);
          if (sizeMatch) {
            result.size = parseInt(sizeMatch[1], 10);
          }
        }
      }
    }
  }

  /**
   * Extract attributes from title and description text
   */
  private static extractFromText(
    title: string,
    description: string,
    result: ProductAttributes,
  ): void {
    const text = `${title} ${description}`.toLowerCase();

    // Color extraction
    if (!result.color) {
      const colorPatterns = [
        /\b(silver|gold|rose gold|platinum|white gold|black|blue|red|green|yellow|purple|pink|orange|brown|gray|grey)\b/g,
        /\b(sterling|14k|18k|24k|925|750|585)\b/g,
      ];

      for (const pattern of colorPatterns) {
        const match = text.match(pattern);
        if (match) {
          result.color = match[0];
          break;
        }
      }
    }

    // Size extraction
    if (!result.size) {
      const sizePatterns = [
        /\b(\d+)\s*(mm|cm|inch|inches|size|ring size)\b/g,
        /\bsize\s*(\d+)\b/g,
        /\b(\d+)\s*bandwidth\b/g,
      ];

      for (const pattern of sizePatterns) {
        const match = text.match(pattern);
        if (match) {
          result.size = parseInt(match[1], 10);
          break;
        }
      }
    }

    // Style extraction
    if (!result.style) {
      const stylePatterns = [
        /\b(comfort fit|classic|vintage|modern|contemporary|traditional|minimalist|art deco|victorian|edwardian)\b/g,
        /\b(ring|band|pendant|earring|bracelet|necklace|chain)\b/g,
      ];

      for (const pattern of stylePatterns) {
        const match = text.match(pattern);
        if (match) {
          result.style = match[0];
          break;
        }
      }
    }

    // Design extraction
    if (!result.design) {
      const designPatterns = [
        /\b(solitaire|halo|three stone|eternity|anniversary|wedding|engagement|promise)\b/g,
        /\b(round|oval|princess|emerald|pear|marquise|heart|cushion|radiant)\b/g,
        /\b(plain|engraved|etched|carved|milled|textured|smooth|polished)\b/g,
      ];

      for (const pattern of designPatterns) {
        const match = text.match(pattern);
        if (match) {
          result.design = match[0];
          break;
        }
      }
    }
  }

  /**
   * Detect customization and engraving
   */
  private static detectCustomization(
    source: AttributeExtractionSource,
    result: ProductAttributes,
  ): void {
    const title = source.title?.toLowerCase() || '';
    const description = source.description?.toLowerCase() || '';
    const text = `${title} ${description}`;

    // Check for customization keywords
    const customKeywords = [
      'custom',
      'personalized',
      'personalization',
      'engraved',
      'engraving',
      'etched',
      'carved',
      'monogram',
      'initial',
      'name',
      'text',
      'message',
    ];

    for (const keyword of customKeywords) {
      if (text.includes(keyword)) {
        if (keyword.includes('engrave') || keyword.includes('etch') || keyword.includes('carve')) {
          result.isEngrave = true;
          if (!result.engraving) {
            result.engraving = 'Custom engraving';
          }
        } else {
          result.isCustom = true;
          if (!result.customization) {
            result.customization = 'Custom item';
          }
        }
      }
    }

    // Check properties for customization
    if (source.properties) {
      for (const prop of source.properties) {
        const name = prop.name?.toLowerCase() || '';
        const value = prop.value || '';

        if (
          name.includes('custom') ||
          name.includes('personal') ||
          name.includes('engrave') ||
          name.includes('message') ||
          name.includes('text')
        ) {
          result.isCustom = true;
          result.customization = result.customization || value;
        }

        if (name.includes('engrave') || name.includes('etch')) {
          result.isEngrave = true;
          result.engraving = result.engraving || value;
        }
      }
    }
  }
}
