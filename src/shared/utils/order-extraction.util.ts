import { decode } from 'html-entities';
import { capitalize, first, last } from 'lodash';
import moment from 'moment-timezone';
import { ProductAttributeExtractor } from './product-attribute-extractor.util';
import { DesignColorCacheService } from '../services/design-color-cache.service';

export interface OrderExtractionResult {
  itemId: string;
  orderId: string;
  createdTimeLocal: string;
  orderItemCount: number;
  orderItemIndex: number;
  orderItemNumberOfTotal: string;
  isShipped: boolean;
  message: string;
  isMixedOrder: boolean;
  isCancelled: boolean;
  orderValue: number;
  orderCurrency: string;
  name: string;
  shippingShortName: string;
  email: string;
  chitchatsAddress: {
    name: string;
    address_1: string;
    address_2: string;
    city: string;
    country_code: string;
    phone: string;
    province_code: string;
    postal_code: string;
  };
  title: string;
  quantity: number;
  isEngraved: boolean;
  sku: string;
  style: string;
  color: string;
  size: string;
  design: string;
  inkColor?: string;
  customFields: Array<{ label: string; value: any }>;
  personalization?: string;
  outside: Record<string, any>;
  inside: Record<string, any>;
  targetFileName: string;
  source: 'etsy' | 'shopify';
  rawData: any;
}

export interface EtsyExtractionSource {
  receipt: any;
  transaction: any;
  index: number;
  timeZone?: string;
}

export interface ShopifyExtractionSource {
  order: any;
  lineItem: any;
  index: number;
  timeZone?: string;
}

export class OrderExtractionUtil {
  private static readonly DEFAULT_TIMEZONE = 'America/Toronto';

  /**
   * Extract order data from Etsy receipt and transaction
   */
  static async extractFromEtsy(
    source: EtsyExtractionSource,
    designColorCacheService: DesignColorCacheService,
  ): Promise<OrderExtractionResult> {
    const { receipt, transaction, index, timeZone = this.DEFAULT_TIMEZONE } = source;

    const {
      receipt_id,
      created_timestamp,
      is_shipped: isShipped,
      buyer_email,
      message_from_buyer,
      transactions,
      name,
      subtotal,
      first_line,
      second_line,
      city,
      country_iso,
      state,
      zip,
    } = receipt;

    const orderValue = subtotal?.amount ? subtotal.amount / subtotal.divisor : 0;

    const isMixedOrder =
      transactions.some((t: any) => t.title?.toLowerCase().includes('engraved')) &&
      transactions.some((t: any) => !t.title?.toLowerCase().includes('engraved'));

    const itemId = `E${receipt_id}.${index}`;
    const orderId = `E${receipt_id}`;
    const createdTimeLocal = moment(created_timestamp * 1000)
      .tz(timeZone)
      .format('YYYY-MM-DD hh:mm');
    const message = decode(message_from_buyer || '');

    const { title: nullableTitle, quantity, sku, variations = [] } = transaction;

    if (!sku) {
      throw new Error('SKU is required for Etsy transaction');
    }

    const title = nullableTitle ?? '';

    // Extract product attributes using the shared extractor
    const productAttributes = ProductAttributeExtractor.extractAttributes({
      title,
      description: transaction.description,
      sku,
      properties: variations.map((v: any) => ({
        name: v.formatted_name || '',
        value: v.formatted_value || '',
      })),
      variations: variations.map((v: any) => ({
        name: v.formatted_name || '',
        value: v.formatted_value || '',
      })),
      productData: transaction.product_data,
      metadata: {
        listingId: transaction.listing_id,
        transactionId: transaction.transaction_id,
      },
    });

    // Extract additional Etsy-specific data
    const { engravingSide, personalization, customFields } =
      this.extractEtsyCustomFields(variations);

    // Generate customer short name and target filename
    const shippingNameParts = name.trim().split(' ');
    const shippingShortName = `${capitalize(first(shippingNameParts) as string)}${
      capitalize(last(shippingNameParts) as string)[0]
    }`;
    const orderItemCount = transactions.length;
    const orderItemIndex = index;
    const targetFileName = `${orderId}-${shippingShortName}${
      orderItemCount > 1 ? `-${orderItemIndex + 1}of${orderItemCount}` : ''
    }`;

    // Build shipping address
    const chitchatsAddress = {
      name,
      address_1: first_line,
      address_2: second_line ?? '',
      city,
      country_code: country_iso,
      phone: '', // Etsy doesn't provide phone
      province_code: state ?? '',
      postal_code: zip,
    };

    // Process design and engraving
    const { outside, inside, isEngraved } = this.processDesignAndEngraving(
      productAttributes.design,
      engravingSide,
      personalization,
      designColorCacheService,
    );

    // Extract ink color
    const inkColor = this.extractInkColor(variations, productAttributes);

    return {
      itemId,
      orderId,
      createdTimeLocal,
      orderItemCount,
      orderItemIndex,
      orderItemNumberOfTotal: orderItemCount > 1 ? `${orderItemIndex + 1}/${orderItemCount}` : '',
      isShipped: !!isShipped,
      message,
      isMixedOrder,
      isCancelled: false, // Etsy API v3 ignores cancelled orders
      orderValue,
      orderCurrency: subtotal?.currency_code || 'USD',
      name,
      shippingShortName,
      email: buyer_email ?? '',
      chitchatsAddress,
      title,
      quantity: quantity || 1,
      isEngraved,
      sku,
      style: productAttributes.style || '',
      color: productAttributes.color || '',
      size: this.formatSize(productAttributes.size),
      design: productAttributes.design || '',
      inkColor,
      customFields,
      personalization,
      outside,
      inside,
      targetFileName,
      source: 'etsy',
      rawData: {
        receipt,
        transaction,
        syncedAt: new Date().toISOString(),
      },
    };
  }

  /**
   * Extract order data from Shopify order and line item
   */
  static async extractFromShopify(
    source: ShopifyExtractionSource,
    designColorCacheService: DesignColorCacheService,
  ): Promise<OrderExtractionResult> {
    const { order, lineItem, index, timeZone = this.DEFAULT_TIMEZONE } = source;

    const {
      line_items,
      created_at,
      shipping_address,
      customer,
      closed_at,
      id,
      name,
      current_subtotal_price: orderValue,
      currency: orderCurrency,
      cancelled_at: cancelledAt,
    } = order;

    if (!shipping_address) {
      throw new Error('Shipping address is required for Shopify order');
    }

    const { first_name, last_name } = shipping_address;
    const isMixedOrder =
      line_items.some((i: any) => i.title.toLowerCase().includes('engraved')) &&
      line_items.some((i: any) => !i.title.toLowerCase().includes('engraved'));

    const isCancelled = !!cancelledAt;
    const orderId = name.replace(/^#/, '');
    const itemId = `${orderId}.${index}`;
    const createdTimeLocal = moment(created_at).tz(timeZone).format('YYYY-MM-DD hh:mm');
    const message = decode(lineItem.note || '');

    const { quantity, sku, properties = [], title } = lineItem;

    // Extract product attributes using the shared extractor
    const productAttributes = ProductAttributeExtractor.extractAttributes({
      title,
      description: lineItem.variant_title,
      sku,
      properties: properties.map((prop: any) => ({
        name: prop.name,
        value: prop.value,
      })),
      metadata: {
        vendor: lineItem.vendor,
        productId: lineItem.product_id,
        variantId: lineItem.variant_id,
      },
    });

    // Extract Shopify-specific custom fields
    const { personalization, customFields } = this.extractShopifyCustomFields(properties);

    // Generate customer short name and target filename
    const shippingShortName = `${capitalize(first_name)}${capitalize(last_name)[0]}`;
    const orderItemCount = line_items.length;
    const orderItemIndex = index;
    const targetFileName = `${orderId}-${shippingShortName}${
      orderItemCount > 1 ? `-${orderItemIndex + 1}of${orderItemCount}` : ''
    }`;

    // Build shipping address
    const chitchatsAddress = {
      name: shipping_address.name,
      address_1: shipping_address.address1,
      address_2: shipping_address.address2 || '',
      city: shipping_address.city,
      country_code: shipping_address.country_code,
      phone: shipping_address.phone || '',
      province_code: shipping_address.province_code,
      postal_code: shipping_address.zip,
    };

    // Process design and engraving
    const { outside, inside, isEngraved } = this.processDesignAndEngraving(
      productAttributes.design,
      undefined, // Shopify doesn't have engraving side concept
      personalization,
      designColorCacheService,
    );

    // Extract ink color from properties
    const inkColor = this.extractInkColorFromProperties(properties, productAttributes);

    return {
      itemId,
      orderId,
      createdTimeLocal,
      orderItemCount,
      orderItemIndex,
      orderItemNumberOfTotal: orderItemCount > 1 ? `${orderItemIndex + 1}/${orderItemCount}` : '',
      isShipped: !!closed_at,
      message,
      isMixedOrder,
      isCancelled,
      orderValue: parseFloat(orderValue || '0'),
      orderCurrency: orderCurrency || 'USD',
      name: `${first_name} ${last_name}`,
      shippingShortName,
      email: customer?.email || order.email || '',
      chitchatsAddress,
      title,
      quantity: quantity || 0,
      isEngraved,
      sku,
      style: productAttributes.style || '',
      color: productAttributes.color || '',
      size: this.formatSize(productAttributes.size),
      design: productAttributes.design || '',
      inkColor,
      customFields,
      personalization,
      outside,
      inside,
      targetFileName,
      source: 'shopify',
      rawData: {
        order,
        lineItem,
        syncedAt: new Date().toISOString(),
      },
    };
  }

  /**
   * Extract custom fields from Etsy variations
   */
  private static extractEtsyCustomFields(variations: any[]): {
    engravingSide?: string;
    personalization?: string;
    customFields: Array<{ label: string; value: any }>;
  } {
    let engravingSide: string | undefined;
    let personalization: string | undefined;
    const customFields: Array<{ label: string; value: any }> = [];

    variations.forEach(({ property_id, formatted_name, formatted_value }) => {
      const decodedName = decode(formatted_name || '');
      const decodedValue = decode(formatted_value || '');

      if (property_id === 54) {
        // 54 = personalization
        personalization = decodedValue;
        if (personalization === 'Not requested on this item.') {
          personalization = undefined;
        }
      }
      if (property_id === 513) {
        // 513 = engraving side
        engravingSide = decodedValue;
      }
      if (property_id === 514 && formatted_value === 'Yes') {
        // 514 = Inside Engraving?
        engravingSide = 'inside';
      }

      customFields.push({
        label: decodedName,
        value: decodedValue,
      });
    });

    return { engravingSide, personalization, customFields };
  }

  /**
   * Extract custom fields from Shopify properties
   */
  private static extractShopifyCustomFields(properties: any[]): {
    personalization?: string;
    customFields: Array<{ label: string; value: any }>;
  } {
    let personalization: string | undefined;
    const customFields: Array<{ label: string; value: any }> = [];

    properties.forEach(({ name, value }) => {
      if (name[0] === '_') return; // Skip Shopify internal properties

      if (name === 'Engraving text') {
        personalization = value;
      }

      customFields.push({
        label: name,
        value,
      });
    });

    return { personalization, customFields };
  }

  /**
   * Process design and engraving information
   */
  private static processDesignAndEngraving(
    design: string | undefined,
    engravingSide: string | undefined,
    personalization: string | undefined,
    designColorCacheService: DesignColorCacheService,
  ): {
    outside: Record<string, any>;
    inside: Record<string, any>;
    isEngraved: boolean;
  } {
    let outside: Record<string, any> = {};
    let inside: Record<string, any> = {};

    if (design) {
      // Strip design prefixes (cs, gs, ss) for the outside object
      const cleanDesign = design.replace(/^(cs|gs|ss)/, '');
      outside = { design: cleanDesign };
    }

    if (engravingSide) {
      const engravingSideLowercase = engravingSide.toLowerCase();

      if (
        engravingSideLowercase.includes('inside') &&
        !engravingSideLowercase.includes('outside')
      ) {
        inside = { text: personalization };
      } else if (
        engravingSideLowercase.includes('outside') &&
        !engravingSideLowercase.includes('inside')
      ) {
        outside = { ...outside, text: personalization };
      } else if (
        engravingSideLowercase.includes('outside') &&
        engravingSideLowercase.includes('inside')
      ) {
        outside = { ...outside, text: '?' };
        inside = { text: '?' };
      }
    }

    const isEngraved =
      (typeof outside === 'object' && Object.keys(outside).length > 0) ||
      (typeof inside === 'object' && Object.keys(inside).length > 0) ||
      !!personalization;

    return { outside, inside, isEngraved };
  }

  /**
   * Extract ink color from Etsy variations
   */
  private static extractInkColor(variations: any[], productAttributes: any): string | undefined {
    // Check if there's an ink-color property in the variations
    const inkColorProperty = variations.find(
      v =>
        v.formatted_name?.toLowerCase().includes('ink-color') ||
        v.formatted_name?.toLowerCase().includes('ink color'),
    );

    if (inkColorProperty?.formatted_value) {
      return inkColorProperty.formatted_value.toLowerCase();
    }

    // Fallback to extracted inlay type
    return productAttributes.inlayType;
  }

  /**
   * Extract ink color from Shopify properties
   */
  private static extractInkColorFromProperties(
    properties: any[],
    productAttributes: any,
  ): string | undefined {
    const inkColorProperty = properties.find(prop => prop.name === 'ink-color');

    if (inkColorProperty?.value) {
      return inkColorProperty.value.toLowerCase();
    }

    // Fallback to extracted inlay type
    return productAttributes.inlayType;
  }

  /**
   * Format size to standard format (e.g., 8 -> S08)
   */
  private static formatSize(size: number | undefined): string {
    if (!size) return '';

    if (typeof size === 'number') {
      const sizeNum = size.toString().padStart(2, '0');
      return `S${sizeNum}`;
    }

    return String(size);
  }
}
