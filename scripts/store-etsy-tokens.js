const axios = require('axios');

// Fresh Etsy tokens
const tokens = {
  accessToken: '13208796.iwcouWrnYlOIxduKrLohAFgFvm6cjkacRQm2QyqxgllA8HygWe3Rr_h6_mch5DLQjw3EeAYHcI0ucQSBLvIckCC1Vrp',
  refreshToken: '13208796.mBj8aG_josUWhI3rKsBnN_OSK-zsakJEfKGG6ryTbGwQDjuFiuKBUy1bSkO2dXUSa-gDWEaAP7CRBCPDOyJ6bkp1r0x',
  shopId: '6507168',
  scopes: 'shops_r transactions_r'
};

async function storeTokens() {
  try {
    console.log('🔑 Storing fresh Etsy tokens...');
    
    // Try to store tokens via API (this will require authentication)
    const response = await axios.post('http://localhost:3002/api/v1/platform/tokens/etsy', tokens, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Tokens stored successfully:', response.data);
    
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('❌ Authentication required for API endpoint');
      console.log('💡 The tokens should be migrated automatically from .env file');
      console.log('🔄 Let\'s try to trigger a sync to force token migration...');
      
      // Try to trigger sync which should force token migration
      try {
        const syncResponse = await axios.post(
          'http://localhost:3002/api/v1/platform/sync/orders/etsy?startDate=2025-08-01T00:00:00Z&endDate=2025-08-06T23:59:59Z&limit=1',
          {},
          { headers: { 'Content-Type': 'application/json' } }
        );
        
        console.log('📊 Sync response:', syncResponse.data);
        
        if (syncResponse.data.data?.errors?.includes('Unknown error')) {
          console.log('⚠️  Still getting token errors. Manual intervention needed.');
        }
        
      } catch (syncError) {
        console.log('❌ Sync failed:', syncError.response?.data || syncError.message);
      }
      
    } else {
      console.error('❌ Error storing tokens:', error.response?.data || error.message);
    }
  }
}

async function testTokenRefresh() {
  console.log('\n🧪 Testing automatic token refresh mechanism...');
  console.log('=================================================');
  
  // The system should automatically:
  // 1. Detect expired tokens
  // 2. Use refresh token to get new access token
  // 3. Store new tokens securely
  // 4. Continue with API calls
  
  console.log('✅ Refresh function registered for Etsy');
  console.log('✅ TokenManagerService.getValidAccessToken() should:');
  console.log('   - Check if token exists');
  console.log('   - Check if token needs refresh (expires within 5 minutes)');
  console.log('   - Call performTokenRefresh() if needed');
  console.log('   - Return valid access token');
  
  console.log('\n🔄 Expected refresh flow:');
  console.log('1. API call fails with 401 (token expired)');
  console.log('2. TokenManager detects token needs refresh');
  console.log('3. Calls EtsyApiService.performTokenRefresh()');
  console.log('4. Gets new access_token + refresh_token');
  console.log('5. Encrypts and stores new tokens');
  console.log('6. Retries original API call');
  
  console.log('\n❓ Current issue: No tokens in database to refresh');
  console.log('💡 Solution: Ensure tokens are properly migrated from .env');
}

// Run the functions
storeTokens().then(() => {
  testTokenRefresh();
}).catch(console.error);
