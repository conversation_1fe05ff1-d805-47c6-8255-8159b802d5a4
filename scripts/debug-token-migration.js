const { Pool } = require('pg');
const crypto = require('crypto');

// Database connection
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'knot_core',
  password: 'postgres',
  port: 5433,
});

// Encryption configuration (same as EncryptionService)
const ENCRYPTION_KEY = Buffer.from('a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456', 'hex');
const ALGORITHM = 'aes-256-gcm';
const IV_LENGTH = 16;
const TAG_LENGTH = 16;

function encrypt(plaintext) {
  const iv = crypto.randomBytes(IV_LENGTH);
  const cipher = crypto.createCipheriv(ALGORITHM, ENCRYPTION_KEY, iv);
  cipher.setAAD(Buffer.from('oauth-token', 'utf8'));

  let encrypted = cipher.update(plaintext, 'utf8', 'hex');
  encrypted += cipher.final('hex');

  const tag = cipher.getAuthTag();

  // Combine iv + tag + encrypted data
  const combined = iv.toString('hex') + tag.toString('hex') + encrypted;
  return combined;
}

async function storeEtsyTokens() {
  try {
    console.log('🔐 Manually storing Etsy tokens with proper encryption...');
    
    // Fresh tokens
    const accessToken = '13208796.iwcouWrnYlOIxduKrLohAFgFvm6cjkacRQm2QyqxgllA8HygWe3Rr_h6_mch5DLQjw3EeAYHcI0ucQSBLvIckCC1Vrp';
    const refreshToken = '13208796.mBj8aG_josUWhI3rKsBnN_OSK-zsakJEfKGG6ryTbGwQDjuFiuKBUy1bSkO2dXUSa-gDWEaAP7CRBCPDOyJ6bkp1r0x';
    const shopId = '6507168';
    
    // Encrypt tokens
    const encryptedAccessToken = encrypt(accessToken);
    const encryptedRefreshToken = encrypt(refreshToken);
    
    console.log('✅ Tokens encrypted successfully');
    
    // Delete existing tokens
    await pool.query("DELETE FROM oauth_tokens WHERE provider = 'etsy'");
    console.log('🗑️  Cleared existing Etsy tokens');
    
    // Insert new encrypted tokens
    const insertQuery = `
      INSERT INTO oauth_tokens (
        provider, 
        status, 
        "encryptedAccessToken", 
        "encryptedRefreshToken", 
        "expiresAt", 
        scopes, 
        "shopId", 
        "lastUsedAt", 
        "lastRefreshedAt", 
        "refreshFailureCount",
        version,
        metadata
      ) VALUES (
        'etsy', 
        'active', 
        $1, 
        $2, 
        NOW() + INTERVAL '1 hour', 
        'shops_r transactions_r', 
        $3, 
        NOW(), 
        NOW(), 
        0,
        1,
        $4
      ) RETURNING id, provider, "shopId", status, "expiresAt"
    `;
    
    const metadata = {
      migratedFromScript: true,
      migratedAt: new Date().toISOString(),
      tokenSource: 'manual_script'
    };
    
    const result = await pool.query(insertQuery, [
      encryptedAccessToken,
      encryptedRefreshToken,
      shopId,
      JSON.stringify(metadata)
    ]);
    
    console.log('✅ Successfully stored encrypted Etsy tokens:');
    console.log('   ID:', result.rows[0].id);
    console.log('   Provider:', result.rows[0].provider);
    console.log('   Shop ID:', result.rows[0].shopId);
    console.log('   Status:', result.rows[0].status);
    console.log('   Expires At:', result.rows[0].expiresAt);
    
    // Verify the tokens are stored
    const verifyQuery = `
      SELECT provider, "shopId", status, "expiresAt", "lastRefreshedAt", metadata
      FROM oauth_tokens 
      WHERE provider = 'etsy'
    `;
    
    const verifyResult = await pool.query(verifyQuery);
    console.log('\n📊 Verification:');
    console.log('   Tokens in database:', verifyResult.rows.length);
    
    if (verifyResult.rows.length > 0) {
      const token = verifyResult.rows[0];
      console.log('   ✅ Token found for shop:', token.shopId);
      console.log('   ✅ Status:', token.status);
      console.log('   ✅ Expires:', token.expiresAt);
      console.log('   ✅ Metadata:', token.metadata);
    }
    
    console.log('\n🎯 Next steps:');
    console.log('1. Restart the application to pick up the new tokens');
    console.log('2. Try the Etsy sync again');
    console.log('3. The TokenManagerService should now find and use these tokens');
    console.log('4. Automatic refresh should work when tokens expire');
    
  } catch (error) {
    console.error('❌ Error storing tokens:', error);
  } finally {
    await pool.end();
  }
}

// Run the function
storeEtsyTokens();
