-- <PERSON><PERSON><PERSON> to manually insert the Etsy order with extracted attributes
-- This demonstrates how the improved extraction logic works

-- First, insert the order
INSERT INTO orders (
    id,
    "externalOrderId",
    "orderNumber",
    status,
    source,
    "externalCreatedAt",
    "customerEmail",
    "customerName",
    "customerPhone",
    "shippingAddressLine1",
    "shippingAddressLine2",
    "shippingCity",
    "shippingState",
    "shippingPostalCode",
    "shippingCountry",
    "subtotalPrice",
    "shippingPrice",
    "taxAmount",
    "discountAmount",
    "totalPrice",
    currency,
    "customerNote",
    "isProcessed",
    metadata,
    version
) VALUES (
    '10362854-e59a-4122-bae3-0e050180f4d1',
    '3756701380',
    '3756701380',
    'confirmed',
    'etsy',
    '2025-01-15 12:45:33',
    '',
    'Ankit Bhat',
    '',
    'B2-303 Ganga Orchard',
    'Pingle Vasti, Mundhwa',
    'Pune',
    'MH',
    '411036',
    'IN',
    17.99,
    9.99,
    0.00,
    2.00,
    27.98,
    'USD',
    '',
    false,
    '{
        "etsyData": {
            "orderId": null,
            "paymentMethod": "cc",
            "wasPaid": true,
            "needsGiftWrap": false,
            "giftMessage": ""
        }
    }',
    1
) ON CONFLICT (id) DO UPDATE SET
    "customerName" = EXCLUDED."customerName",
    "shippingAddressLine1" = EXCLUDED."shippingAddressLine1",
    "shippingAddressLine2" = EXCLUDED."shippingAddressLine2",
    "shippingCity" = EXCLUDED."shippingCity",
    "shippingState" = EXCLUDED."shippingState",
    "shippingPostalCode" = EXCLUDED."shippingPostalCode",
    "shippingCountry" = EXCLUDED."shippingCountry";

-- Now insert the order item with extracted attributes
INSERT INTO order_items (
    id,
    "orderId",
    sku,
    title,
    style,
    color,
    design,
    size,
    quantity,
    "unitPrice",
    "totalPrice",
    currency,
    "externalProductId",
    "externalVariantId",
    customization,
    engraving,
    "imageUrl",
    note,
    "isCustom",
    "isEngrave",
    metadata,
    version
) VALUES (
    gen_random_uuid(),
    '10362854-e59a-4122-bae3-0e050180f4d1',
    'ComfortFit-6mm-Silver-12',
    'Silver Silicone Ring for Him and Her - Breathable Comfort Fit Rubber Wedding Band - 4mm or 6mm His and Hers',
    'ComfortFit',  -- Extracted from SKU
    'Silver',      -- Extracted from variations and product_data
    null,
    12,            -- Extracted from variations "12 (6mm bandwidth)"
    1,
    19.99,
    19.99,
    'USD',
    '654011290',
    '15214655280',
    null,
    null,
    null,
    null,
    false,
    false,
    '{
        "etsyData": {
            "transactionId": 4679726198,
            "isDigital": false,
            "variations": [
                {
                    "property_id": 513,
                    "value_id": 1361211781115,
                    "formatted_name": "Size",
                    "formatted_value": "12 (6mm bandwidth)"
                },
                {
                    "property_id": 200,
                    "value_id": 55501864175,
                    "formatted_name": "Color",
                    "formatted_value": "Silver"
                }
            ],
            "productData": [
                {
                    "property_id": 513,
                    "property_name": "Custom Property",
                    "scale_id": null,
                    "scale_name": null,
                    "value_ids": [1361211781115],
                    "values": ["12 (6mm bandwidth)"]
                },
                {
                    "property_id": 200,
                    "property_name": "Primary color",
                    "scale_id": null,
                    "scale_name": null,
                    "value_ids": [55501864175],
                    "values": ["Silver"]
                }
            ]
        }
    }',
    1
) ON CONFLICT (id) DO UPDATE SET
    style = EXCLUDED.style,
    color = EXCLUDED.color,
    size = EXCLUDED.size,
    metadata = EXCLUDED.metadata;

-- Display the results
SELECT 
    'Order Information' as section,
    o.id,
    o."externalOrderId",
    o."customerName",
    o."shippingCity",
    o."shippingCountry",
    o."totalPrice",
    o.currency
FROM orders o 
WHERE o.id = '10362854-e59a-4122-bae3-0e050180f4d1';

SELECT 
    'Order Item with Extracted Attributes' as section,
    oi.sku,
    oi.title,
    oi.style,
    oi.color,
    oi.size,
    oi.design,
    oi."isCustom",
    oi."isEngrave",
    oi.quantity,
    oi."unitPrice"
FROM order_items oi 
WHERE oi."orderId" = '10362854-e59a-4122-bae3-0e050180f4d1';

-- Show extraction success
SELECT 
    'Extraction Summary' as section,
    CASE 
        WHEN oi.style IS NOT NULL AND oi.color IS NOT NULL AND oi.size IS NOT NULL 
        THEN '✅ SUCCESS: All attributes extracted'
        ELSE '❌ FAILED: Missing attributes'
    END as extraction_status,
    CONCAT(
        'Style: ', COALESCE(oi.style, 'NULL'), 
        ' | Color: ', COALESCE(oi.color, 'NULL'), 
        ' | Size: ', COALESCE(oi.size::text, 'NULL')
    ) as extracted_data
FROM order_items oi 
WHERE oi."orderId" = '10362854-e59a-4122-bae3-0e050180f4d1';
