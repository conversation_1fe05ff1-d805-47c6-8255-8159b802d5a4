#!/bin/bash

# Development startup script for knot-core-refactored

set -e

echo "🚀 Starting knot-core-refactored development environment..."

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️  .env file not found. Please create one based on .env.example"
    echo "📝 Make sure to add your Shopify and Etsy API credentials"
    exit 1
fi

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

echo "🔧 Building and starting services..."

# Build and start all services
docker-compose up --build -d

echo "⏳ Waiting for services to be ready..."

# Wait for PostgreSQL to be ready
echo "🐘 Waiting for PostgreSQL..."
until docker-compose exec -T postgres pg_isready -U postgres -d knot_core; do
    sleep 2
done

# Wait for Redis to be ready
echo "🔴 Waiting for Redis..."
until docker-compose exec -T redis redis-cli ping; do
    sleep 2
done

echo "✅ All services are ready!"

echo ""
echo "🌐 Application URLs:"
echo "   • Main App:        http://localhost:3000"
echo "   • API Docs:        http://localhost:3000/api"
echo "   • PgAdmin:         http://localhost:8080"
echo "   • Redis Commander: http://localhost:8081"
echo ""

echo "📊 Service Status:"
docker-compose ps

echo ""
echo "🔍 To view logs:"
echo "   docker-compose logs -f app"
echo ""
echo "🛑 To stop services:"
echo "   docker-compose down"
echo ""

echo "🎉 Development environment is ready!"
echo ""
echo "📋 Next steps:"
echo "   1. Update .env with your Shopify and Etsy API credentials"
echo "   2. Test the API endpoints at http://localhost:3000/api"
echo "   3. Sync orders: POST http://localhost:3000/api/platform/sync/last-week-orders"
echo "   4. View orders: GET http://localhost:3000/api/orders"
