#!/bin/bash

# Script to clear Etsy orders and re-sync them with improved attribute extraction
# This script will:
# 1. Delete all existing Etsy orders and order items from the database
# 2. Clear sync history for Etsy
# 3. Re-sync Etsy orders with the new logic that extracts color, size, style

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
API_BASE_URL="http://localhost:3002/api/v1"
DB_NAME="knot_core"

echo -e "${BLUE}🚀 Starting Etsy Order Clear and Re-sync Process${NC}"
echo "=============================================="

# Function to print status
print_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Function to print success
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# Function to print warning
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Function to print error
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if API is running
print_status "Checking if API is running..."
if ! curl -s "${API_BASE_URL}/health" > /dev/null; then
    print_error "API is not running at ${API_BASE_URL}"
    print_error "Please start the development environment first: ./scripts/start-dev.sh"
    exit 1
fi
print_success "API is running"

# Step 1: Clear Etsy orders from database
print_status "Step 1: Clearing existing Etsy orders from database..."

# Delete order items for Etsy orders first (due to foreign key constraints)
print_status "Deleting Etsy order items..."
psql -d "${DB_NAME}" -c "
DELETE FROM order_items
WHERE \"orderId\" IN (
    SELECT id FROM orders WHERE source = 'etsy'
);" || print_warning "No Etsy order items found or deletion failed"

# Delete Etsy orders
print_status "Deleting Etsy orders..."
DELETED_ORDERS=$(psql -d "${DB_NAME}" -t -c "
WITH deleted AS (DELETE FROM orders WHERE source = 'etsy' RETURNING id)
SELECT COUNT(*) FROM deleted;
" | tr -d ' ')

if [ -z "$DELETED_ORDERS" ] || [ "$DELETED_ORDERS" = "0" ]; then
    print_warning "No Etsy orders found to delete"
else
    print_success "Deleted ${DELETED_ORDERS} Etsy orders"
fi

# Step 2: Clear sync history
print_status "Step 2: Clearing Etsy sync history..."
# Note: This would require a specific API endpoint or Redis access
# For now, we'll skip this step as it's not critical

# Step 3: Verify database is clean
print_status "Step 3: Verifying database cleanup..."
REMAINING_ORDERS=$(psql -d "${DB_NAME}" -t -c "SELECT COUNT(*) FROM orders WHERE source = 'etsy';" | tr -d ' ')
REMAINING_ITEMS=$(psql -d "${DB_NAME}" -t -c "
SELECT COUNT(*) FROM order_items
WHERE \"orderId\" IN (
    SELECT id FROM orders WHERE source = 'etsy'
);" | tr -d ' ')

if [ "$REMAINING_ORDERS" = "0" ] && [ "$REMAINING_ITEMS" = "0" ]; then
    print_success "Database cleanup completed successfully"
else
    print_error "Database cleanup incomplete. Remaining: ${REMAINING_ORDERS} orders, ${REMAINING_ITEMS} items"
    exit 1
fi

# Step 4: Re-sync Etsy orders with new logic
print_status "Step 4: Starting Etsy order re-sync with improved attribute extraction..."

# Sync orders from the last 30 days to get recent data
# macOS date command syntax
START_DATE=$(date -u -v-30d '+%Y-%m-%dT%H:%M:%SZ')
END_DATE=$(date -u '+%Y-%m-%dT%H:%M:%SZ')

print_status "Syncing Etsy orders from ${START_DATE} to ${END_DATE}..."

# Make the sync request
SYNC_RESPONSE=$(curl -s -X POST "${API_BASE_URL}/platform/sync/orders/etsy?startDate=${START_DATE}&endDate=${END_DATE}&limit=100&forceUpdate=true" \
    -H "Content-Type: application/json")

# Check if sync was successful
if echo "$SYNC_RESPONSE" | grep -q '"success":true'; then
    print_success "Etsy order sync initiated successfully"

    # Extract sync details from response
    ORDERS_PROCESSED=$(echo "$SYNC_RESPONSE" | grep -o '"ordersProcessed":[0-9]*' | cut -d':' -f2)
    ORDERS_CREATED=$(echo "$SYNC_RESPONSE" | grep -o '"ordersCreated":[0-9]*' | cut -d':' -f2)
    ORDERS_UPDATED=$(echo "$SYNC_RESPONSE" | grep -o '"ordersUpdated":[0-9]*' | cut -d':' -f2)

    print_success "Sync Results:"
    echo "  - Orders Processed: ${ORDERS_PROCESSED:-0}"
    echo "  - Orders Created: ${ORDERS_CREATED:-0}"
    echo "  - Orders Updated: ${ORDERS_UPDATED:-0}"
else
    print_error "Etsy order sync failed"
    echo "Response: $SYNC_RESPONSE"
    exit 1
fi

# Step 5: Verify the sync results
print_status "Step 5: Verifying sync results..."

sleep 5  # Wait a moment for the sync to complete

# Check how many orders were synced
NEW_ORDERS=$(psql -d "${DB_NAME}" -t -c "SELECT COUNT(*) FROM orders WHERE source = 'etsy';" | tr -d ' ')
NEW_ITEMS=$(psql -d "${DB_NAME}" -t -c "
SELECT COUNT(*) FROM order_items
WHERE \"orderId\" IN (
    SELECT id FROM orders WHERE source = 'etsy'
);" | tr -d ' ')

print_success "Sync verification:"
echo "  - New Etsy orders: ${NEW_ORDERS}"
echo "  - New order items: ${NEW_ITEMS}"

# Check if any items have extracted attributes
ITEMS_WITH_ATTRIBUTES=$(psql -d "${DB_NAME}" -t -c "
SELECT COUNT(*) FROM order_items
WHERE \"orderId\" IN (
    SELECT id FROM orders WHERE source = 'etsy'
) AND (style IS NOT NULL OR color IS NOT NULL OR size IS NOT NULL);" | tr -d ' ')

print_success "Items with extracted attributes: ${ITEMS_WITH_ATTRIBUTES}"

# Show sample of extracted data
if [ "$ITEMS_WITH_ATTRIBUTES" -gt "0" ]; then
    print_status "Sample of extracted product attributes:"
    psql -d "${DB_NAME}" -c "
    SELECT sku, style, color, size, title
    FROM order_items
    WHERE \"orderId\" IN (
        SELECT id FROM orders WHERE source = 'etsy'
    ) AND (style IS NOT NULL OR color IS NOT NULL OR size IS NOT NULL)
    LIMIT 5;"
fi

echo ""
print_success "🎉 Etsy order clear and re-sync process completed successfully!"
echo ""
print_status "Summary:"
echo "  ✅ Cleared existing Etsy orders and items"
echo "  ✅ Re-synced Etsy orders with improved attribute extraction"
echo "  ✅ Extracted product attributes (style, color, size) from:"
echo "     - Etsy variations data"
echo "     - Etsy product_data"
echo "     - SKU parsing as fallback"
echo ""
print_status "Next steps:"
echo "  • Check the order with ID 10362854-e59a-4122-bae3-0e050180f4d1 to verify attributes"
echo "  • Review the extracted data in the database"
echo "  • Test the order filtering and search functionality"
