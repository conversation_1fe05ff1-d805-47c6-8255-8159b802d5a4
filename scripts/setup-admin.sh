#!/bin/bash

# Script to create and promote an admin user
# This script will:
# 1. Register a new user
# 2. Verify the user
# 3. Promote the user to admin

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
API_BASE_URL="http://localhost:3002/api/v1"
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="Password123!"

echo -e "${BLUE}🔧 Setting up admin user...${NC}"
echo "=================================="

# Function to print status
print_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Function to print success
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# Function to print error
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Step 1: Register user
print_status "Step 1: Registering admin user..."
REGISTER_RESPONSE=$(curl -s -X POST "${API_BASE_URL}/auth/register" \
    -H "Content-Type: application/json" \
    -d "{
        \"firstName\": \"Admin\",
        \"lastName\": \"User\",
        \"email\": \"${ADMIN_EMAIL}\",
        \"password\": \"${ADMIN_PASSWORD}\"
    }")

if [ $? -eq 0 ]; then
    print_success "User registered successfully"
else
    print_error "Failed to register user"
    echo "Response: $REGISTER_RESPONSE"
    exit 1
fi

# Step 2: Verify user
print_status "Step 2: Verifying user email..."
VERIFY_RESPONSE=$(curl -s -X POST "${API_BASE_URL}/health/verify-user/${ADMIN_EMAIL}")

if [ $? -eq 0 ]; then
    print_success "User verified successfully"
else
    print_error "Failed to verify user"
    echo "Response: $VERIFY_RESPONSE"
    exit 1
fi

# Step 3: Promote to admin
print_status "Step 3: Promoting user to admin..."
PROMOTE_RESPONSE=$(curl -s -X POST "${API_BASE_URL}/health/promote-admin/${ADMIN_EMAIL}")

if [ $? -eq 0 ]; then
    print_success "User promoted to admin successfully"
else
    print_error "Failed to promote user to admin"
    echo "Response: $PROMOTE_RESPONSE"
    exit 1
fi

# Step 4: Test login
print_status "Step 4: Testing admin login..."
LOGIN_RESPONSE=$(curl -s -X POST "${API_BASE_URL}/auth/login" \
    -H "Content-Type: application/json" \
    -d "{
        \"email\": \"${ADMIN_EMAIL}\",
        \"password\": \"${ADMIN_PASSWORD}\"
    }")

if [ $? -eq 0 ]; then
    TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.data.accessToken // .accessToken // empty' 2>/dev/null || echo "")
    if [ -n "$TOKEN" ] && [ "$TOKEN" != "null" ]; then
        print_success "Admin login successful"
        echo "Admin credentials:"
        echo "  Email: $ADMIN_EMAIL"
        echo "  Password: $ADMIN_PASSWORD"
        echo "  Token: ${TOKEN:0:20}..."
    else
        print_error "Failed to extract token from login response"
        echo "Response: $LOGIN_RESPONSE"
        exit 1
    fi
else
    print_error "Failed to login as admin"
    echo "Response: $LOGIN_RESPONSE"
    exit 1
fi

echo ""
print_success "🎉 Admin user setup completed successfully!"
echo ""
echo -e "${BLUE}📝 Admin Credentials:${NC}"
echo "Email: $ADMIN_EMAIL"
echo "Password: $ADMIN_PASSWORD"
echo ""
echo -e "${BLUE}💡 Next Steps:${NC}"
echo "1. Run the order sync script: ./scripts/clear-and-resync-september.sh"
echo "2. Check the admin dashboard for order data"
echo ""
