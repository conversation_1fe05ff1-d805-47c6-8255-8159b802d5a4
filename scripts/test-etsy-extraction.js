const { Pool } = require('pg');

// Database connection
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'knot_core',
  password: 'postgres',
  port: 5433,
});

// Sample Etsy order data from your example
const etsyOrderData = {
  "platform": "etsy",
  "receipt": {
    "receipt_id": **********,
    "receipt_type": 0,
    "seller_user_id": 13208796,
    "seller_email": "<EMAIL>",
    "buyer_user_id": 77466816,
    "buyer_email": null,
    "name": "Ankit Bhat",
    "first_line": "B2-303 Ganga Orchard",
    "second_line": "Pingle Vasti, Mundhwa",
    "city": "Pune",
    "state": "MH",
    "zip": "411036",
    "status": "Paid",
    "formatted_address": "Ankit Bhat\nB2-303 Ganga Orchard\nPingle Vasti, Mundhwa\nPUNE 411036\nMH\nIndia",
    "country_iso": "IN",
    "payment_method": "cc",
    "payment_email": null,
    "message_from_payment": null,
    "message_from_seller": "I hope these rings accompany you and your loved ones in many amazing moments! ❤\r\n\r\nIf you have any questions, me and my small team are happy to help! Message us on Etsy or email <NAME_EMAIL>\r\n\r\nThank you for your love and support ❤\r\n- Tanya & Knot Theory Team\r\n\r\n►► Visit Knotheory.com for the latest designs\r\n\r\n►► Sign up to learn about newest collections & limited edition rings! ⭐\r\nhttp://bit.ly/new-and-limited-edition-rings\r\n\r\n🌎  Knot Theory packaging is now 100% biodegradable and ocean-friendly!\r\n\r\n",
    "message_from_buyer": "",
    "is_shipped": false,
    "is_paid": true,
    "create_timestamp": 1*********,
    "created_timestamp": 1*********,
    "update_timestamp": **********,
    "updated_timestamp": **********,
    "is_gift": false,
    "gift_message": "",
    "gift_sender": "",
    "grandtotal": {"amount": 2798, "divisor": 100, "currency_code": "USD"},
    "subtotal": {"amount": 1799, "divisor": 100, "currency_code": "USD"},
    "total_price": {"amount": 1999, "divisor": 100, "currency_code": "USD"},
    "total_shipping_cost": {"amount": 999, "divisor": 100, "currency_code": "USD"},
    "total_tax_cost": {"amount": 0, "divisor": 100, "currency_code": "USD"},
    "total_vat_cost": {"amount": 0, "divisor": 100, "currency_code": "USD"},
    "discount_amt": {"amount": 200, "divisor": 100, "currency_code": "USD"},
    "gift_wrap_price": {"amount": 0, "divisor": 100, "currency_code": "USD"},
    "shipments": [],
    "transactions": [{
      "transaction_id": 4679726198,
      "title": "Silver Silicone Ring for Him and Her - Breathable Comfort Fit Rubber Wedding Band - 4mm or 6mm His and Hers",
      "description": "****************************\n***** KNOT THOERY *****\n****************************\n\nHello and Welcome! \n\nKnot Theory was founded in the beautiful Vancouver, Canada. We are one of the OGs of silicone rings, and have pioneered many silicone ring innovations, including being the first to design women's silicone rings, metallic silicone rings, comfort fit breathable rings, and 360° engraving on both inside & outside of the ring.\n\nWe love designing highly functional and good-looking silicone rings. We also love our customers from over 40 countries. You rock our world <3\n\nVisit us at knotheory.com for more.\n\n\n*******************************************\n***** WHAT MAKES US DIFFERENT *****\n*******************************************\n\nOUR RINGS:\n\n★ Comfortable 24/7 - Enhanced breathability and comfort with a curved inner wall\n\n★ Looks Great on Your Hand - Thoughtful designs; no finger muffin-tops thanks to the ergonomic design\n\n★ Durable - Best-of-both-worlds thickness & flexibility for durability and comfort.\n\n★ Hypoallergenic - Made with high quality food-grade silicone and pigments\n\n★ Safe for All Activities - No pinching, no calluses. Non-toxic, non-conductive, and heat-resistant. \n\n\nOUR ENGRAVING:\n\n★ 360° engraving available on both inside & outside of the ring\n\n★ Each engraved ring is made locally in Canada\n\n\nOUR GREEN INITIATIVES:\n\n★ Eco-friendly minimal packaging with ocean-friendly, biodegradable plastics\n\n★ Each engraved ring is cleaned with eco-friendly mild soap (to remove engrave residue)\n\n\n**********************\n****** SIZING ******\n**********************\n\nPlease see the product images for a ring size guide, or better yet, print a free size tool here:\n\nhttps://knotheory.com/blogs/whats-my-silicone-ring-size/how-do-i-find-my-ring-size\n\n★ HELPFUL TIPS:\n1. If you are between sizes, for example size 9.5, we recommend going for size 9.\n2. A silicone ring is flexible and can stretch over your knuckles. So quite often it is 1 size smaller than your current metal ring. \n\nHere is an approximate guide:\n\nWomen:\nSize 4: XS\nSize 5: S\nSize 6 & 7: M\nSize 9: L\n\nMen:\nSize 7: XS\nSize 8: S\nSize 9 & 10: M\nSize 12: L\n\n\n*************************************\n**** RETURNS & EXCHANGES ****\n*************************************\n\nPlease contact us within 14 days of receiving your ring for exchanges and returns.\n\nIf you'd like to exchange your ring for a different size, we offer a perfect fit promise on your FIRST ring. If your ring was engraved, the re-engraving will be free too. You'll be responsible for postage.\n\nFor returns, you'll be responsible for the return postage. For engraved rings, we'll refund your payment minus $10 for single-sided engraving, or $15 for double-sided engraving.\n\n\n*******************************************\n**** WHY WEAR SILICONE RINGS? ****\n*******************************************\n\n★ KEEPS YOUR FINGERS SAFELY ATTACHED \n\nEach year there are 150,000 ring avulsion injuries in the US alone. Ring avulsions include \"degloving\" and \"severing\" of the finger, which are extremely painful and often irreversible. Your ring should not to be a potential health hazard!\n\n\n★ MOST COMFORTABLE RINGS IN THE WORLD\n\nPeople who wear Knot Theory say these are the MOST comfortable silicone rings they've ever worn. \n\n\n★  GREAT TRAINING RING\n\n1 in 4 men & 1 in 7 women lose their wedding rings (at least once), especially in the beginning of the marriage, when getting used to wearing the ring. Silicone wedding rings are a great training ring!\n\n\n★ FLEXES WITH YOUR FINGER\n\nOur fingers swell for different reasons. Weather, pregnancy, health-related reasons. Your silicone ring will flex with your finger, so it's always comfortable to wear.\n\n\n★ TRAVEL SMART\n\nWhen you travel - whether it's half way around the world or a trip to the gym - wear your silicone wedding ring instead of your irreplaceable and expensive ring.\n\nIt's also a good idea to not flash your expensive ring while traveling. We want you to get into all kinds of troubles (the fun ones!) while traveling, but stay away from travel thefts and robberies.\n\n\n********************************\n**** THOUGHTFUL GIFT  ****\n********************************\n\nThe best gift is a thoughtful gift. Keep your loved ones (and their fingers) safe and they'll never want to take this ring off! ♥\n\nGift ideas:\n- Anniversary gift for hubby or wife\n- Engagement ring gift for your fiance\n- Promise ring for your love\n- Birthday gift to your super active and hands-on bestie\n- Mother's day and father's day gift\n- Wedding gifts to bride and groom\n- Gift to self!\n\nEngraving ideas:\n- Wedding date in roman numerals\n- Meaningful or funny quote between you and your love\n- Cute nicknames\n- Initials\n- Coordinates to that special place\n- Your inspiring word or mantra\n\n\nTo many more happiest years of your life :)\n~ Tanya & Knot Theory Team****************************\n***** KNOT THOERY *****\n****************************\n\nHello and Welcome! \n\nKnot Theory was founded in the beautiful Vancouver, Canada. We are one of the OGs of silicone rings, and have pioneered many silicone ring innovations, including being the first to design women's silicone rings, metallic silicone rings, comfort fit breathable rings, and 360° engraving on both inside & outside of the ring.\n\nWe love designing highly functional and good-looking silicone rings. We also love our customers from over 40 countries. You rock our world <3\n\nVisit us at knotheory.com for more.\n\n\n*******************************************\n***** WHAT MAKES US DIFFERENT *****\n*******************************************\n\nOUR RINGS:\n\n★ Comfortable 24/7 - Enhanced breathability and comfort with a curved inner wall\n\n★ Looks Great on Your Hand - Thoughtful designs; no finger muffin-tops thanks to the ergonomic design\n\n★ Durable - Best-of-both-worlds thickness & flexibility for durability and comfort.\n\n★ Hypoallergenic - Made with high quality food-grade silicone and pigments\n\n★ Safe for All Activities - No pinching, no calluses. Non-toxic, non-conductive, and heat-resistant. \n\n\nOUR ENGRAVING:\n\n★ 360° engraving available on both inside & outside of the ring\n\n★ Each engraved ring is made locally in Canada\n\n\nOUR GREEN INITIATIVES:\n\n★ Eco-friendly minimal packaging with ocean-friendly, biodegradable plastics\n\n★ Each engraved ring is cleaned with eco-friendly mild soap (to remove engrave residue)\n\n\n**********************\n****** SIZING ******\n**********************\n\nPlease see the product images for a ring size guide, or better yet, print a free size tool here:\n\nhttps://knotheory.com/blogs/whats-my-silicone-ring-size/how-do-i-find-my-ring-size\n\n★ HELPFUL TIPS:\n1. If you are between sizes, for example size 9.5, we recommend going for size 9.\n2. A silicone ring is flexible and can stretch over your knuckles. So quite often it is 1 size smaller than your current metal ring. \n\nHere is an approximate guide:\n\nWomen:\nSize 4: XS\nSize 5: S\nSize 6 & 7: M\nSize 9: L\n\nMen:\nSize 7: XS\nSize 8: S\nSize 9 & 10: M\nSize 12: L\n\n\n*************************************\n**** RETURNS & EXCHANGES ****\n*************************************\n\nPlease contact us within 14 days of receiving your ring for exchanges and returns.\n\nIf you'd like to exchange your ring for a different size, we offer a perfect fit promise on your FIRST ring, so it is sent to you for free. For engraved rings, we charge $12 re-engrave fee on one side, and $4 re-engrave fee on the second side; it is also sent to you for free.\n\nFor returns, you'll be responsible for the return postage to US or Canada. For engraved rings, we'll refund your payment minus $12 for single-sided engraving, or $16 for double-sided engraving.\n\n\n*******************************************\n**** WHY WEAR SILICONE RINGS? ****\n*******************************************\n\n★ KEEPS YOUR FINGERS SAFELY ATTACHED \n\nEach year there are 150,000 ring avulsion injuries in the US alone. Ring avulsions include \"degloving\" and \"severing\" of the finger, which are extremely painful and often irreversible. Your ring should not to be a potential health hazard!\n\n\n★ MOST COMFORTABLE RINGS IN THE WORLD\n\nPeople who wear Knot Theory say these are the MOST comfortable silicone rings they've ever worn. \n\n\n★  GREAT TRAINING RING\n\n1 in 4 men & 1 in 7 women lose their wedding rings (at least once), especially in the beginning of the marriage, when getting used to wearing the ring. Silicone wedding rings are a great training ring!\n\n\n★ FLEXES WITH YOUR FINGER\n\nOur fingers swell for different reasons. Weather, pregnancy, health-related reasons. Your silicone ring will flex with your finger, so it's always comfortable to wear.\n\n\n★ TRAVEL SMART\n\nWhen you travel - whether it's half way around the world or a trip to the gym - wear your silicone wedding ring instead of your irreplaceable and expensive ring.\n\nIt's also a good idea to not flash your expensive ring while traveling. We want you to get into all kinds of troubles (the fun ones!) while traveling, but stay away from travel thefts and robberies.\n\n\n********************************\n**** THOUGHTFUL GIFT  ****\n********************************\n\nThe best gift is a thoughtful gift. Keep your loved ones (and their fingers) safe and they'll never want to take this ring off! ♥\n\nGift ideas:\n- Anniversary gift for hubby or wife\n- Engagement ring gift for your fiance\n- Promise ring for your love\n- Birthday gift to your super active and hands-on bestie\n- Mother's day and father's day gift\n- Wedding gifts to bride and groom\n- Gift to self!\n\nEngraving ideas:\n- Wedding date in roman numerals\n- Meaningful or funny quote between you and your love\n- Cute nicknames\n- Initials\n- Coordinates to that special place\n- Your inspiring word or mantra\n\n\nTo many more happiest years of your life :)\n~ Tanya & Knot Theory Team",
      "seller_user_id": 13208796,
      "buyer_user_id": 77466816,
      "create_timestamp": 1*********,
      "created_timestamp": 1*********,
      "paid_timestamp": **********,
      "shipped_timestamp": null,
      "quantity": 1,
      "listing_image_id": 2151687643,
      "receipt_id": **********,
      "is_digital": false,
      "file_data": "",
      "listing_id": 654011290,
      "sku": "ComfortFit-6mm-Silver-12",
      "product_id": 15214655280,
      "transaction_type": "listing",
      "price": {"amount": 1999, "divisor": 100, "currency_code": "USD"},
      "shipping_cost": {"amount": 0, "divisor": 100, "currency_code": "USD"},
      "variations": [
        {"property_id": 513, "value_id": 1361211781115, "formatted_name": "Size", "formatted_value": "12 (6mm bandwidth)"},
        {"property_id": 200, "value_id": 55501864175, "formatted_name": "Color", "formatted_value": "Silver"}
      ],
      "product_data": [
        {"property_id": 513, "property_name": "Custom Property", "scale_id": null, "scale_name": null, "value_ids": [1361211781115], "values": ["12 (6mm bandwidth)"]},
        {"property_id": 200, "property_name": "Primary color", "scale_id": null, "scale_name": null, "value_ids": [55501864175], "values": ["Silver"]}
      ],
      "shipping_profile_id": 16386588278,
      "min_processing_days": 5,
      "max_processing_days": 7,
      "shipping_method": null,
      "shipping_upgrade": null,
      "expected_ship_date": 1755154740,
      "buyer_coupon": 182,
      "shop_coupon": 2
    }],
    "refunds": []
  },
  "syncedAt": "2025-08-05T05:44:47.597Z",
  "apiVersion": "v3"
};

// Function to extract product attributes (same logic as in the service)
function extractProductAttributes(transaction) {
  const result = {
    style: undefined,
    color: undefined,
    size: undefined,
    design: undefined,
    isCustom: false,
    isEngrave: false,
    customization: undefined,
    engraving: undefined,
  };

  // 1. Extract from variations (Etsy's primary variation data)
  if (transaction.variations && Array.isArray(transaction.variations)) {
    for (const variation of transaction.variations) {
      const propertyName = variation.formatted_name?.toLowerCase() || '';
      const value = variation.formatted_value || '';

      if (propertyName.includes('color') || propertyName.includes('colour')) {
        result.color = value;
      } else if (propertyName.includes('size')) {
        // Extract size number from formatted value like "12 (6mm bandwidth)"
        const sizeMatch = value.match(/(\d+)/);
        if (sizeMatch) {
          result.size = parseInt(sizeMatch[1], 10);
        }
      } else if (propertyName.includes('style') || propertyName.includes('type')) {
        result.style = value;
      } else if (propertyName.includes('design') || propertyName.includes('pattern')) {
        result.design = value;
      }
    }
  }

  // 2. Extract from product_data (alternative variation format)
  if (transaction.product_data && Array.isArray(transaction.product_data)) {
    for (const productData of transaction.product_data) {
      const propertyName = productData.property_name?.toLowerCase() || '';
      const values = productData.values || [];
      const value = values[0] || '';

      if (propertyName.includes('color') || propertyName.includes('colour') || propertyName === 'primary color') {
        result.color = result.color || value;
      } else if (propertyName.includes('size') || propertyName === 'custom property') {
        // For custom property, check if it contains size info
        const sizeMatch = value.match(/(\d+)/);
        if (sizeMatch) {
          result.size = result.size || parseInt(sizeMatch[1], 10);
        }
      } else if (propertyName.includes('style') || propertyName.includes('type')) {
        result.style = result.style || value;
      } else if (propertyName.includes('design') || propertyName.includes('pattern')) {
        result.design = result.design || value;
      }
    }
  }

  // 3. Extract from SKU as fallback (format: Style-Width-Color-Size)
  if (transaction.sku && (!result.style || !result.color || !result.size)) {
    const skuParts = transaction.sku.split('-');
    
    if (skuParts.length >= 3) {
      // ComfortFit-6mm-Silver-12 format
      if (!result.style && skuParts[0]) {
        result.style = skuParts[0];
      }
      if (!result.color && skuParts[2]) {
        result.color = skuParts[2];
      }
      if (!result.size && skuParts[3]) {
        const sizeMatch = skuParts[3].match(/(\d+)/);
        if (sizeMatch) {
          result.size = parseInt(sizeMatch[1], 10);
        }
      }
    }
  }

  return result;
}

async function testExtraction() {
  try {
    console.log('🧪 Testing Etsy Product Attribute Extraction');
    console.log('===========================================');
    
    const transaction = etsyOrderData.receipt.transactions[0];
    console.log('\n📦 Original Transaction Data:');
    console.log('SKU:', transaction.sku);
    console.log('Title:', transaction.title);
    console.log('Variations:', JSON.stringify(transaction.variations, null, 2));
    console.log('Product Data:', JSON.stringify(transaction.product_data, null, 2));
    
    console.log('\n🔍 Extracting Attributes...');
    const extracted = extractProductAttributes(transaction);
    
    console.log('\n✅ Extracted Attributes:');
    console.log('Style:', extracted.style);
    console.log('Color:', extracted.color);
    console.log('Size:', extracted.size);
    console.log('Design:', extracted.design);
    console.log('Is Custom:', extracted.isCustom);
    console.log('Is Engrave:', extracted.isEngrave);
    
    console.log('\n🎯 Expected vs Actual:');
    console.log('Expected Style: ComfortFit | Actual:', extracted.style);
    console.log('Expected Color: Silver | Actual:', extracted.color);
    console.log('Expected Size: 12 | Actual:', extracted.size);
    
    // Test if extraction worked correctly
    const success = extracted.style === 'ComfortFit' && 
                   extracted.color === 'Silver' && 
                   extracted.size === 12;
    
    console.log('\n' + (success ? '🎉 SUCCESS!' : '❌ FAILED!'));
    console.log('Attribute extraction is', success ? 'working correctly' : 'not working as expected');
    
  } catch (error) {
    console.error('❌ Error during testing:', error);
  }
}

// Run the test
testExtraction();
