#!/bin/bash

# Script to clear all orders and re-sync them from Etsy and Shopify with enhanced SKU extraction
# This script will:
# 1. Clear all existing orders and order items from the database
# 2. Clear sync history for all platforms
# 3. Re-sync orders from both Etsy and Shopify from September 1st till now
# 4. Use enhanced SKU extraction to fill missing color, size, design, style information

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
API_BASE_URL="http://localhost:3002/api/v1"
DB_NAME="knot_core"

echo -e "${BLUE}🚀 Starting Complete Order Clear and Re-sync Process${NC}"
echo "============================================================"
echo -e "${PURPLE}This will clear ALL order data and re-sync from September 1st${NC}"
echo "============================================================"

# Function to print status
print_status() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Function to print success
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# Function to print warning
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Function to print error
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if API is running
check_api() {
    print_status "Checking if API is running..."
    if ! curl -s "${API_BASE_URL}/health" > /dev/null; then
        print_error "API is not running at ${API_BASE_URL}"
        print_error "Please start the development environment first"
        exit 1
    fi
    print_success "API is running"
}

# Function to get admin token
get_admin_token() {
    print_status "Getting admin authentication token..."
    
    # Try to login and get token
    LOGIN_RESPONSE=$(curl -s -X POST "${API_BASE_URL}/auth/login" \
        -H "Content-Type: application/json" \
        -d '{"email":"<EMAIL>","password":"Password123!"}' || echo "")
    
    if [ -z "$LOGIN_RESPONSE" ]; then
        print_error "Failed to login. Please ensure admin user exists."
        exit 1
    fi
    
    # Extract token from response
    TOKEN=$(echo "$LOGIN_RESPONSE" | jq -r '.data.accessToken // .accessToken // empty' 2>/dev/null || echo "")
    
    if [ -z "$TOKEN" ] || [ "$TOKEN" = "null" ]; then
        print_error "Failed to extract authentication token"
        print_error "Login response: $LOGIN_RESPONSE"
        exit 1
    fi
    
    print_success "Authentication token obtained"
}

# Function to clear all order data
clear_all_orders() {
    print_status "Step 1: Clearing all existing order data..."
    
    CLEAR_RESPONSE=$(curl -s -X DELETE "${API_BASE_URL}/platform/orders/clear" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json")
    
    if [ $? -eq 0 ]; then
        ORDERS_DELETED=$(echo "$CLEAR_RESPONSE" | jq -r '.data.ordersDeleted // 0' 2>/dev/null || echo "0")
        ITEMS_DELETED=$(echo "$CLEAR_RESPONSE" | jq -r '.data.orderItemsDeleted // 0' 2>/dev/null || echo "0")
        
        if [ "$ORDERS_DELETED" = "0" ] && [ "$ITEMS_DELETED" = "0" ]; then
            print_warning "No existing order data found to clear"
        else
            print_success "Cleared $ORDERS_DELETED orders and $ITEMS_DELETED order items"
        fi
    else
        print_error "Failed to clear order data via API"
        exit 1
    fi
}

# Function to sync platform orders
sync_platform_orders() {
    local platform=$1
    local platform_name=$2

    print_status "Step 2.$platform: Syncing $platform_name orders from September 1st..."

    # Calculate date range (September 1st to now)
    START_DATE="2024-09-01T00:00:00Z"
    END_DATE=$(date -u '+%Y-%m-%dT%H:%M:%SZ')

    print_status "Date range: $START_DATE to $END_DATE"

    # Check platform status first
    print_status "Checking $platform_name platform status..."
    STATUS_RESPONSE=$(curl -s "${API_BASE_URL}/platform/status/${platform}" \
        -H "Authorization: Bearer $TOKEN")

    if [ $? -eq 0 ]; then
        IS_CONNECTED=$(echo "$STATUS_RESPONSE" | jq -r '.isConnected // false' 2>/dev/null || echo "false")
        if [ "$IS_CONNECTED" = "false" ]; then
            print_warning "$platform_name is not connected. Sync may fail."
        else
            print_success "$platform_name is connected and ready"
        fi
    fi

    # Sync orders with enhanced extraction
    print_status "Starting $platform_name order sync..."
    SYNC_RESPONSE=$(curl -s -X POST "${API_BASE_URL}/platform/sync/orders/${platform}?startDate=${START_DATE}&endDate=${END_DATE}&limit=1000&forceUpdate=true" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json")

    if [ $? -eq 0 ]; then
        # Check if response contains error
        ERROR_MSG=$(echo "$SYNC_RESPONSE" | jq -r '.error // empty' 2>/dev/null || echo "")
        if [ -n "$ERROR_MSG" ]; then
            print_error "$platform_name sync failed: $ERROR_MSG"
            return 1
        fi

        ORDERS_PROCESSED=$(echo "$SYNC_RESPONSE" | jq -r '.ordersProcessed // 0' 2>/dev/null || echo "0")
        ORDERS_CREATED=$(echo "$SYNC_RESPONSE" | jq -r '.ordersCreated // 0' 2>/dev/null || echo "0")
        ORDERS_UPDATED=$(echo "$SYNC_RESPONSE" | jq -r '.ordersUpdated // 0' 2>/dev/null || echo "0")
        ORDERS_SKIPPED=$(echo "$SYNC_RESPONSE" | jq -r '.ordersSkipped // 0' 2>/dev/null || echo "0")
        ERRORS=$(echo "$SYNC_RESPONSE" | jq -r '.errors // [] | length' 2>/dev/null || echo "0")

        print_success "$platform_name sync completed:"
        echo "  📊 Processed: $ORDERS_PROCESSED"
        echo "  ➕ Created: $ORDERS_CREATED"
        echo "  🔄 Updated: $ORDERS_UPDATED"
        echo "  ⏭️  Skipped: $ORDERS_SKIPPED"
        if [ "$ERRORS" != "0" ]; then
            echo "  ⚠️  Errors: $ERRORS"
        fi
    else
        print_error "Failed to sync $platform_name orders"
        print_error "Response: $SYNC_RESPONSE"
        return 1
    fi
}

# Function to verify sync results
verify_results() {
    print_status "Step 3: Verifying sync results..."
    
    # Get order statistics
    STATS_RESPONSE=$(curl -s "${API_BASE_URL}/orders/stats" \
        -H "Authorization: Bearer $TOKEN")
    
    if [ $? -eq 0 ]; then
        TOTAL_ORDERS=$(echo "$STATS_RESPONSE" | jq -r '.totalOrders // 0' 2>/dev/null || echo "0")
        ETSY_ORDERS=$(echo "$STATS_RESPONSE" | jq -r '.ordersByPlatform.ETSY // 0' 2>/dev/null || echo "0")
        SHOPIFY_ORDERS=$(echo "$STATS_RESPONSE" | jq -r '.ordersByPlatform.SHOPIFY // 0' 2>/dev/null || echo "0")
        
        print_success "Final Results:"
        echo "  📈 Total Orders: $TOTAL_ORDERS"
        echo "  🛍️  Etsy Orders: $ETSY_ORDERS"
        echo "  🛒 Shopify Orders: $SHOPIFY_ORDERS"
    else
        print_warning "Could not retrieve final statistics"
    fi
}

# Main execution
main() {
    echo ""
    check_api
    echo ""
    get_admin_token
    echo ""
    clear_all_orders
    echo ""
    sync_platform_orders "etsy" "Etsy"
    echo ""
    sync_platform_orders "shopify" "Shopify"
    echo ""
    verify_results
    echo ""
    print_success "🎉 Complete order sync process finished successfully!"
    echo ""
    echo -e "${PURPLE}📝 Summary:${NC}"
    echo "- ✅ All existing order data cleared"
    echo "- ✅ Etsy orders synced from September 1st with enhanced SKU extraction"
    echo "- ✅ Shopify orders synced from September 1st with enhanced SKU extraction"
    echo "- ✅ Missing color, size, design, style extracted from SKU when needed"
    echo ""
}

# Run main function
main
