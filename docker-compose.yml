services:
  app:
    build:
      context: .
      target: development
    container_name: knot-core-app
    restart: unless-stopped
    ports:
      - '${PORT:-3001}:3001'
    environment:
      - NODE_ENV=development
    env_file:
      - .env.docker
    volumes:
      - .:/usr/src/app
      - /usr/src/app/node_modules
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - knot-core-network

  postgres:
    image: postgres:15-alpine
    container_name: knot-core-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${DATABASE_NAME:-knot_core}
      POSTGRES_USER: ${DATABASE_USERNAME:-postgres}
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD:-postgres}
      PGDATA: /var/lib/postgresql/data/pgdata
    ports:
      - '5433:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init:/docker-entrypoint-initdb.d
    healthcheck:
      test:
        ['CMD-SHELL', 'pg_isready -U ${DATABASE_USERNAME:-postgres} -d ${DATABASE_NAME:-knot_core}']
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - knot-core-network

  redis:
    image: redis:7-alpine
    container_name: knot-core-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis123}
    ports:
      - '${REDIS_PORT:-6379}:6379'
    volumes:
      - redis_data:/data
    healthcheck:
      test: ['CMD', 'redis-cli', '--raw', 'incr', 'ping']
      interval: 10s
      timeout: 3s
      retries: 5
    networks:
      - knot-core-network

  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: knot-core-pgadmin
    restart: unless-stopped
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_EMAIL:-<EMAIL>}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD:-admin123}
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - '${PGADMIN_PORT:-8080}:80'
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - knot-core-network

  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: knot-core-redis-commander
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis:6379:0:${REDIS_PASSWORD:-redis123}
    ports:
      - '${REDIS_COMMANDER_PORT:-8081}:8081'
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - knot-core-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local

networks:
  knot-core-network:
    driver: bridge
