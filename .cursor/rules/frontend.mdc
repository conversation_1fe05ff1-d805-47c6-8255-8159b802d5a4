---
description: Frontend implementation standards using TanStack, Zustand & Shadcn UI in NestJS context
globs:
  - 'src/**/*.ts'
  - 'src/**/*.tsx'
alwaysApply: true
---

# Frontend Implementation Guidelines

## Core Principles

- **Keep it clean & simple**  
  Strive for minimal, readable code. Avoid unnecessary abstractions or boilerplate.

- **Use TanStack & Zustand for state & data loading**  
  Always use TanStack Query or TanStack-related libraries for data management and caching. Use Zustand for local state management.

- **Use Shadcn UI components**  
  Prefer Shadcn UI for all UI elements to maintain consistency and modern design.

- **No test code in frontend modules**  
  Skip writing test files or test logic within frontend modules. Keep UI code lean.

- **Modular, small components only**  
  - Max ~200 lines per file or component.
  - Use smaller, reusable components; break large components into parts.

---

## Examples

```tsx
// Good example: small component using Zustand & Shadcn UI
import { Button } from "shadcn-ui";
import create from "zustand";
import { useQuery } from "tanstack/react-query";

const useStore = create(set => ({
  count: 0,
  inc: () => set(state => ({ count: state.count + 1 })),
}));

export function Counter() {
  const { data } = useQuery(["data"], fetchData);
  const { count, inc } = useStore();

  return (
    <div>
      <p>Count: {count}</p>
      <Button onClick={inc}>Increment</Button>
      {data && <span>{data.value}</span>}
    </div>
  );
}
