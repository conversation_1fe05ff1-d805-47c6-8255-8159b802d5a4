import { NextRequest, NextResponse } from 'next/server';
import { apiFetch } from '@/lib/api';

export async function POST(req: NextRequest) {
  const body = await req.json();
  try {
    const registerRes = await apiFetch<{ success: boolean; data: { message: string } }>(
      '/auth/register',
      { method: 'POST', body: JSON.stringify(body) },
    );

    // For dev convenience: auto-verify the user using the fixed token the backend expects
    // This mirrors AuthService using a fixed token '000-111' in development
    try {
      await apiFetch('/auth/verify-email', {
        method: 'POST',
        body: JSON.stringify({ token: '000-111' }),
      });
    } catch {}

    // Dev convenience: promote to admin so the user can access admin features immediately
    try {
      const encodedEmail = encodeURIComponent(body.email);
      await apiFetch(`/health/promote-admin/${encodedEmail}`, { method: 'POST' });
    } catch {}

    return NextResponse.json(registerRes);
  } catch (e: any) {
    return NextResponse.json({ success: false, message: e.message }, { status: 400 });
  }
}
