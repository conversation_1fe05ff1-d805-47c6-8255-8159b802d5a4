import { NextRequest, NextResponse } from 'next/server';
import { apiFetch } from '@/lib/api';

export async function POST(req: NextRequest) {
  const { email, password } = await req.json();
  try {
    const res = await apiFetch<{ success: boolean; data: { user: any; accessToken: string } }>(
      '/auth/login',
      {
        method: 'POST',
        body: JSON.stringify({ email, password }),
      },
    );

    const token = res.data.accessToken;
    const response = NextResponse.json({ success: true });
    const isProd = process.env.NODE_ENV === 'production';
    response.cookies.set('token', token, {
      httpOnly: true,
      secure: isProd,
      sameSite: 'lax',
      path: '/',
      maxAge: 60 * 60 * 4, // 4 hours
    });
    return response;
  } catch (e: any) {
    return NextResponse.json({ success: false, message: e.message }, { status: 401 });
  }
}
