import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest) {
  try {
    const token = (await cookies()).get('token')?.value;

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const backendUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001/api/v1';
    const response = await fetch(`${backendUrl}/users`, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Backend responded with status: ${response.status}`);
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching users:', error);
    // Return mock data for now since backend might not have this endpoint yet
    return NextResponse.json({
      users: [
        {
          id: '1',
          name: 'Admin User',
          email: '<EMAIL>',
          role: 'admin',
          isVerified: true,
          lastLogin: new Date().toISOString(),
        },
        {
          id: '2',
          name: '<PERSON> Doe',
          email: '<EMAIL>',
          role: 'user',
          isVerified: true,
          lastLogin: new Date(Date.now() - 86400000).toISOString(),
        },
        {
          id: '3',
          name: 'Jane Smith',
          email: '<EMAIL>',
          role: 'user',
          isVerified: false,
          lastLogin: undefined,
        },
      ],
    });
  }
}
