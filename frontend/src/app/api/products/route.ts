import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest) {
  try {
    const token = (await cookies()).get('token')?.value;

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const backendUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001/api/v1';
    const response = await fetch(`${backendUrl}/products`, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Backend responded with status: ${response.status}`);
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching products:', error);
    // Return mock data for now since backend might not have this endpoint yet
    return NextResponse.json({
      products: [
        {
          id: '1',
          name: 'Classic Silver Ring',
          sku: 'RING-001',
          platform: 'shopify',
          price: 89.99,
          stock: 15,
          status: 'active',
        },
        {
          id: '2',
          name: 'Gold Pendant Necklace',
          sku: 'NECK-001',
          platform: 'etsy',
          price: 125.5,
          stock: 8,
          status: 'active',
        },
        {
          id: '3',
          name: 'Pearl Earrings',
          sku: 'EARR-001',
          platform: 'shopify',
          price: 45.99,
          stock: 5,
          status: 'active',
        },
        {
          id: '4',
          name: 'Diamond Bracelet',
          sku: 'BRAC-001',
          platform: 'amazon',
          price: 299.99,
          stock: 3,
          status: 'active',
        },
      ],
    });
  }
}
