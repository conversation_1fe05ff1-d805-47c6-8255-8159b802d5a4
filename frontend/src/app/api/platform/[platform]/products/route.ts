import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ platform: string }> },
) {
  try {
    const token = (await cookies()).get('token')?.value;
    const { platform } = await params;

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const backendUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001/api/v1';
    const response = await fetch(`${backendUrl}/platform/${platform}/products`, {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Backend responded with status: ${response.status}`);
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error(`Error checking ${(await params).platform} products:`, error);
    // Return mock data for now since backend might not have this endpoint yet
    const { platform } = await params;
    return NextResponse.json({
      success: true,
      platform: platform,
      products: [
        {
          id: `new-${Date.now()}`,
          name: `New ${platform} Product`,
          sku: `${platform.toUpperCase()}-${Math.floor(Math.random() * 1000)}`,
          platform: platform,
          price: Math.floor(Math.random() * 100) + 25,
          stock: Math.floor(Math.random() * 50) + 10,
          status: 'active',
        },
      ],
    });
  }
}
