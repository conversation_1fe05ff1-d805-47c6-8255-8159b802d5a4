import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ platform: string }> },
) {
  try {
    const token = (await cookies()).get('token')?.value;
    const { platform } = await params;

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const backendUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001/api/v1';

    // Two-month window (approx 60 days)
    const end = new Date();
    const start = new Date();
    start.setDate(start.getDate() - 60);

    const query = new URLSearchParams({
      startDate: start.toISOString(),
      endDate: end.toISOString(),
      limit: '1000',
      forceUpdate: 'false',
    }).toString();

    // Use POST /platform/sync/orders/:platform to trigger fetch+persist
    const response = await fetch(`${backendUrl}/platform/sync/orders/${platform}?${query}`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Backend responded with status: ${response.status}`);
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error(`Error checking ${(await params).platform} orders:`, error);
    // Return mock data for now since backend might not have this endpoint yet
    const { platform } = await params;
    return NextResponse.json({
      success: true,
      platform: platform,
      orders: [
        {
          id: `new-${Date.now()}`,
          orderNumber: `${platform.toUpperCase()}-${Math.floor(Math.random() * 1000)}`,
          platform: platform,
          customerName: 'New Customer',
          total: Math.floor(Math.random() * 200) + 50,
          status: 'pending',
          createdAt: new Date().toISOString(),
        },
      ],
    });
  }
}
