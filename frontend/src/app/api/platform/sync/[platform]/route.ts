import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ platform: string }> },
) {
  try {
    const token = (await cookies()).get('token')?.value;
    const { platform } = await params;

    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const backendUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3001/api/v1';
    const response = await fetch(`${backendUrl}/platform/sync/${platform}`, {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Backend responded with status: ${response.status}`);
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error(`Error syncing platform ${(await params).platform}:`, error);
    // Return success for now since backend might not have this endpoint yet
    const { platform } = await params;
    return NextResponse.json({
      success: true,
      message: `Sync started for ${platform}`,
      platform: platform,
    });
  }
}
