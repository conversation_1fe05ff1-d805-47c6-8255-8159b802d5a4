import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Package, TrendingUp, AlertTriangle, CheckCircle } from 'lucide-react';

export default function ProductsPage() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Products</h2>
        <p className="text-muted-foreground">
          Manage your product catalog across all platforms.
        </p>
      </div>

      {/* Product Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Products</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">567</div>
            <p className="text-xs text-muted-foreground">+12 new this week</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Products</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">489</div>
            <p className="text-xs text-muted-foreground">Available for sale</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Low Stock</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">23</div>
            <p className="text-xs text-muted-foreground">Need restocking</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Top Seller</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Ring #42</div>
            <p className="text-xs text-muted-foreground">Most popular item</p>
          </CardContent>
        </Card>
      </div>

      {/* Product Management */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Product Categories</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span>Rings</span>
                <span className="text-sm text-muted-foreground">234 products</span>
              </div>
              <div className="flex items-center justify-between">
                <span>Necklaces</span>
                <span className="text-sm text-muted-foreground">156 products</span>
              </div>
              <div className="flex items-center justify-between">
                <span>Earrings</span>
                <span className="text-sm text-muted-foreground">89 products</span>
              </div>
              <div className="flex items-center justify-between">
                <span>Bracelets</span>
                <span className="text-sm text-muted-foreground">67 products</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Platform Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span>Shopify</span>
                <span className="text-sm text-muted-foreground">245 products</span>
              </div>
              <div className="flex items-center justify-between">
                <span>Etsy</span>
                <span className="text-sm text-muted-foreground">198 products</span>
              </div>
              <div className="flex items-center justify-between">
                <span>Amazon</span>
                <span className="text-sm text-muted-foreground">124 products</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Products Table Placeholder */}
      <Card>
        <CardHeader>
          <CardTitle>Product Catalog</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <Package className="mx-auto h-12 w-12 mb-4 opacity-50" />
            <p>Product management interface will be implemented here</p>
            <p className="text-sm">Add, edit, and manage products across all platforms</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
