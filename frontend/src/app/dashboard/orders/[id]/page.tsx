'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/app-sidebar';
import { SiteHeader } from '@/components/site-header';
import { ArrowLeft, Calendar, Package, ShoppingCart, User, MapPin, CreditCard } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';

interface OrderItem {
  id: string;
  sku: string;
  title: string;
  quantity: number;
  unitPrice: string;
  totalPrice: string;
  currency: string;
  isCustom: boolean;
  isEngrave: boolean;
  customText?: string;
  engraveText?: string;
  size?: string;
  color?: string;
  style?: string;
  design?: string;
}

interface Order {
  id: string;
  externalOrderId: string;
  orderNumber: string;
  status: string;
  source: string;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  shippingAddress: {
    line1: string;
    line2?: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  subtotalPrice: string;
  shippingPrice: string;
  taxAmount: string;
  discountAmount: string;
  totalPrice: string;
  currency: string;
  customerNote?: string;
  isProcessed: boolean;
  externalCreatedAt: string;
  createdAt: string;
  updatedAt: string;
  orderItems: OrderItem[];
  metadata?: any;
}

export default function OrderDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(true);

  const orderId = params.id as string;

  useEffect(() => {
    if (orderId) {
      fetchOrderDetails();
    }
  }, [orderId]);

  const fetchOrderDetails = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/orders/${orderId}`);

      if (!response.ok) {
        if (response.status === 401) {
          toast.error('Session expired. Please login again.');
          router.push('/login');
          return;
        }
        throw new Error('Failed to fetch order details');
      }

      const data = await response.json();
      setOrder(data.data);
    } catch (error) {
      console.error('Error fetching order details:', error);
      toast.error('Failed to fetch order details');
    } finally {
      setLoading(false);
    }
  };

  const getPlatformLogo = (source: string) => {
    const logos: Record<string, string> = {
      shopify: '/brands/Shopify.com/Shopify.com_Symbol_0.svg',
      amazon: '/brands/Amazon/Amazon_Logo_0.svg',
      etsy: '/brands/Etsy/Etsy_Logo_0.svg',
    };
    return logos[source] || '/brands/Icon.jpeg';
  };

  const getStatusVariant = (status: string) => {
    const variants: Record<string, 'default' | 'secondary' | 'destructive' | 'outline'> = {
      pending: 'outline',
      confirmed: 'default',
      processing: 'secondary',
      shipped: 'default',
      delivered: 'default',
      cancelled: 'destructive',
      refunded: 'destructive',
      returned: 'outline',
    };
    return variants[status] || 'outline';
  };

  const formatAddress = (address: Order['shippingAddress']) => {
    const parts = [
      address.line1,
      address.line2,
      `${address.city}, ${address.state} ${address.postalCode}`,
      address.country,
    ].filter(Boolean);
    return parts.join('\n');
  };

  if (loading) {
    return (
      <SidebarProvider
        style={
          {
            '--sidebar-width': 'calc(var(--spacing) * 52)',
            '--header-height': 'calc(var(--spacing) * 10)',
          } as React.CSSProperties
        }
      >
        <AppSidebar variant="inset" />
        <SidebarInset>
          <SiteHeader />
          <div className="flex flex-1 flex-col">
            <div className="@container/main flex flex-1 flex-col gap-2">
              <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
                <div className="space-y-6 px-4 lg:px-6">
                  <div className="flex items-center gap-4">
                    <Button variant="outline" size="sm" onClick={() => router.back()}>
                      <ArrowLeft className="h-4 w-4 mr-2" />
                      Back
                    </Button>
                    <div className="h-8 w-48 bg-muted animate-pulse rounded" />
                  </div>
                  <div className="space-y-4">
                    <div className="h-64 bg-muted animate-pulse rounded" />
                    <div className="h-32 bg-muted animate-pulse rounded" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  if (!order) {
    return (
      <SidebarProvider
        style={
          {
            '--sidebar-width': 'calc(var(--spacing) * 52)',
            '--header-height': 'calc(var(--spacing) * 10)',
          } as React.CSSProperties
        }
      >
        <AppSidebar variant="inset" />
        <SidebarInset>
          <SiteHeader />
          <div className="flex flex-1 flex-col">
            <div className="@container/main flex flex-1 flex-col gap-2">
              <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
                <div className="space-y-6 px-4 lg:px-6">
                  <div className="flex items-center gap-4">
                    <Button variant="outline" size="sm" onClick={() => router.back()}>
                      <ArrowLeft className="h-4 w-4 mr-2" />
                      Back
                    </Button>
                    <h1 className="text-2xl font-bold">Order Not Found</h1>
                  </div>
                  <Card>
                    <CardContent className="text-center py-8">
                      <Package className="mx-auto h-12 w-12 mb-4 opacity-50" />
                      <p className="text-muted-foreground">
                        The order you're looking for doesn't exist.
                      </p>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  return (
    <SidebarProvider
      style={
        {
          '--sidebar-width': 'calc(var(--spacing) * 52)',
          '--header-height': 'calc(var(--spacing) * 10)',
        } as React.CSSProperties
      }
    >
      <AppSidebar variant="inset" />
      <SidebarInset>
        <SiteHeader />
        <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-2">
            <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
              <div className="space-y-6 px-4 lg:px-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <Button variant="outline" size="sm" onClick={() => router.back()}>
                      <ArrowLeft className="h-4 w-4 mr-2" />
                      Back
                    </Button>
                    <div>
                      <h1 className="text-3xl font-bold tracking-tight">{order.orderNumber}</h1>
                      <p className="text-muted-foreground">
                        Order from {order.source.charAt(0).toUpperCase() + order.source.slice(1)}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge
                      variant={getStatusVariant(order.status)}
                      className="text-sm font-medium px-3 py-1"
                    >
                      {order.status.replace('_', ' ').toUpperCase()}
                    </Badge>
                    <div className="w-8 h-8 flex items-center justify-center bg-muted rounded-lg">
                      <img
                        src={getPlatformLogo(order.source)}
                        alt={order.source}
                        className="w-6 h-6 object-contain"
                        onError={e => {
                          e.currentTarget.style.display = 'none';
                        }}
                      />
                    </div>
                  </div>
                </div>

                <div className="grid gap-6 lg:grid-cols-3">
                  {/* Order Information */}
                  <div className="lg:col-span-2 space-y-6">
                    {/* Order Items */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Package className="h-5 w-5" />
                          Order Items ({order.orderItems.length})
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          {order.orderItems.map((item, index) => (
                            <div key={item.id || index} className="border rounded-lg p-4">
                              <div className="flex items-start justify-between">
                                <div className="flex-1">
                                  <h4 className="font-medium">{item.title}</h4>
                                  <div className="mt-2 grid grid-cols-2 gap-4 text-sm text-muted-foreground">
                                    <div>
                                      <span className="font-medium">SKU:</span> {item.sku}
                                    </div>
                                    <div>
                                      <span className="font-medium">Quantity:</span> {item.quantity}
                                    </div>
                                    {item.size && (
                                      <div>
                                        <span className="font-medium">Size:</span> {item.size}
                                      </div>
                                    )}
                                    {item.color && (
                                      <div>
                                        <span className="font-medium">Color:</span> {item.color}
                                      </div>
                                    )}
                                    {item.style && (
                                      <div>
                                        <span className="font-medium">Style:</span> {item.style}
                                      </div>
                                    )}
                                    {item.design && (
                                      <div>
                                        <span className="font-medium">Design:</span> {item.design}
                                      </div>
                                    )}
                                  </div>
                                  {(item.isCustom || item.isEngrave) && (
                                    <div className="mt-3 p-3 bg-muted rounded-lg">
                                      <h5 className="font-medium text-sm mb-2">
                                        Customization Details:
                                      </h5>
                                      <div className="space-y-1 text-sm">
                                        {item.isCustom && item.customText && (
                                          <div>
                                            <span className="font-medium">Custom Text:</span>{' '}
                                            {item.customText}
                                          </div>
                                        )}
                                        {item.isEngrave && item.engraveText && (
                                          <div>
                                            <span className="font-medium">Engraving:</span>{' '}
                                            {item.engraveText}
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  )}
                                </div>
                                <div className="text-right ml-4">
                                  <p className="font-medium">${item.totalPrice}</p>
                                  <p className="text-sm text-muted-foreground">
                                    ${item.unitPrice} each
                                  </p>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>

                    {/* Order Timeline */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Calendar className="h-5 w-5" />
                          Order Timeline
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="flex items-center gap-3">
                            <div className="w-2 h-2 bg-primary rounded-full" />
                            <div>
                              <p className="font-medium">Order Created</p>
                              <p className="text-sm text-muted-foreground">
                                {new Date(order.externalCreatedAt).toLocaleString()}
                              </p>
                            </div>
                          </div>
                          <div className="flex items-center gap-3">
                            <div className="w-2 h-2 bg-muted rounded-full" />
                            <div>
                              <p className="font-medium">Order Updated</p>
                              <p className="text-sm text-muted-foreground">
                                {new Date(order.updatedAt).toLocaleString()}
                              </p>
                            </div>
                          </div>
                          {order.isProcessed && (
                            <div className="flex items-center gap-3">
                              <div className="w-2 h-2 bg-green-500 rounded-full" />
                              <div>
                                <p className="font-medium">Order Processed</p>
                                <p className="text-sm text-muted-foreground">
                                  Order has been processed
                                </p>
                              </div>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Order Summary */}
                  <div className="space-y-6">
                    {/* Customer Information */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <User className="h-5 w-5" />
                          Customer Information
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div>
                          <p className="font-medium">{order.customerName}</p>
                          <p className="text-sm text-muted-foreground">{order.customerEmail}</p>
                          {order.customerPhone && (
                            <p className="text-sm text-muted-foreground">{order.customerPhone}</p>
                          )}
                        </div>
                      </CardContent>
                    </Card>

                    {/* Shipping Address */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <MapPin className="h-5 w-5" />
                          Shipping Address
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <pre className="text-sm whitespace-pre-line font-sans">
                          {formatAddress(order.shippingAddress)}
                        </pre>
                      </CardContent>
                    </Card>

                    {/* Order Summary */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <ShoppingCart className="h-5 w-5" />
                          Order Summary
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div className="flex justify-between">
                          <span>Subtotal</span>
                          <span>${order.subtotalPrice}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Shipping</span>
                          <span>${order.shippingPrice}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Tax</span>
                          <span>${order.taxAmount}</span>
                        </div>
                        {parseFloat(order.discountAmount) > 0 && (
                          <div className="flex justify-between text-green-600">
                            <span>Discount</span>
                            <span>-${order.discountAmount}</span>
                          </div>
                        )}
                        <Separator />
                        <div className="flex justify-between font-bold text-lg">
                          <span>Total</span>
                          <span>
                            ${order.totalPrice} {order.currency}
                          </span>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Order Details */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <CreditCard className="h-5 w-5" />
                          Order Details
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Order ID</span>
                          <span className="font-mono text-sm">{order.externalOrderId}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Platform</span>
                          <span className="capitalize">{order.source}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Status</span>
                          <Badge variant={getStatusVariant(order.status)}>
                            {order.status.replace('_', ' ').toUpperCase()}
                          </Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Processed</span>
                          <span>{order.isProcessed ? 'Yes' : 'No'}</span>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Customer Note */}
                    {order.customerNote && (
                      <Card>
                        <CardHeader>
                          <CardTitle>Customer Note</CardTitle>
                        </CardHeader>
                        <CardContent>
                          <p className="text-sm">{order.customerNote}</p>
                        </CardContent>
                      </Card>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
