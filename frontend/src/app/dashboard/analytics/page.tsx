import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  ShoppingCart,
  Users,
  Package,
  Calendar,
  Target,
} from 'lucide-react';

export default function AnalyticsPage() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Analytics</h2>
        <p className="text-muted-foreground">
          Track your business performance and key metrics across all platforms.
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">$45,231.89</div>
            <p className="text-xs text-muted-foreground flex items-center gap-1">
              <TrendingUp className="h-3 w-3 text-green-600" />
              +20.1% from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Orders</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">+2,350</div>
            <p className="text-xs text-muted-foreground flex items-center gap-1">
              <TrendingUp className="h-3 w-3 text-green-600" />
              +180.1% from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Customers</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">+12,234</div>
            <p className="text-xs text-muted-foreground flex items-center gap-1">
              <TrendingUp className="h-3 w-3 text-green-600" />
              +19% from last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Products Sold</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">+573</div>
            <p className="text-xs text-muted-foreground flex items-center gap-1">
              <TrendingDown className="h-3 w-3 text-red-600" />
              -4% from last month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Platform Performance */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <div className="w-6 h-6 bg-green-100 rounded-lg flex items-center justify-center">
                <span className="text-green-600 font-bold text-xs">S</span>
              </div>
              Shopify Performance
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between">
              <span>Revenue</span>
              <span className="font-medium">$28,456</span>
            </div>
            <div className="flex items-center justify-between">
              <span>Orders</span>
              <span className="font-medium">1,234</span>
            </div>
            <div className="flex items-center justify-between">
              <span>Conversion</span>
              <span className="font-medium">3.2%</span>
            </div>
            <div className="flex items-center justify-between">
              <span>Growth</span>
              <Badge variant="secondary" className="bg-green-100 text-green-700">
                +15.3%
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <div className="w-6 h-6 bg-orange-100 rounded-lg flex items-center justify-center">
                <span className="text-orange-600 font-bold text-xs">E</span>
              </div>
              Etsy Performance
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between">
              <span>Revenue</span>
              <span className="font-medium">$12,789</span>
            </div>
            <div className="flex items-center justify-between">
              <span>Orders</span>
              <span className="font-medium">567</span>
            </div>
            <div className="flex items-center justify-between">
              <span>Conversion</span>
              <span className="font-medium">2.8%</span>
            </div>
            <div className="flex items-center justify-between">
              <span>Growth</span>
              <Badge variant="secondary" className="bg-green-100 text-green-700">
                +8.7%
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <div className="w-6 h-6 bg-blue-100 rounded-lg flex items-center justify-center">
                <span className="text-blue-600 font-bold text-xs">A</span>
              </div>
              Amazon Performance
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center justify-between">
              <span>Revenue</span>
              <span className="font-medium">$3,986</span>
            </div>
            <div className="flex items-center justify-between">
              <span>Orders</span>
              <span className="font-medium">89</span>
            </div>
            <div className="flex items-center justify-between">
              <span>Conversion</span>
              <span className="font-medium">1.5%</span>
            </div>
            <div className="flex items-center justify-between">
              <span>Growth</span>
              <Badge variant="outline">New</Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Time-based Analytics */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Revenue Trends</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm">This Week</span>
                <div className="flex items-center gap-2">
                  <span className="font-medium">$8,234</span>
                  <Badge variant="secondary" className="bg-green-100 text-green-700">
                    +12%
                  </Badge>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">This Month</span>
                <div className="flex items-center gap-2">
                  <span className="font-medium">$45,231</span>
                  <Badge variant="secondary" className="bg-green-100 text-green-700">
                    +20%
                  </Badge>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">This Quarter</span>
                <div className="flex items-center gap-2">
                  <span className="font-medium">$128,456</span>
                  <Badge variant="secondary" className="bg-green-100 text-green-700">
                    +18%
                  </Badge>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">This Year</span>
                <div className="flex items-center gap-2">
                  <span className="font-medium">$456,789</span>
                  <Badge variant="secondary" className="bg-green-100 text-green-700">
                    +25%
                  </Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Top Performing Products</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Ring #42 - Classic Band</p>
                  <p className="text-sm text-muted-foreground">Category: Rings</p>
                </div>
                <div className="text-right">
                  <p className="font-medium">$2,456</p>
                  <p className="text-sm text-muted-foreground">23 sales</p>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Necklace #18 - Pendant</p>
                  <p className="text-sm text-muted-foreground">Category: Necklaces</p>
                </div>
                <div className="text-right">
                  <p className="font-medium">$1,890</p>
                  <p className="text-sm text-muted-foreground">18 sales</p>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">Earrings #7 - Studs</p>
                  <p className="text-sm text-muted-foreground">Category: Earrings</p>
                </div>
                <div className="text-right">
                  <p className="font-medium">$1,234</p>
                  <p className="text-sm text-muted-foreground">15 sales</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Goals & Targets */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Monthly Goals & Progress
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Revenue Target</span>
                <span className="text-sm text-muted-foreground">$50,000</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-green-600 h-2 rounded-full" style={{ width: '90%' }}></div>
              </div>
              <p className="text-xs text-muted-foreground">90% complete</p>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Orders Target</span>
                <span className="text-sm text-muted-foreground">2,500</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-blue-600 h-2 rounded-full" style={{ width: '94%' }}></div>
              </div>
              <p className="text-xs text-muted-foreground">94% complete</p>
            </div>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Customer Target</span>
                <span className="text-sm text-muted-foreground">12,000</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-purple-600 h-2 rounded-full" style={{ width: '102%' }}></div>
              </div>
              <p className="text-xs text-muted-foreground">102% complete</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
