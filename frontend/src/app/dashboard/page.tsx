'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { DateRange } from 'react-day-picker';
import { format, subDays, subMonths, subYears } from 'date-fns';
import { AppSidebar } from '@/components/app-sidebar';
import { ChartAreaInteractive } from '@/components/chart-area-interactive';
import { DataTable } from '@/components/data-table';
import { SectionCards } from '@/components/section-cards';
import { SiteHeader } from '@/components/site-header';
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';
import { PlatformHealthCards } from '../../components/platform-health-cards';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DateRangePicker } from '@/components/ui/date-range-picker';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { toast } from 'sonner';
import {
  ShoppingCart,
  Package,
  Link as LinkIcon,
  BarChart3,
  Users,
  TrendingUp,
  TrendingDown,
  DollarSign,
  CheckCircle,
  XCircle,
  AlertTriangle,
  RefreshCw,
  Settings,
  MoreHorizontal,
  Shield,
  Mail,
  Activity,
  UserPlus,
  ExternalLink,
  Search,
  Filter,
  Timer,
  Calendar,
} from 'lucide-react';

// Types for backend data
interface PlatformStatus {
  platform: string;
  isHealthy: boolean;
  apiStatus: string;
}

interface Order {
  id: string;
  externalOrderId?: string;
  orderNumber: string;
  source: string; // This is the platform
  customerName: string;
  customerEmail?: string;
  totalPrice: number; // This is the total
  status: string;
  currency?: string;
  externalCreatedAt?: string;
  createdAt: string;
  updatedAt: string;
  orderItems?: Array<{ quantity: number }>;
}

interface Product {
  id: string;
  name: string;
  sku: string;
  platform: string;
  price: number;
  stock: number;
  status: string;
}

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  isVerified: boolean;
  lastLogin?: string;
}

export default function Page() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [platformStatuses, setPlatformStatuses] = useState<PlatformStatus[]>([]);
  const [orders, setOrders] = useState<Order[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);

  // Filter states for orders
  const [orderSearch, setOrderSearch] = useState('');
  const [orderStatusFilter, setOrderStatusFilter] = useState('all');
  const [orderPlatformFilter, setOrderPlatformFilter] = useState('all');
  const [dateRange, setDateRange] = useState<DateRange | undefined>();
  const [datePreset, setDatePreset] = useState('all');

  // Pagination states for orders
  const [orderPage, setOrderPage] = useState(1);
  const [orderLimit, setOrderLimit] = useState(20);
  const [orderPagination, setOrderPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  });

  // Fetch platform statuses
  const fetchPlatformStatuses = async () => {
    try {
      const response = await fetch('/api/platform/status');
      if (!response.ok) throw new Error('Failed to fetch platform status');
      const data = await response.json();

      // Handle backend response structure: { success: true, data: [...] }
      const statuses = data?.data || [];
      setPlatformStatuses(statuses);
    } catch (error) {
      console.error('Error fetching platform status:', error);
      toast.error('Failed to fetch platform status');
    }
  };

  // Fetch orders
  const fetchOrders = async (page = orderPage, limit = orderLimit) => {
    try {
      setLoading(true);
      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(orderStatusFilter !== 'all' && { status: orderStatusFilter }),
        ...(orderPlatformFilter !== 'all' && { source: orderPlatformFilter }),
        ...(orderSearch && { search: orderSearch }),
        ...(dateRange?.from && { startDate: dateRange.from.toISOString() }),
        ...(dateRange?.to && { endDate: dateRange.to.toISOString() }),
      });

      const response = await fetch(`/api/orders?${queryParams}`);
      if (!response.ok) {
        if (response.status === 401) {
          toast.error('Authentication required. Please log in again.');
          return;
        }
        throw new Error(`Failed to fetch orders: ${response.status}`);
      }
      const data = await response.json();

      // Handle backend response structure: { success: true, data: { orders: [...], pagination: {...} } }
      const ordersData = data?.data?.orders || [];
      const paginationData = data?.data?.pagination || {};

      setOrders(ordersData);
      setOrderPagination(paginationData);
    } catch (error) {
      console.error('Error fetching orders:', error);
      toast.error('Failed to fetch orders');
    } finally {
      setLoading(false);
    }
  };

  // Fetch products
  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/products');
      if (!response.ok) throw new Error('Failed to fetch products');
      const data = await response.json();

      // Handle backend response structure: { success: true, data: { products: [...] } }
      const productsData = data?.data?.products || [];
      setProducts(productsData);
    } catch (error) {
      console.error('Error fetching products:', error);
      toast.error('Failed to fetch products');
    } finally {
      setLoading(false);
    }
  };

  // Fetch users
  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/users');
      if (!response.ok) throw new Error('Failed to fetch users');
      const data = await response.json();

      // Handle backend response structure: { success: true, data: { users: [...] } }
      const usersData = data?.data?.users || [];
      setUsers(usersData);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast.error('Failed to fetch users');
    } finally {
      setLoading(false);
    }
  };

  // Sync platform data
  const syncPlatform = async (platform: string) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/platform/sync/${platform}`, {
        method: 'POST',
      });
      if (!response.ok) throw new Error(`Failed to sync ${platform}`);

      toast.success(`${platform} sync started successfully`);

      // Refresh data after sync
      setTimeout(() => {
        fetchOrders();
        fetchProducts();
        fetchPlatformStatuses();
      }, 2000);
    } catch (error) {
      console.error(`Error syncing ${platform}:`, error);
      toast.error(`Failed to sync ${platform}`);
    } finally {
      setLoading(false);
    }
  };

  // Check platform orders
  const checkPlatformOrders = async (platform: string) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/platform/${platform}/orders`);
      if (!response.ok) throw new Error(`Failed to check ${platform} orders`);

      const data = await response.json();
      toast.success(`Found ${data.orders?.length || 0} new orders from ${platform}`);

      // Refresh orders
      fetchOrders();
    } catch (error) {
      console.error(`Error checking ${platform} orders:`, error);
      toast.error(`Failed to check ${platform} orders`);
    } finally {
      setLoading(false);
    }
  };

  // Handle date preset changes
  const handleDatePresetChange = (preset: string) => {
    setDatePreset(preset);
    const today = new Date();

    switch (preset) {
      case 'last7days':
        setDateRange({
          from: subDays(today, 7),
          to: today,
        });
        break;
      case 'last30days':
        setDateRange({
          from: subDays(today, 30),
          to: today,
        });
        break;
      case 'lastmonth':
        setDateRange({
          from: subMonths(today, 1),
          to: today,
        });
        break;
      case 'lastyear':
        setDateRange({
          from: subYears(today, 1),
          to: today,
        });
        break;
      case 'all':
      default:
        setDateRange(undefined);
        break;
    }
  };

  // Check platform products
  const checkPlatformProducts = async (platform: string) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/platform/${platform}/products`);
      if (!response.ok) throw new Error(`Failed to check ${platform} products`);

      const data = await response.json();
      toast.success(`Found ${data.products?.length || 0} products from ${platform}`);

      // Refresh products
      fetchProducts();
    } catch (error) {
      console.error(`Error checking ${platform} products:`, error);
      toast.error(`Failed to check ${platform} products`);
    } finally {
      setLoading(false);
    }
  };

  // Handle tab changes with URL updates
  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    const url = new URL(window.location.href);
    url.searchParams.set('tab', tab);
    router.push(url.pathname + url.search, { scroll: false });
  };

  // Handle pagination changes
  const handlePageChange = (newPage: number) => {
    setOrderPage(newPage);
    fetchOrders(newPage, orderLimit);
  };

  const handleLimitChange = (newLimit: number) => {
    setOrderLimit(newLimit);
    setOrderPage(1); // Reset to first page when changing limit
    fetchOrders(1, newLimit);
  };

  // Get platform logo
  const getPlatformLogo = (platform: string) => {
    const platformLower = platform.toLowerCase();
    if (platformLower === 'shopify') {
      return '/brands/Shopify.com/Shopify.com_Symbol_0.svg';
    } else if (platformLower === 'amazon') {
      return '/brands/Amazon/Amazon_Logo_0.svg';
    } else if (platformLower === 'etsy') {
      return '/brands/Icon.jpeg'; // Using generic icon for Etsy
    }
    return '/brands/Icon.jpeg'; // Default icon
  };

  // Get status color variant
  const getStatusVariant = (status: string) => {
    switch (status.toLowerCase()) {
      case 'delivered':
      case 'completed':
        return 'secondary';
      case 'in_transit':
      case 'shipped':
        return 'default';
      case 'pending':
      case 'processing':
        return 'outline';
      case 'cancelled':
      case 'failed':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  // Get unique platforms and statuses for filters (we'll need to fetch these separately)
  // All available platforms and statuses
  const allPlatforms = ['shopify', 'etsy', 'amazon', 'orders', 'manual'];
  const allStatuses = [
    'pending',
    'confirmed',
    'processing',
    'shipped',
    'delivered',
    'cancelled',
    'refunded',
    'returned',
  ];

  const uniquePlatforms = [...new Set(orders.map(o => o.source))];
  const uniqueStatuses = [...new Set(orders.map(o => o.status))];

  // Load data when tab changes
  useEffect(() => {
    if (activeTab === 'orders') fetchOrders();
    if (activeTab === 'products') fetchProducts();
    if (activeTab === 'users') fetchUsers();
    if (activeTab === 'integrations') fetchPlatformStatuses();
  }, [activeTab]);

  // Refetch orders when filters change
  useEffect(() => {
    if (activeTab === 'orders') {
      setOrderPage(1); // Reset to first page when filters change
      fetchOrders(1, orderLimit);
    }
  }, [orderSearch, orderStatusFilter, orderPlatformFilter, dateRange]);

  // Initialize tab from URL
  useEffect(() => {
    const tabFromUrl = searchParams.get('tab');
    if (
      tabFromUrl &&
      ['dashboard', 'orders', 'products', 'users', 'integrations', 'analytics'].includes(tabFromUrl)
    ) {
      setActiveTab(tabFromUrl);
    }
  }, [searchParams]);

  // Load initial data
  useEffect(() => {
    fetchPlatformStatuses();
  }, []);

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return (
          <>
            <div className="px-4 lg:px-6">
              <PlatformHealthCards />
            </div>
            <SectionCards />
            <div className="px-4 lg:px-6">
              <ChartAreaInteractive />
            </div>
            <DataTable data={[]} />
          </>
        );
      case 'orders':
        return (
          <div className="space-y-6 px-4 lg:px-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-3xl font-bold tracking-tight">Orders</h2>
                <p className="text-muted-foreground">
                  Manage and view all your orders across platforms.
                </p>
              </div>
              <div className="flex gap-2">
                <Button onClick={() => fetchOrders()} disabled={loading}>
                  <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>
                <Button
                  variant="outline"
                  onClick={() => platformStatuses.forEach(p => checkPlatformOrders(p.platform))}
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Check All Platforms
                </Button>
              </div>
            </div>

            {/* Filters */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Filter className="h-5 w-5" />
                  Filters
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Search</label>
                    <div className="relative">
                      <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search orders or customers..."
                        value={orderSearch}
                        onChange={e => setOrderSearch(e.target.value)}
                        className="pl-8"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Status</label>
                    <Select value={orderStatusFilter} onValueChange={setOrderStatusFilter}>
                      <SelectTrigger>
                        <SelectValue placeholder="All Statuses" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Statuses</SelectItem>
                        {allStatuses.map(status => (
                          <SelectItem key={status} value={status}>
                            {status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ')}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Platform</label>
                    <Select value={orderPlatformFilter} onValueChange={setOrderPlatformFilter}>
                      <SelectTrigger>
                        <SelectValue placeholder="All Platforms" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Platforms</SelectItem>
                        {allPlatforms.map(platform => (
                          <SelectItem key={platform} value={platform}>
                            {platform.charAt(0).toUpperCase() + platform.slice(1)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Date Range</label>
                    <Select value={datePreset} onValueChange={handleDatePresetChange}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select date range" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Time</SelectItem>
                        <SelectItem value="last7days">Last 7 Days</SelectItem>
                        <SelectItem value="last30days">Last 30 Days</SelectItem>
                        <SelectItem value="lastmonth">Last Month</SelectItem>
                        <SelectItem value="lastyear">Last Year</SelectItem>
                        <SelectItem value="custom">Custom Range</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Custom Date Range Picker */}
                {datePreset === 'custom' && (
                  <div className="mt-4 pt-4 border-t">
                    <div className="space-y-2">
                      <label className="text-sm font-medium">Custom Date Range</label>
                      <DateRangePicker
                        date={dateRange}
                        onDateChange={setDateRange}
                        className="w-full"
                      />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
                  <ShoppingCart className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{orderPagination.total}</div>
                  <p className="text-xs text-muted-foreground">All orders</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Confirmed</CardTitle>
                  <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {orders.filter(o => o.status === 'confirmed').length}
                  </div>
                  <p className="text-xs text-muted-foreground">Confirmed orders</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Shipped</CardTitle>
                  <Package className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {orders.filter(o => o.status === 'shipped').length}
                  </div>
                  <p className="text-xs text-muted-foreground">In transit</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Delivered</CardTitle>
                  <CheckCircle className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {orders.filter(o => o.status === 'delivered').length}
                  </div>
                  <p className="text-xs text-muted-foreground">Successfully delivered</p>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Orders List</CardTitle>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-muted-foreground">
                      Showing {orders.length} of {orderPagination.total} orders
                    </span>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="text-center py-8">
                    <RefreshCw className="mx-auto h-8 w-8 animate-spin mb-4" />
                    <p>Loading orders...</p>
                  </div>
                ) : orders.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <ShoppingCart className="mx-auto h-12 w-12 mb-4 opacity-50" />
                    <p>No orders found</p>
                    <p className="text-sm">
                      {orderPagination.total === 0
                        ? 'Sync your platforms to see orders'
                        : 'Try adjusting your filters'}
                    </p>
                  </div>
                ) : (
                  <>
                    <div className="space-y-3">
                      {orders.map(order => (
                        <div
                          key={order.id}
                          className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors cursor-pointer"
                          onClick={() => router.push(`/dashboard/orders/${order.id}`)}
                        >
                          <div className="flex items-center gap-4">
                            {/* Platform Logo */}
                            <div className="w-8 h-8 flex items-center justify-center bg-muted rounded-lg">
                              <img
                                src={getPlatformLogo(order.source)}
                                alt={order.source}
                                className="w-6 h-6 object-contain"
                                onError={e => {
                                  e.currentTarget.style.display = 'none';
                                }}
                              />
                            </div>

                            {/* Order Info */}
                            <div className="flex-1">
                              <div className="flex items-center gap-3 ">
                                <p className="font-medium">{order.orderNumber}</p>
                                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                                  <Calendar className="h-3 w-3 text-muted-foreground" />
                                  <span>{new Date(order.updatedAt).toLocaleDateString()}</span>
                                </div>
                              </div>
                              <p className="text-sm text-muted-foreground my-1">
                                {order.customerName}
                              </p>
                              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                <span className="flex items-center gap-1">
                                  <Package className="h-3 w-3" />
                                  {order.orderItems?.length || 0} items
                                </span>
                                <span className="flex items-center gap-1">
                                  <ShoppingCart className="h-3 w-3" />
                                  Qty:{' '}
                                  {order.orderItems?.reduce(
                                    (sum, item) => sum + (item.quantity || 0),
                                    0,
                                  ) || 0}
                                </span>
                              </div>
                            </div>
                          </div>

                          {/* Price and Status */}
                          <div className="text-right">
                            <p className="font-medium text-lg">${order.totalPrice}</p>
                            <div className="flex items-center justify-end gap-2 mt-1">
                              <Badge
                                variant={getStatusVariant(order.status)}
                                className="text-xs font-medium"
                              >
                                {order.status.replace('_', ' ').toUpperCase()}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Pagination Controls */}
                    {orderPagination.totalPages > 1 && (
                      <div className="flex items-center justify-between mt-6 pt-4 border-t">
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-muted-foreground">Items per page:</span>
                          <Select
                            value={orderLimit.toString()}
                            onValueChange={value => handleLimitChange(parseInt(value))}
                          >
                            <SelectTrigger className="w-20">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="10">10</SelectItem>
                              <SelectItem value="20">20</SelectItem>
                              <SelectItem value="50">50</SelectItem>
                              <SelectItem value="100">100</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePageChange(orderPage - 1)}
                            disabled={!orderPagination.hasPrev || loading}
                          >
                            Previous
                          </Button>

                          <div className="flex items-center gap-1">
                            {Array.from(
                              { length: Math.min(5, orderPagination.totalPages) },
                              (_, i) => {
                                const pageNum =
                                  Math.max(
                                    1,
                                    Math.min(orderPagination.totalPages - 4, orderPage - 2),
                                  ) + i;
                                return (
                                  <Button
                                    key={pageNum}
                                    variant={pageNum === orderPage ? 'default' : 'outline'}
                                    size="sm"
                                    onClick={() => handlePageChange(pageNum)}
                                    disabled={loading}
                                    className="w-8 h-8 p-0"
                                  >
                                    {pageNum}
                                  </Button>
                                );
                              },
                            )}
                          </div>

                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePageChange(orderPage + 1)}
                            disabled={!orderPagination.hasNext || loading}
                          >
                            Next
                          </Button>
                        </div>

                        <div className="text-sm text-muted-foreground">
                          Page {orderPage} of {orderPagination.totalPages}
                        </div>
                      </div>
                    )}
                  </>
                )}
              </CardContent>
            </Card>
          </div>
        );
      case 'products':
        return (
          <div className="space-y-6 px-4 lg:px-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-3xl font-bold tracking-tight">Products</h2>
                <p className="text-muted-foreground">
                  Manage your product catalog across all platforms.
                </p>
              </div>
              <div className="flex gap-2">
                <Button onClick={fetchProducts} disabled={loading}>
                  <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>
                <Button
                  variant="outline"
                  onClick={() => platformStatuses.forEach(p => checkPlatformProducts(p.platform))}
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Check All Platforms
                </Button>
              </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Products</CardTitle>
                  <Package className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{products.length}</div>
                  <p className="text-xs text-muted-foreground">Across all platforms</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Active Products</CardTitle>
                  <CheckCircle className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {products.filter(p => p.status === 'active').length}
                  </div>
                  <p className="text-xs text-muted-foreground">Available for sale</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Low Stock</CardTitle>
                  <AlertTriangle className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {products.filter(p => p.stock < 10).length}
                  </div>
                  <p className="text-xs text-muted-foreground">Need restocking</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Platforms</CardTitle>
                  <LinkIcon className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {new Set(products.map(p => p.platform)).size}
                  </div>
                  <p className="text-xs text-muted-foreground">Connected platforms</p>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Product Catalog</CardTitle>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="text-center py-8">
                    <RefreshCw className="mx-auto h-8 w-8 animate-spin mb-4" />
                    <p>Loading products...</p>
                  </div>
                ) : products.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Package className="mx-auto h-12 w-12 mb-4 opacity-50" />
                    <p>No products found</p>
                    <p className="text-sm">Sync your platforms to see products</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {products.slice(0, 10).map(product => (
                      <div
                        key={product.id}
                        className="flex items-center justify-between p-3 border rounded-lg"
                      >
                        <div>
                          <p className="font-medium">{product.name}</p>
                          <p className="text-sm text-muted-foreground">SKU: {product.sku}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">${product.price}</p>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="text-xs">
                              {product.platform}
                            </Badge>
                            <Badge
                              variant={product.stock < 10 ? 'destructive' : 'secondary'}
                              className="text-xs"
                            >
                              Stock: {product.stock}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        );
      case 'integrations':
        return (
          <div className="space-y-6 px-4 lg:px-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-3xl font-bold tracking-tight">Integrations</h2>
                <p className="text-muted-foreground">
                  Manage your platform connections and API integrations.
                </p>
              </div>
              <Button onClick={fetchPlatformStatuses} disabled={loading}>
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh Status
              </Button>
            </div>

            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {platformStatuses.map(status => (
                <Card key={status.platform} className="hover:shadow-md transition-shadow">
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <CardTitle className="flex items-center gap-2">
                        <div
                          className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                            status.platform === 'shopify'
                              ? 'bg-green-100'
                              : status.platform === 'etsy'
                                ? 'bg-orange-100'
                                : 'bg-blue-100'
                          }`}
                        >
                          <span
                            className={`font-bold text-sm ${
                              status.platform === 'shopify'
                                ? 'text-green-600'
                                : status.platform === 'etsy'
                                  ? 'text-orange-600'
                                  : 'text-blue-600'
                            }`}
                          >
                            {status.platform.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        {status.platform.charAt(0).toUpperCase() + status.platform.slice(1)}
                      </CardTitle>
                      <Badge
                        variant={status.isHealthy ? 'secondary' : 'destructive'}
                        className={status.isHealthy ? 'bg-green-100 text-green-700' : ''}
                      >
                        {status.isHealthy ? 'Connected' : 'Disconnected'}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span>Status:</span>
                        <span
                          className={`flex items-center gap-1 ${
                            status.isHealthy ? 'text-green-600' : 'text-red-600'
                          }`}
                        >
                          {status.isHealthy ? (
                            <CheckCircle className="h-4 w-4" />
                          ) : (
                            <XCircle className="h-4 w-4" />
                          )}
                          {status.isHealthy ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span>API:</span>
                        <span className="font-medium capitalize">{status.apiStatus}</span>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        variant="outline"
                        className="flex-1"
                        onClick={() => syncPlatform(status.platform)}
                        disabled={loading}
                      >
                        <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                        Sync
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => checkPlatformOrders(status.platform)}
                        disabled={loading}
                      >
                        <ExternalLink className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        );
      case 'analytics':
        return (
          <div className="space-y-6 px-4 lg:px-6">
            <div>
              <h2 className="text-3xl font-bold tracking-tight">Analytics</h2>
              <p className="text-muted-foreground">
                Track your business performance and key metrics across all platforms.
              </p>
            </div>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    ${orders.reduce((sum, order) => sum + order.totalPrice, 0).toFixed(2)}
                  </div>
                  <p className="text-xs text-muted-foreground">From all orders</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Orders</CardTitle>
                  <ShoppingCart className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{orderPagination.total}</div>
                  <p className="text-xs text-muted-foreground">Across all platforms</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Products</CardTitle>
                  <Package className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{products.length}</div>
                  <p className="text-xs text-muted-foreground">In catalog</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Platforms</CardTitle>
                  <LinkIcon className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{platformStatuses.length}</div>
                  <p className="text-xs text-muted-foreground">Connected</p>
                </CardContent>
              </Card>
            </div>
          </div>
        );
      case 'users':
        return (
          <div className="space-y-6 px-4 lg:px-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-3xl font-bold tracking-tight">Users</h2>
                <p className="text-muted-foreground">
                  Manage user accounts, roles, and permissions.
                </p>
              </div>
              <div className="flex gap-2">
                <Button onClick={fetchUsers} disabled={loading}>
                  <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>
                <Button>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Add User
                </Button>
              </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{users.length}</div>
                  <p className="text-xs text-muted-foreground">Registered accounts</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Admins</CardTitle>
                  <Shield className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {users.filter(u => u.role === 'admin').length}
                  </div>
                  <p className="text-xs text-muted-foreground">Full system access</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Verified</CardTitle>
                  <Mail className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{users.filter(u => u.isVerified).length}</div>
                  <p className="text-xs text-muted-foreground">Email verified</p>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Regular Users</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {users.filter(u => u.role === 'user').length}
                  </div>
                  <p className="text-xs text-muted-foreground">Standard access</p>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>User Management</CardTitle>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="text-center py-8">
                    <RefreshCw className="mx-auto h-8 w-8 animate-spin mb-4" />
                    <p>Loading users...</p>
                  </div>
                ) : users.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Users className="mx-auto h-12 w-12 mb-4 opacity-50" />
                    <p>No users found</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {users.map(user => (
                      <div
                        key={user.id}
                        className="flex items-center justify-between p-4 border rounded-lg"
                      >
                        <div className="flex items-center gap-4">
                          <Avatar>
                            <AvatarFallback>
                              {user.name
                                .split(' ')
                                .map(n => n[0])
                                .join('')
                                .toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium">{user.name}</p>
                            <p className="text-sm text-muted-foreground">{user.email}</p>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge variant={user.role === 'admin' ? 'secondary' : 'outline'}>
                                {user.role}
                              </Badge>
                              <Badge variant={user.isVerified ? 'secondary' : 'outline'}>
                                {user.isVerified ? 'Verified' : 'Pending'}
                              </Badge>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm">Last login</p>
                          <p className="text-xs text-muted-foreground">
                            {user.lastLogin
                              ? new Date(user.lastLogin).toLocaleDateString()
                              : 'Never'}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        );
      default:
        return (
          <>
            <div className="px-4 lg:px-6">
              <PlatformHealthCards />
            </div>
            <SectionCards />
            <div className="px-4 lg:px-6">
              <ChartAreaInteractive />
            </div>
            <DataTable data={[]} />
          </>
        );
    }
  };

  return (
    <SidebarProvider
      style={
        {
          '--sidebar-width': 'calc(var(--spacing) * 52)',
          '--header-height': 'calc(var(--spacing) * 10)',
        } as React.CSSProperties
      }
    >
      <AppSidebar variant="inset" onTabChange={handleTabChange} activeTab={activeTab} />
      <SidebarInset>
        <SiteHeader />
        <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-2">
            <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">{renderContent()}</div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
