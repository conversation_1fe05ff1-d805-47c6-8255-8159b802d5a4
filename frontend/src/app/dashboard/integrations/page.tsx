import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Link as LinkIcon, 
  CheckCircle, 
  XCircle, 
  Alert<PERSON>riangle,
  <PERSON>fresh<PERSON><PERSON>,
  Settings
} from 'lucide-react';

export default function IntegrationsPage() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Integrations</h2>
        <p className="text-muted-foreground">
          Manage your platform connections and API integrations.
        </p>
      </div>

      {/* Integration Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {/* Shopify */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                  <span className="text-green-600 font-bold text-sm">S</span>
                </div>
                Shopify
              </CardTitle>
              <Badge variant="secondary" className="bg-green-100 text-green-700">
                Connected
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Status:</span>
                <span className="flex items-center gap-1 text-green-600">
                  <CheckCircle className="h-4 w-4" />
                  Active
                </span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span>Last Sync:</span>
                <span>2 hours ago</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span>Products:</span>
                <span>245</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span>Orders:</span>
                <span>1,234</span>
              </div>
            </div>
            <div className="flex gap-2">
              <Button size="sm" variant="outline" className="flex-1">
                <RefreshCw className="h-4 w-4 mr-2" />
                Sync Now
              </Button>
              <Button size="sm" variant="outline">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Etsy */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                  <span className="text-orange-600 font-bold text-sm">E</span>
                </div>
                Etsy
              </CardTitle>
              <Badge variant="secondary" className="bg-green-100 text-green-700">
                Connected
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Status:</span>
                <span className="flex items-center gap-1 text-green-600">
                  <CheckCircle className="h-4 w-4" />
                  Active
                </span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span>Last Sync:</span>
                <span>1 hour ago</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span>Products:</span>
                <span>198</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span>Orders:</span>
                <span>567</span>
              </div>
            </div>
            <div className="flex gap-2">
              <Button size="sm" variant="outline" className="flex-1">
                <RefreshCw className="h-4 w-4 mr-2" />
                Sync Now
              </Button>
              <Button size="sm" variant="outline">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Amazon */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <span className="text-blue-600 font-bold text-sm">A</span>
                </div>
                Amazon
              </CardTitle>
              <Badge variant="secondary" className="bg-red-100 text-red-700">
                Disconnected
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Status:</span>
                <span className="flex items-center gap-1 text-red-600">
                  <XCircle className="h-4 w-4" />
                  Inactive
                </span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span>Last Sync:</span>
                <span>Never</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span>Products:</span>
                <span>0</span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span>Orders:</span>
                <span>0</span>
              </div>
            </div>
            <Button size="sm" className="w-full">
              <LinkIcon className="h-4 w-4 mr-2" />
              Connect
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Connection Management */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>API Keys & Tokens</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="font-medium">Shopify API Key</p>
                  <p className="text-sm text-muted-foreground">Expires: Never</p>
                </div>
                <Badge variant="secondary">Valid</Badge>
              </div>
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="font-medium">Etsy Access Token</p>
                  <p className="text-sm text-muted-foreground">Expires: 2 days</p>
                </div>
                <Badge variant="secondary">Valid</Badge>
              </div>
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="font-medium">Amazon MWS</p>
                  <p className="text-sm text-muted-foreground">Not configured</p>
                </div>
                <Badge variant="outline">Missing</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Sync Settings</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span>Auto-sync enabled</span>
                <Badge variant="secondary">Yes</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span>Sync frequency</span>
                <span className="text-sm">Every 30 minutes</span>
              </div>
              <div className="flex items-center justify-between">
                <span>Last full sync</span>
                <span className="text-sm">Yesterday</span>
              </div>
              <div className="flex items-center justify-between">
                <span>Next sync</span>
                <span className="text-sm">In 15 minutes</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Webhook Status */}
      <Card>
        <CardHeader>
          <CardTitle>Webhook Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>Shopify Order Updates</span>
              </div>
              <Badge variant="secondary">Active</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>Etsy Order Notifications</span>
              </div>
              <Badge variant="secondary">Active</Badge>
            </div>
            <div className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center gap-2">
                <XCircle className="h-4 w-4 text-red-600" />
                <span>Amazon Order Updates</span>
              </div>
              <Badge variant="outline">Inactive</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
