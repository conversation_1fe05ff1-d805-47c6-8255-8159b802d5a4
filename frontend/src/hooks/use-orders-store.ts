import { create } from 'zustand';

type SortOrder = 'asc' | 'desc';

type OrdersState = {
  page: number;
  limit: number;
  sortBy: string;
  sortOrder: SortOrder;
  setPage: (page: number) => void;
  setLimit: (limit: number) => void;
  setSort: (by: string, order: SortOrder) => void;
};

export const useOrdersStore = create<OrdersState>()(set => ({
  page: 1,
  limit: 20,
  sortBy: 'externalCreatedAt',
  sortOrder: 'desc',
  setPage: (page: number) => set({ page }),
  setLimit: (limit: number) => set({ limit, page: 1 }),
  setSort: (by: string, order: SortOrder) => set({ sortBy: by, sortOrder: order, page: 1 }),
}));
