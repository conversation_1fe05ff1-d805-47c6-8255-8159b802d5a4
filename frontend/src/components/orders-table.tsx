'use client';

import { useMemo } from 'react';
import { useOrdersStore } from '@/hooks/use-orders-store';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

type OrderItem = {
  id: string;
  sku: string;
  title: string;
  quantity: number;
  unitPrice: string;
  totalPrice: string;
  currency: string;
};

type Order = {
  id: string;
  externalOrderId: string;
  orderNumber: string;
  status: string;
  source: string;
  customerName: string;
  customerEmail: string;
  totalPrice: string;
  currency: string;
  externalCreatedAt: string;
  orderItems: OrderItem[];
};

type BackendOrdersResponse =
  | {
      success: boolean;
      data: {
        orders: Order[];
        pagination: { page: number; limit: number; total: number; totalPages: number };
      };
    }
  | {
      orders: Order[];
    };

function useOrdersQuery(qs: string) {
  return useQuery({
    queryKey: ['orders', qs],
    queryFn: async () => {
      const res = await fetch(`/api/orders?${qs}`, { cache: 'no-store', credentials: 'include' });
      if (!res.ok) throw new Error(`Failed: ${res.status}`);
      const payload = await res.json();
      const orders = (payload?.data?.orders ?? []) as Order[];
      const total = Number(payload?.data?.pagination?.total ?? orders.length ?? 0);
      return { orders, total } as { orders: Order[]; total: number };
    },
    placeholderData: previousData => previousData,
  });
}

export function OrdersTable() {
  const { page, limit, sortBy, sortOrder, setPage, setLimit } = useOrdersStore();

  const qs = useMemo(() => {
    const p = new URLSearchParams();
    p.set('page', String(page));
    p.set('limit', String(limit));
    p.set('sortBy', sortBy);
    p.set('sortOrder', sortOrder);
    return p.toString();
  }, [page, limit, sortBy, sortOrder]);

  const { data, isLoading } = useOrdersQuery(qs);
  const orders = data?.orders ?? [];
  const total = data?.total ?? 0;

  const totalPages = Math.max(1, Math.ceil(total / limit));

  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Orders</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between gap-3 pb-3">
          <div className="text-sm text-muted-foreground">Total: {total.toLocaleString()}</div>
          <div className="flex items-center gap-2">
            <Select value={String(limit)} onValueChange={v => setLimit(Number(v))}>
              <SelectTrigger className="w-24">
                <SelectValue placeholder={limit} />
              </SelectTrigger>
              <SelectContent>
                {['10', '20', '50', '100'].map(v => (
                  <SelectItem key={v} value={v}>
                    {v} / page
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <div className="text-sm">
              Page {page} / {totalPages}
            </div>
            <Button variant="outline" size="sm" onClick={() => setPage(1)} disabled={page === 1}>
              First
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(Math.max(1, page - 1))}
              disabled={page === 1}
            >
              Prev
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(Math.min(totalPages, page + 1))}
              disabled={page >= totalPages}
            >
              Next
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(totalPages)}
              disabled={page >= totalPages}
            >
              Last
            </Button>
          </div>
        </div>

        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Order #</TableHead>
                <TableHead>Platform</TableHead>
                <TableHead>Customer</TableHead>
                <TableHead className="text-right">Total</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Date</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={6}>Loading…</TableCell>
                </TableRow>
              ) : orders.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6}>No orders</TableCell>
                </TableRow>
              ) : (
                orders.map(o => (
                  <TableRow key={o.id}>
                    <TableCell>{o.orderNumber || o.externalOrderId}</TableCell>
                    <TableCell className="capitalize">{o.source}</TableCell>
                    <TableCell>{o.customerName || o.customerEmail || '—'}</TableCell>
                    <TableCell className="text-right">
                      {Number(o.totalPrice).toFixed(2)} {o.currency}
                    </TableCell>
                    <TableCell className="capitalize">{o.status}</TableCell>
                    <TableCell>{new Date(o.externalCreatedAt).toLocaleString()}</TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
