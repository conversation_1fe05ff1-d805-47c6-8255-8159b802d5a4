"use client"

import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { CheckCircle, XCircle, AlertTriangle, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'

type PlatformStatus = {
  platform: string
  isHealthy: boolean
  apiStatus: string
}

export function PlatformHealthCards() {
  const [statuses, setStatuses] = useState<PlatformStatus[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchPlatformStatus = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/platform/status')
      if (!response.ok) {
        throw new Error('Failed to fetch platform status')
      }
      const data = await response.json()
      // Handle nested data structure from backend
      const statuses = data?.data?.data ?? data?.data ?? data
      setStatuses(statuses)
      setError(null)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch status')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPlatformStatus()
  }, [])

  const getPlatformIcon = (platform: string) => {
    const icons = {
      shopify: '🛍️',
      etsy: '🏪',
      amazon: '📦'
    }
    return icons[platform as keyof typeof icons] || '🔗'
  }

  const getStatusIcon = (isHealthy: boolean) => {
    if (isHealthy) {
      return <CheckCircle className="h-5 w-5 text-green-600" />
    }
    return <XCircle className="h-5 w-5 text-red-600" />
  }

  const getStatusBadge = (isHealthy: boolean, apiStatus: string) => {
    if (isHealthy && apiStatus === 'connected') {
      return <Badge className="bg-green-100 text-green-700">Connected</Badge>
    } else if (apiStatus === 'disconnected') {
      return <Badge variant="destructive">Disconnected</Badge>
    } else {
      return <Badge variant="outline">Unknown</Badge>
    }
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold tracking-tight">Platform Health</h2>
          <Button variant="outline" size="sm" disabled>
            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            Loading...
          </Button>
        </div>
        <div className="grid gap-4 md:grid-cols-3">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-full"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold tracking-tight">Platform Health</h2>
          <Button variant="outline" size="sm" onClick={fetchPlatformStatus}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-red-600">
              <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
              <p>Error loading platform status: {error}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Platform Health</h2>
          <p className="text-muted-foreground">
            Monitor the connection status of your e-commerce platforms
          </p>
        </div>
        <Button variant="outline" size="sm" onClick={fetchPlatformStatus}>
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>
      
      <div className="grid gap-4 md:grid-cols-3">
        {statuses.map((status) => (
          <Card key={status.platform} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-lg font-semibold flex items-center gap-2">
                <span className="text-2xl">{getPlatformIcon(status.platform)}</span>
                {status.platform.charAt(0).toUpperCase() + status.platform.slice(1)}
              </CardTitle>
              {getStatusIcon(status.isHealthy)}
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Status:</span>
                  {getStatusBadge(status.isHealthy, status.apiStatus)}
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">API:</span>
                  <span className="text-sm font-medium capitalize">
                    {status.apiStatus}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Health:</span>
                  <span className={`text-sm font-medium ${
                    status.isHealthy ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {status.isHealthy ? 'Healthy' : 'Unhealthy'}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
