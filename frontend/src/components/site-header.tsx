import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { SidebarTrigger } from '@/components/ui/sidebar';
import { Input } from '@/components/ui/input';
import { Search, Package } from 'lucide-react';
import { UserNav } from './user-nav';

export function SiteHeader() {
  return (
    <header className="flex  py-3 shrink-0 items-center gap-2 border-b transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-(--header-height)">
      <div className="flex w-full items-center gap-1 px-4 lg:gap-2 lg:px-6">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mx-2 data-[orientation=vertical]:h-4" />
        <div className="flex items-center gap-2">
          <Package className="h-5 w-5" />
          <h1 className="text-base font-medium">Knot Core Dashboard</h1>
        </div>

        <div className="flex-1 max-w-md mx-4">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input placeholder="Search orders, products..." className="pl-8" />
          </div>
        </div>

        <div className="ml-auto flex items-center gap-2">
          <UserNav />
        </div>
      </div>
    </header>
  );
}
