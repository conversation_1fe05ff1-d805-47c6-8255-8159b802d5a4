'use client';

import * as React from 'react';
import {
  IconDashboard,
  IconShoppingCart,
  IconPackage,
  IconLink,
  IconChartBar,
  IconUsers,
  IconSettings,
  IconHelp,
  IconSearch,
} from '@tabler/icons-react';

import { NavDocuments } from '@/components/nav-documents';
import { NavMain } from '@/components/nav-main';
import { NavSecondary } from '@/components/nav-secondary';
import { NavUser } from '@/components/nav-user';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '@/components/ui/sidebar';

const data = {
  user: {
    name: 'Admin User',
    email: '<EMAIL>',
    avatar: '',
  },
  navMain: [
    {
      title: 'Dashboard',
      id: 'dashboard',
      icon: IconDashboard,
    },
    {
      title: 'Orders',
      id: 'orders',
      icon: IconShoppingCart,
    },
    {
      title: 'Products',
      id: 'products',
      icon: IconPackage,
    },
    {
      title: 'Integrations',
      id: 'integrations',
      icon: IconLink,
    },
    {
      title: 'Analytics',
      id: 'analytics',
      icon: IconChartBar,
    },
    {
      title: 'Users',
      id: 'users',
      icon: IconUsers,
    },
  ],
  navClouds: [
    {
      title: 'Platform Management',
      icon: IconLink,
      isActive: true,
      url: '#',
      items: [
        {
          title: 'Shopify',
          url: '#',
        },
        {
          title: 'Etsy',
          url: '#',
        },
        {
          title: 'Amazon',
          url: '#',
        },
      ],
    },
  ],
  navSecondary: [
    {
      title: 'Settings',
      url: '#',
      icon: IconSettings,
    },
    {
      title: 'Get Help',
      url: '#',
      icon: IconHelp,
    },
    {
      title: 'Search',
      url: '#',
      icon: IconSearch,
    },
  ],
  documents: [
    {
      name: 'API Documentation',
      url: '#',
      icon: IconLink,
    },
    {
      name: 'Reports',
      url: '#',
      icon: IconChartBar,
    },
    {
      name: 'Webhooks',
      url: '#',
      icon: IconLink,
    },
  ],
};

interface AppSidebarProps extends React.ComponentProps<typeof Sidebar> {
  onTabChange?: (tabId: string) => void;
  activeTab?: string;
}

export function AppSidebar({ onTabChange, activeTab, ...props }: AppSidebarProps) {
  return (
    <Sidebar collapsible="offcanvas" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild className="data-[slot=sidebar-menu-button]:!p-2">
              <button onClick={() => onTabChange?.('dashboard')} className="w-full text-left">
                <IconDashboard className="!size-5" />
                <span className="text-sm font-semibold">Knot Core</span>
              </button>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} onTabChange={onTabChange} activeTab={activeTab} />
        <NavDocuments items={data.documents} />
        <NavSecondary items={data.navSecondary} className="mt-auto" />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
    </Sidebar>
  );
}
