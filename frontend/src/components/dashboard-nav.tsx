'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  LayoutDashboard,
  ShoppingCart,
  Package,
  Link as LinkIcon,
  BarChart3,
  Settings,
  Users,
} from 'lucide-react';

const items = [
  {
    title: 'Dashboard',
    href: '/dashboard',
    icon: LayoutDashboard,
    variant: 'default' as const,
  },
  {
    title: 'Orders',
    href: '/dashboard/orders',
    icon: ShoppingCart,
    variant: 'ghost' as const,
  },
  {
    title: 'Products',
    href: '/dashboard/products',
    icon: Package,
    variant: 'ghost' as const,
  },
  {
    title: 'Integrations',
    href: '/dashboard/integrations',
    icon: LinkIcon,
    variant: 'ghost' as const,
  },
  {
    title: 'Analytics',
    href: '/dashboard/analytics',
    icon: BarChart3,
    variant: 'ghost' as const,
  },
  {
    title: 'Users',
    href: '/dashboard/users',
    icon: Users,
    variant: 'ghost' as const,
  },
  {
    title: 'Settings',
    href: '/dashboard/settings',
    icon: Settings,
    variant: 'ghost' as const,
  },
];

export function DashboardNav() {
  const path = usePathname();

  return (
    <nav className="grid items-start gap-2 px-4">
      {items.map((item, index) => {
        const Icon = item.icon;
        return (
          <Link key={index} href={item.href}>
            <Button
              variant={path === item.href ? 'default' : 'ghost'}
              className={cn('w-full justify-start', path === item.href && 'bg-muted font-medium')}
            >
              <Icon className="mr-2 h-4 w-4" />
              {item.title}
            </Button>
          </Link>
        );
      })}
    </nav>
  );
}
