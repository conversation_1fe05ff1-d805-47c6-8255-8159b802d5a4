export type ApiResponse<T> = {
  success: boolean;
  data: T;
  message?: string;
};

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3002/api/v1';

export function getApiBaseUrl(): string {
  return API_BASE_URL.replace(/\/$/, '');
}

export async function apiFetch<T>(path: string, options: RequestInit = {}): Promise<T> {
  const url = `${getApiBaseUrl()}${path.startsWith('/') ? '' : '/'}${path}`;

  const headers = new Headers(options.headers);
  if (!headers.has('Content-Type')) headers.set('Content-Type', 'application/json');

  const res = await fetch(url, {
    ...options,
    headers,
    credentials: 'include',
    cache: 'no-store',
  });

  if (!res.ok) {
    let payload: any = null;
    try {
      payload = await res.json();
    } catch {}
    const message = payload?.message || `Request failed with status ${res.status}`;
    throw new Error(message);
  }

  return (await res.json()) as T;
}

export function withAuth(token?: string): HeadersInit {
  const headers = new Headers();
  if (token) headers.set('Authorization', `Bearer ${token}`);
  return headers;
}
