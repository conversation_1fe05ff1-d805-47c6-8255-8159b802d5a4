import { cookies } from 'next/headers';
import { apiFetch, withAuth } from './api';

type LoginDto = { email: string; password: string };

export type UserProfile = {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  role?: string;
  isEmailVerified?: boolean;
};

type LoginResponse = {
  success: boolean;
  data: { user: UserProfile; accessToken: string };
};

export async function login(
  email: string,
  password: string,
): Promise<{ token: string; user: UserProfile }> {
  const body: LoginDto = { email, password };
  const res = await apiFetch<LoginResponse>('/auth/login', {
    method: 'POST',
    body: JSON.stringify(body),
  });
  const token = res.data.accessToken;
  const user = res.data.user;
  // store token in httpOnly-ish via route handler; here we fallback to client cookie if needed
  (await cookies()).set('token', token, { httpOnly: false, path: '/' });
  return { token, user };
}

export async function getProfile(token?: string): Promise<UserProfile | null> {
  try {
    const headers = withAuth(token || (await cookies()).get('token')?.value);
    const res = await apiFetch<{ success: boolean; data: UserProfile }>('/auth/profile', {
      headers,
    });
    return res.data;
  } catch {
    return null;
  }
}

export async function getTokenFromCookies(): Promise<string | undefined> {
  return (await cookies()).get('token')?.value;
}

export async function clearAuth(): Promise<void> {
  (await cookies()).set('token', '', { path: '/', maxAge: 0 });
}
